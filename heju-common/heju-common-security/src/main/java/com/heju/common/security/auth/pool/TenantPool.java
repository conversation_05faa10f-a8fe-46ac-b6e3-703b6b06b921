package com.heju.common.security.auth.pool;

/**
 * 租户模块 权限标识常量
 *
 * <AUTHOR>
 */
public interface TenantPool {

    /** 租户 - 租户管理 - 列表 */
    String TE_TENANT_LIST = "tenant:tenant:list";
    /** 租户 - 租户管理 - 详情 */
    String TE_TENANT_SINGLE = "tenant:tenant:single";
    /** 租户 - 租户管理 - 新增 */
    String TE_TENANT_ADD = "tenant:tenant:add";
    /** 租户 - 租户管理 - 修改 */
    String TE_TENANT_EDIT = "tenant:tenant:edit";
    /** 租户 - 租户管理 - 权限 */
    String TE_TENANT_AUTH = "tenant:tenant:auth";
    /** 租户 - 租户管理 - 修改状态 */
    String TE_TENANT_ES = "tenant:tenant:es";
    /** 租户 - 租户管理 - 删除 */
    String TE_TENANT_DEL = "tenant:tenant:delete";
    /** 租户 - 租户管理 - 导入 */
    String TE_TENANT_IMPORT = "tenant:tenant:import";
    /** 租户 - 租户管理 - 导出 */
    String TE_TENANT_EXPORT = "tenant:tenant:export";

    /** 租户 - 数据源策略管理 - 列表 */
    String TE_STRATEGY_LIST = "tenant:strategy:list";
    /** 租户 - 数据源策略管理 - 详情 */
    String TE_STRATEGY_SINGLE = "tenant:strategy:single";
    /** 租户 - 数据源策略管理 - 新增 */
    String TE_STRATEGY_ADD = "tenant:strategy:add";
    /** 租户 - 数据源策略管理 - 修改 */
    String TE_STRATEGY_EDIT = "tenant:strategy:edit";
    /** 租户 - 数据源策略管理 - 修改状态 */
    String TE_STRATEGY_ES = "tenant:strategy:es";
    /** 租户 - 数据源策略管理 - 删除 */
    String TE_STRATEGY_DEL = "tenant:strategy:delete";
    /** 租户 - 数据源策略管理 - 导入 */
    String TE_STRATEGY_IMPORT = "tenant:strategy:import";
    /** 租户 - 数据源策略管理 - 导出 */
    String TE_STRATEGY_EXPORT = "tenant:strategy:export";

    /** 租户 - 数据源管理 - 列表 */
    String TE_SOURCE_LIST = "tenant:source:list";
    /** 租户 - 数据源管理 - 详情 */
    String TE_SOURCE_SINGLE = "tenant:source:single";
    /** 租户 - 数据源管理 - 新增 */
    String TE_SOURCE_ADD = "tenant:source:add";
    /** 租户 - 数据源管理 - 修改 */
    String TE_SOURCE_EDIT = "tenant:source:edit";
    /** 租户 - 数据源管理 - 修改状态 */
    String TE_SOURCE_ES = "tenant:source:es";
    /** 租户 - 数据源管理 - 删除 */
    String TE_SOURCE_DEL = "tenant:source:delete";
    /** 租户 - 数据源管理 - 导入 */
    String TE_SOURCE_IMPORT = "tenant:source:import";
    /** 租户 - 数据源管理 - 导出 */
    String TE_SOURCE_EXPORT = "tenant:source:export";

    /** 系统 - 租户审批管理 - 列表 */
    String TE_TENANT_APPROVAL_LIST = "approval:approval:list";
    /** 系统 - 租户审批管理 - 详情 */
    String TE_TENANT_APPROVAL_SINGLE = "approval:approval:single";
    /** 系统 - 租户审批管理 - 修改状态 */
    String TE_TENANT_APPROVAL_ES = "approval:approval:es";

}
