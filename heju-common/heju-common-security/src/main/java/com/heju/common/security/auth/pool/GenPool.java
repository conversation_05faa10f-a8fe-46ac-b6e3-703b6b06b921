package com.heju.common.security.auth.pool;

/**
 * 代码和聚模块 权限标识常量
 *
 * <AUTHOR>
 */
public interface GenPool {

    /** 代码生成 - 代码生成管理 - 列表 */
    String GENERATE_GEN_LIST = "generate:gen:list";
    /** 代码生成 - 代码生成管理 - 详情 */
    String GENERATE_GEN_SINGLE = "generate:gen:single";
    /** 代码生成 - 代码生成管理 - 新增 */
    String GENERATE_GEN_ADD = "generate:gen:add";
    /** 代码生成 - 代码生成管理 - 修改 */
    String GENERATE_GEN_EDIT = "generate:gen:edit";
    /** 代码生成 - 代码生成管理 - 删除 */
    String GENERATE_GEN_DEL = "generate:gen:delete";
    /** 代码生成 - 代码生成管理 - 导入 */
    String GENERATE_GEN_IMPORT = "generate:gen:import";
    /** 代码生成 - 代码生成管理 - 生成 */
    String GENERATE_GEN_PREVIEW = "generate:gen:preview";
    /** 代码生成 - 代码生成管理 - 下载 */
    String GENERATE_GEN_CODE = "generate:gen:code";
}
