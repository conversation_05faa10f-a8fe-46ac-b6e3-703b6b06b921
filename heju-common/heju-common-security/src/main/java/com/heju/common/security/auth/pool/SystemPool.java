package com.heju.common.security.auth.pool;

/**
 * 系统模块 权限标识常量
 *
 * <AUTHOR>
 */
public interface SystemPool {

    /** 系统 - 部门管理 - 列表 */
    String SYS_DEPT_LIST = "organize:dept:list" ;
    /** 系统 - 部门管理 - 详情 */
    String SYS_DEPT_SINGLE = "organize:dept:single" ;
    /** 系统 - 部门管理 - 新增 */
    String SYS_DEPT_ADD = "organize:dept:add" ;
    /** 系统 - 部门管理 - 修改 */
    String SYS_DEPT_EDIT = "organize:dept:edit" ;
    /** 系统 - 部门管理 - 权限 */
    String SYS_DEPT_AUTH = "organize:dept:auth" ;
    /** 系统 - 部门管理 - 修改状态 */
    String SYS_DEPT_ES = "organize:dept:es" ;
    /** 系统 - 部门管理 - 删除 */
    String SYS_DEPT_DEL = "organize:dept:delete" ;
    /** 系统 - 部门管理 - 导入 */
    String SYS_DEPT_IMPORT = "organize:dept:import" ;
    /** 系统 - 部门管理 - 导出 */
    String SYS_DEPT_EXPORT = "organize:dept:export" ;

    /** 系统 - 岗位管理 - 列表 */
    String SYS_POST_LIST = "organize:post:list" ;
    /** 系统 - 岗位管理 - 详情 */
    String SYS_POST_SINGLE = "organize:post:single" ;
    /** 系统 - 岗位管理 - 新增 */
    String SYS_POST_ADD = "organize:post:add" ;
    /** 系统 - 岗位管理 - 修改 */
    String SYS_POST_EDIT = "organize:post:edit" ;
    /** 系统 - 岗位管理 - 权限 */
    String SYS_POST_AUTH = "organize:post:auth" ;
    /** 系统 - 岗位管理 - 修改状态 */
    String SYS_POST_ES = "organize:post:es" ;
    /** 系统 - 岗位管理 - 删除 */
    String SYS_POST_DEL = "organize:post:delete" ;
    /** 系统 - 岗位管理 - 导入 */
    String SYS_POST_IMPORT = "organize:post:import" ;
    /** 系统 - 岗位管理 - 导出 */
    String SYS_POST_EXPORT = "organize:post:export" ;

    /** 系统 - 用户管理 - 列表 */
    String SYS_USER_LIST = "organize:user:list" ;
    /** 系统 - 用户管理 - 详情 */
    String SYS_USER_SINGLE = "organize:user:single" ;
    /** 系统 - 用户管理 - 新增 */
    String SYS_USER_ADD = "organize:user:add" ;
    /** 系统 - 用户管理 - 修改 */
    String SYS_USER_EDIT = "organize:user:edit" ;
    /** 系统 - 用户管理 - 权限 */
    String SYS_USER_AUTH = "organize:user:auth" ;
    /** 系统 - 用户管理 - 修改状态 */
    String SYS_USER_ES = "organize:user:es" ;
    /** 系统 - 用户管理 - 密码修改 */
    String SYS_USER_RESET_PASSWORD = "organize:user:rp" ;
    /** 系统 - 用户管理 - 删除 */
    String SYS_USER_DEL = "organize:user:delete" ;
    /** 系统 - 用户管理 - 导入 */
    String SYS_USER_IMPORT = "organize:user:import" ;
    /** 系统 - 用户管理 - 导出 */
    String SYS_USER_EXPORT = "organize:user:export" ;

    /** 系统 - 模块管理 - 列表 */
    String SYS_MODULE_LIST = "authority:module:list" ;
    /** 系统 - 模块管理 - 详情 */
    String SYS_MODULE_SINGLE = "authority:module:single" ;
    /** 系统 - 模块管理 - 新增 */
    String SYS_MODULE_ADD = "authority:module:add" ;
    /** 系统 - 模块管理 - 修改 */
    String SYS_MODULE_EDIT = "authority:module:edit" ;
    /** 系统 - 模块管理 - 修改状态 */
    String SYS_MODULE_ES = "authority:module:es" ;
    /** 系统 - 模块管理 - 删除 */
    String SYS_MODULE_DEL = "authority:module:delete" ;
    /** 系统 - 模块管理 - 导入 */
    String SYS_MODULE_IMPORT = "authority:module:import" ;
    /** 系统 - 模块管理 - 导出 */
    String SYS_MODULE_EXPORT = "authority:module:export" ;

    /** 系统 - 菜单管理 - 列表 */
    String SYS_MENU_LIST = "authority:menu:list" ;
    /** 系统 - 菜单管理 - 详情 */
    String SYS_MENU_SINGLE = "authority:menu:single" ;
    /** 系统 - 菜单管理 - 新增 */
    String SYS_MENU_ADD = "authority:menu:add" ;
    /** 系统 - 菜单管理 - 修改 */
    String SYS_MENU_EDIT = "authority:menu:edit" ;
    /** 系统 - 菜单管理 - 修改状态 */
    String SYS_MENU_ES = "authority:menu:es" ;
    /** 系统 - 菜单管理 - 删除 */
    String SYS_MENU_DEL = "authority:menu:delete" ;
    /** 系统 - 菜单管理 - 导入 */
    String SYS_MENU_IMPORT = "authority:menu:import" ;
    /** 系统 - 菜单管理 - 导出 */
    String SYS_MENU_EXPORT = "authority:menu:export" ;

    /** 系统 - 角色管理 - 列表 */
    String SYS_ROLE_LIST = "authority:role:list" ;
    /** 系统 - 角色管理 - 详情 */
    String SYS_ROLE_SINGLE = "authority:role:single" ;
    /** 系统 - 角色管理 - 新增 */
    String SYS_ROLE_ADD = "authority:role:add" ;
    /** 系统 - 角色管理 - 修改 */
    String SYS_ROLE_EDIT = "authority:role:edit" ;
    /** 系统 - 角色管理 - 权限 */
    String SYS_ROLE_AUTH = "authority:role:auth" ;
    /** 系统 - 角色管理 - 修改状态 */
    String SYS_ROLE_ES = "authority:role:es" ;
    /** 系统 - 角色管理 - 删除 */
    String SYS_ROLE_DEL = "authority:role:delete" ;
    /** 系统 - 角色管理 - 导入 */
    String SYS_ROLE_IMPORT = "authority:role:import" ;
    /** 系统 - 角色管理 - 导出 */
    String SYS_ROLE_EXPORT = "authority:role:export" ;

    /** 系统 - 字典管理 - 列表 */
    String SYS_DICT_LIST = "dict:dict:list" ;
    /** 系统 - 字典管理 - 详情 */
    String SYS_DICT_SINGLE = "dict:dict:single" ;
    /** 系统 - 字典管理 - 新增 */
    String SYS_DICT_ADD = "dict:dict:add" ;
    /** 系统 - 字典管理 - 修改 */
    String SYS_DICT_EDIT = "dict:dict:edit" ;
    /** 系统 - 字典管理 - 修改状态 */
    String SYS_DICT_ES = "dict:dict:es" ;
    /** 系统 - 字典管理 - 删除 */
    String SYS_DICT_DEL = "dict:dict:delete" ;
    /** 系统 - 字典管理 - 导入 */
    String SYS_DICT_IMPORT = "dict:dict:import" ;
    /** 系统 - 字典管理 - 导出 */
    String SYS_DICT_EXPORT = "dict:dict:export" ;
    /** 系统 - 字典管理 - 字典管理 */
    String SYS_DICT_DICT = "dict:dict:dict" ;

    /** 系统 - 参数管理 - 列表 */
    String SYS_CONFIG_LIST = "dict:config:list" ;
    /** 系统 - 参数管理 - 详情 */
    String SYS_CONFIG_SINGLE = "dict:config:single" ;
    /** 系统 - 参数管理 - 新增 */
    String SYS_CONFIG_ADD = "dict:config:add" ;
    /** 系统 - 参数管理 - 修改 */
    String SYS_CONFIG_EDIT = "dict:config:edit" ;
    /** 系统 - 参数管理 - 修改状态 */
    String SYS_CONFIG_ES = "dict:config:es" ;
    /** 系统 - 参数管理 - 删除 */
    String SYS_CONFIG_DEL = "dict:config:delete" ;
    /** 系统 - 参数管理 - 强制删除 */
    String SYS_CONFIG_DEL_FORCE = "dict:config:delForce" ;
    /** 系统 - 参数管理 - 导入 */
    String SYS_CONFIG_IMPORT = "dict:config:import" ;
    /** 系统 - 参数管理 - 导出 */
    String SYS_CONFIG_EXPORT = "dict:config:export" ;

    /** 系统 - 通知公告 - 列表 */
    String SYS_NOTICE_LIST = "system:notice:list" ;
    /** 系统 - 通知公告 - 详情 */
    String SYS_NOTICE_SINGLE = "system:notice:single" ;
    /** 系统 - 通知公告 - 新增 */
    String SYS_NOTICE_ADD = "system:notice:add" ;
    /** 系统 - 通知公告 - 修改 */
    String SYS_NOTICE_EDIT = "system:notice:edit" ;
    /** 系统 - 通知公告 - 修改状态 */
    String SYS_NOTICE_ES = "system:notice:es" ;
    /** 系统 - 通知公告 - 删除 */
    String SYS_NOTICE_DEL = "system:notice:delete" ;
    /** 系统 - 通知公告 - 导入 */
    String SYS_NOTICE_IMPORT = "system:notice:import" ;
    /** 系统 - 通知公告 - 导出 */
    String SYS_NOTICE_EXPORT = "system:notice:export" ;

    /** 系统 - 访问日志 - 列表 */
    String SYS_LOGIN_LOG_LIST = "monitor:loginLog:list" ;
    /** 系统 - 访问日志 - 删除 */
    String SYS_LOGIN_LOG_DEL = "monitor:loginLog:delete" ;
    /** 系统 - 访问日志 - 导出 */
    String SYS_LOGIN_LOG_EXPORT = "monitor:loginLog:export" ;

    /** 系统 - 操作日志 - 列表 */
    String SYS_OPERATE_LOG_LIST = "monitor:operateLog:list" ;
    /** 系统 - 操作日志 - 详情 */
    String SYS_OPERATE_LOG_SINGLE = "monitor:operateLog:single" ;
    /** 系统 - 操作日志 - 删除 */
    String SYS_OPERATE_LOG_DEL = "monitor:operateLog:delete" ;
    /** 系统 - 操作日志 - 导出 */
    String SYS_OPERATE_LOG_EXPORT = "monitor:operateLog:export" ;

    /** 系统 - 在线用户 - 列表 */
    String SYS_ONLINE_LIST = "monitor:online:list" ;
    /** 系统 - 在线用户 - 强退 */
    String SYS_ONLINE_FORCE_LOGOUT = "monitor:online:forceLogout" ;

    /** 系统 - 文件管理 - 列表 */
    String SYS_FILE_LIST = "system:file:list" ;
    /** 系统 - 文件管理 - 详情 */
    String SYS_FILE_SINGLE = "system:file:single" ;
    /** 系统 - 文件管理 - 新增 */
    String SYS_FILE_ADD = "system:file:add" ;
    /** 系统 - 文件管理 - 删除 */
    String SYS_FILE_DEL = "system:file:delete" ;

    /** 系统 - 公司管理 - 列表 */
    String SYS_COMPANY_LIST = "system:company:list";
    /** 系统 - 公司管理 - 详情 */
    String SYS_COMPANY_SINGLE = "system:company:single";
    /** 系统 - 公司管理 - 新增 */
    String SYS_COMPANY_ADD = "system:company:add";
    /** 系统 - 公司管理 - 修改 */
    String SYS_COMPANY_EDIT = "system:company:edit";
    /** 系统 - 公司管理 - 修改状态 */
    String SYS_COMPANY_ES = "system:company:es";
    /** 系统 - 公司管理 - 删除 */
    String SYS_COMPANY_DEL = "system:company:delete";

    /** 系统 - 第三方模块管理 - 列表 */
    String SYS_THIRD_LIST = "system:third:list";
    /** 系统 - 第三方模块管理 - 详情 */
    String SYS_THIRD_SINGLE = "system:third:single";
    /** 系统 - 第三方模块管理 - 新增 */
    String SYS_THIRD_ADD = "system:third:add";
    /** 系统 - 第三方模块管理 - 修改 */
    String SYS_THIRD_EDIT = "system:third:edit";
    /** 系统 - 第三方模块管理 - 修改状态 */
    String SYS_THIRD_ES = "system:third:es";
    /** 系统 - 第三方模块管理 - 删除 */
    String SYS_THIRD_DEL = "system:third:delete";

    /** 系统 - 第三方认证管理 - 列表 */
    String SYS_THIRD_AUTH_LIST = "system:auth:list";
    /** 系统 - 第三方认证管理 - 详情 */
    String SYS_THIRD_AUTH_SINGLE = "system:auth:single";
    /** 系统 - 第三方认证管理 - 新增 */
    String SYS_THIRD_AUTH_ADD = "system:auth:add";
    /** 系统 - 第三方认证管理 - 修改 */
    String SYS_THIRD_AUTH_EDIT = "system:auth:edit";
    /** 系统 - 第三方认证管理 - 修改状态 */
    String SYS_THIRD_AUTH_ES = "system:auth:es";
    /** 系统 - 第三方认证管理 - 删除 */
    String SYS_THIRD_AUTH_DEL = "system:auth:delete";

    /** 系统 - 第三方认证管理 - 删除(MASTER) */
    String SYS_THIRD_AUTH_DELMASTER = "system:auth:deleteMaster";


    /** 系统 - 第三方认证管理 - 配置公司 */
    String SYS_THIRD_AUTH_CONFIGURE = "system:auth:configure";

    /** 系统 - 第三方认证管理 - 公司列表 */
    String SYS_THIRD_AUTH_COMPANY_LIST= "system:auth:companyList";


    /** 系统 - 实体字段管理管理 - 列表 */
    String SYS_ENTITY_FIELD_LIST = "system:field:list";
    /** 系统 - 实体字段管理管理 - 详情 */
    String SYS_ENTITY_FIELD_SINGLE = "system:field:single";
    /** 系统 - 实体字段管理管理 - 新增 */
    String SYS_ENTITY_FIELD_ADD = "system:field:add";
    /** 系统 - 实体字段管理管理 - 修改 */
    String SYS_ENTITY_FIELD_EDIT = "system:field:edit";
    /** 系统 - 实体字段管理管理 - 修改状态 */
    String SYS_ENTITY_FIELD_ES = "system:field:es";
    /** 系统 - 实体字段管理管理 - 删除 */
    String SYS_ENTITY_FIELD_DEL = "system:field:delete";

    /** 系统 - 服务项目管理管理 - 列表 */
    String SYS_SERVICE_MANAGEMENT_LIST = "system:management:list";
    /** 系统 - 服务项目管理管理 - 详情 */
    String SYS_SERVICE_MANAGEMENT_SINGLE = "system:management:single";
    /** 系统 - 服务项目管理管理 - 新增 */
    String SYS_SERVICE_MANAGEMENT_ADD = "system:management:add";
    /** 系统 - 服务项目管理管理 - 修改 */
    String SYS_SERVICE_MANAGEMENT_EDIT = "system:management:edit";
    /** 系统 - 服务项目管理管理 - 修改状态 */
    String SYS_SERVICE_MANAGEMENT_ES = "system:management:es";
    /** 系统 - 服务项目管理管理 - 删除 */
    String SYS_SERVICE_MANAGEMENT_DEL = "system:management:delete";

    /** 系统 - 实体信息审核管理 - 列表 */
    String SYS_ENTITY_EXAMINE_LIST = "system:examine:list";
    /** 系统 - 实体信息审核管理 - 详情 */
    String SYS_ENTITY_EXAMINE_SINGLE = "system:examine:single";
    /** 系统 - 实体信息审核管理 - 新增 */
    String SYS_ENTITY_EXAMINE_ADD = "system:examine:add";
    /** 系统 - 实体信息审核管理 - 修改 */
    String SYS_ENTITY_EXAMINE_EDIT = "system:examine:edit";
    /** 系统 - 实体信息审核管理 - 修改状态 */
    String SYS_ENTITY_EXAMINE_ES = "system:examine:es";
    /** 系统 - 实体信息审核管理 - 删除 */
    String SYS_ENTITY_EXAMINE_DEL = "system:examine:delete";

    /** 系统 - 财税报表管理 - 列表 */
    String SYS_FINANCE_REPORT_LIST = "system:finance:list";
    /** 系统 - 财税报表管理 - 详情 */
    String SYS_FINANCE_REPORT_SINGLE = "system:finance:single";
    /** 系统 - 财税报表管理 - 新增 */
    String SYS_FINANCE_REPORT_ADD = "system:finance:add";
    /** 系统 - 财税报表管理 - 修改 */
    String SYS_FINANCE_REPORT_EDIT = "system:finance:edit";
    /** 系统 - 财税报表管理 - 修改状态 */
    String SYS_FINANCE_REPORT_ES = "system:finance:es";
    /** 系统 - 财税报表管理 - 删除 */
    String SYS_FINANCE_REPORT_DEL = "system:finance:delete";

    /** 系统 - 银行报表管理 - 列表 */
    String SYS_BANK_REPORT_LIST = "system:bank:list";
    /** 系统 - 银行报表管理 - 详情 */
    String SYS_BANK_REPORT_SINGLE = "system:bank:single";
    /** 系统 - 银行报表管理 - 新增 */
    String SYS_BANK_REPORT_ADD = "system:bank:add";
    /** 系统 - 银行报表管理 - 修改 */
    String SYS_BANK_REPORT_EDIT = "system:bank:edit";
    /** 系统 - 银行报表管理 - 修改状态 */
    String SYS_BANK_REPORT_ES = "system:bank:es";
    /** 系统 - 银行报表管理 - 删除 */
    String SYS_BANK_REPORT_DEL = "system:bank:delete";

    /** 系统 - 税务申报管理 - 列表 */
    String SYS_TAX_REPORT_LIST = "system:tax:list";
    /** 系统 - 税务申报管理 - 详情 */
    String SYS_TAX_REPORT_SINGLE = "system:tax:single";
    /** 系统 - 税务申报管理 - 新增 */
    String SYS_TAX_REPORT_ADD = "system:tax:add";
    /** 系统 - 税务申报管理 - 修改 */
    String SYS_TAX_REPORT_EDIT = "system:tax:edit";
    /** 系统 - 税务申报管理 - 修改状态 */
    String SYS_TAX_REPORT_ES = "system:tax:es";
    /** 系统 - 税务申报管理 - 删除 */
    String SYS_TAX_REPORT_DEL = "system:tax:delete";

    /** 系统 - 发票管理详情管理 - 列表 */
    String SYS_BILL_REPORT_LIST = "system:bill:list";
    /** 系统 - 发票管理详情管理 - 详情 */
    String SYS_BILL_REPORT_SINGLE = "system:bill:single";
    /** 系统 - 发票管理详情管理 - 新增 */
    String SYS_BILL_REPORT_ADD = "system:bill:add";
    /** 系统 - 发票管理详情管理 - 修改 */
    String SYS_BILL_REPORT_EDIT = "system:bill:edit";
    /** 系统 - 发票管理详情管理 - 修改状态 */
    String SYS_BILL_REPORT_ES = "system:bill:es";
    /** 系统 - 发票管理详情管理 - 删除 */
    String SYS_BILL_REPORT_DEL = "system:bill:delete";

    /** 系统 - 实体信息管理 - 列表 */
    String SYS_ENTITY_LIST = "system:entity:list";
    /** 系统 - 实体信息管理 - 详情 */
    String SYS_ENTITY_SINGLE = "system:entity:single";
    /** 系统 - 实体信息管理 - 新增 */
    String SYS_ENTITY_ADD = "system:entity:add";
    /** 系统 - 实体信息管理 - 修改 */
    String SYS_ENTITY__EDIT = "system:entity:edit";
    /** 系统 - 实体信息管理 - 修改状态 */
    String SYS_ENTITY_ES = "system:entity:es";

    /** 系统 - 实体信息管理 - 导入 */
    String SYS_ENTITY_IMPORT = "system:entity:import";

    /** 系统 - 实体信息管理 - 下载模板 */
    String SYS_ENTITY_TEMPLATE = "system:entity:template";

    /** 系统 - 实体信息管理 - 删除 */
    String SYS_ENTITY_DEL = "system:entity:delete";


    /** 系统 - 工商信息管理 - 修改 */
    String SYS_SAIC_EDIT = "system:saic:edit";

    /** 系统 - 工商信息管理 - 详情 */
    String SYS_SAIC_SINGLE = "system:saic:single";


    /** 系统 - 税务信息管理 - 修改 */
    String SYS_TAX_EDIT = "system:tax:edit";

    /** 系统 - 税务信息管理 - 详情 */
    String SYS_TAX_SINGLE = "system:tax:single";



    /** 系统 - 第三方认证管理 - 新增 */
    String SYS_THIRD_AUTH_CONFIGURE_ADD = "system:auth:addAuth";

    /** 系统 - 第三方认证管理 - 配置公司超管查询 */
    String SYS_THIRD_AUTH_CONFIGURE_ADMIN = "system:auth:configureAdmin";

    /** 系统 - 第三方认证管理 - 配置公司超管新增 */
    String SYS_THIRD_AUTH_CONFIGURE_ADMIN_ADD= "system:auth:addAuthAdmin";


    /** 系统 - 工商年报 - 列表 */
    String SYS_BUSINESS_ANNUAL_REPORT_LIST = "system:business:list";
    /** 系统 - 工商年报管理 - 列表 */
    String SYS_BUSINESS_ANNUAL_REPORT_MANAGE_LIST = "system:manage:list";
    /** 系统 - 工商年报 - 详情 */
    String SYS_BUSINESS_ANNUAL_REPORT_SINGLE = "system:business:single";
    /** 系统 - 工商年报管理 - 详情 */
    String SYS_BUSINESS_ANNUAL_REPORT_MANAGE_SINGLE = "system:manage:single";
    /** 系统 - 工商年报 - 新增 */
    String SYS_BUSINESS_ANNUAL_REPORT_ADD = "system:business:add";
    /** 系统 - 工商年报 - 修改 */
    String SYS_BUSINESS_ANNUAL_REPORT_EDIT = "system:business:edit";
    /** 系统 - 工商年报 - 修改状态 */
    String SYS_BUSINESS_ANNUAL_REPORT_ES = "system:business:es";
    /** 系统 - 工商年报 - 删除 */
    String SYS_BUSINESS_ANNUAL_REPORT_DEL = "system:business:delete";
    /** 系统 - 工商年报管理 - 审批通过 */
    String SYS_BUSINESS_ANNUAL_REPORT_PASS = "system:manage:pass";
    /** 系统 - 工商年报管理 - 驳回 */
    String SYS_BUSINESS_ANNUAL_REPORT_REJECT = "system:manage:reject";


    /** 系统 - 税务申报 - 列表 */
    String SYS_TAX_FILINGS_LIST = "system:taxFilings:list";
    /** 系统 - 税务申报 - 详情 */
    String SYS_TAX_FILINGS_SINGLE = "system:taxFilings:single";
    /** 系统 - 税务申报 - 新增 */
    String SYS_TAX_FILINGS_ADD = "system:taxFilings:add";
    /** 系统 - 税务申报 - 修改 */
    String SYS_TAX_FILINGS_EDIT = "system:taxFilings:edit";
    /** 系统 - 税务申报 - 修改状态 */
    String SYS_TAX_FILINGS_ES = "system:taxFilings:es";
    /** 系统 - 税务申报 - 删除 */
    String SYS_TAX_FILINGS_DEL = "system:taxFilings:delete";

    /** 系统 - 租户菜单 - 修改 */
    String SYS_TENANT_MENU_EDIT ="system:tenantMenu_edit";
    /** 系统 - 租户菜单 - 查询列表 */
    String SYS_TENANT_MENU_LIST = "system:tenantMenu_list";

    /** 系统 - 企业经营异常信息管理 - 列表 */
    String SYS_ENTITY_EXCEPTION_INFO_LIST = "system:SysEntityExceptionInfo:list";
    /** 系统 - 企业经营异常信息管理 - 删除 */
    String SYS_ENTITY_EXCEPTION_INFO_DEL = "system:SysEntityExceptionInfo:delete";
    /** 系统 - 企业经营异常信息管理 - 添加 */
    String SYS_ENTITY_EXCEPTION_INFO_ADD = "system:SysEntityExceptionInfo:add";
    /** 系统 - 企业经营异常信息管理 - 历史详情 */
    String SYS_ENTITY_EXCEPTION_INFO_HISTORY= "system:SysEntityExceptionInfo:history";
    /** 系统 - 企业经营异常信息管理 - 刷新*/
    String SYS_ENTITY_EXCEPTION_INFO_REFRESH="system:SysEntityExceptionInfo:refresh";
    /** 系统 - 手机号验证码 - 查看*/
    String SYS_PHONE_CODE = "system:Phone:code";


    /** 系统 - 表单管理管理 - 列表 */
    String SYS_SHEET_LIST = "system:sheet:list";
    /** 系统 - 表单管理管理 - 详情 */
    String SYS_SHEET_SINGLE = "system:sheet:single";
    /** 系统 - 表单管理管理 - 新增 */
    String SYS_SHEET_ADD = "system:sheet:add";
    /** 系统 - 表单管理管理 - 修改 */
    String SYS_SHEET_EDIT = "system:sheet:edit";
    /** 系统 - 表单管理管理 - 修改状态 */
    String SYS_SHEET_ES = "system:sheet:es";
    /** 系统 - 表单管理管理 - 删除 */
    String SYS_SHEET_DEL = "system:sheet:delete";

    /** 系统 - 字段管理管理 - 列表 */
    String SYS_FIELD_LIST = "system:field:list";
    /** 系统 - 字段管理管理 - 详情 */
    String SYS_FIELD_SINGLE = "system:field:single";
    /** 系统 - 字段管理管理 - 新增 */
    String SYS_FIELD_ADD = "system:field:add";
    /** 系统 - 字段管理管理 - 修改 */
    String SYS_FIELD_EDIT = "system:field:edit";
    /** 系统 - 字段管理管理 - 修改状态 */
    String SYS_FIELD_ES = "system:field:es";
    /** 系统 - 字段管理管理 - 删除 */
    String SYS_FIELD_DEL = "system:field:delete";

    /** 系统 - 关联关系管理 - 列表 */
    String SYS_FIELD_RELATION = "system:field:relation";

    /** 系统 - 选项管理 - 列表 */
    String SYS_OPTION_LIST = "system:option:list";
    /** 系统 - 选项管理 - 详情 */
    String SYS_OPTION_SINGLE = "system:option:single";
    /** 系统 - 选项管理 - 新增 */
    String SYS_OPTION_ADD = "system:option:add";
    /** 系统 - 选项管理 - 修改 */
    String SYS_OPTION_EDIT = "system:option:edit";
    /** 系统 - 选项管理 - 修改状态 */
    String SYS_OPTION_ES = "system:option:es";
    /** 系统 - 选项管理 - 删除 */
    String SYS_OPTION_DEL = "system:option:delete";

    String SYS_OPTION_QUOTE = "system:option:quote";

    /** 系统 - 级联管理 - 列表 */
    String SYS_CASCADE_LIST = "system:cascade:list";
    /** 系统 - 级联管理 - 详情 */
    String SYS_CASCADE_SINGLE = "system:cascade:single";
    /** 系统 - 级联管理 - 新增 */
    String SYS_CASCADE_ADD = "system:cascade:add";
    /** 系统 - 级联管理 - 修改 */
    String SYS_CASCADE_EDIT = "system:cascade:edit";
    /** 系统 - 级联管理 - 修改状态 */
    String SYS_CASCADE_ES = "system:cascade:es";
    /** 系统 - 级联管理 - 删除 */
    String SYS_CASCADE_DEL = "system:cascade:delete";

    String SYS_CASCADE_QUOTE = "system:cascade:quote";

    /** 流程 - 流程管理 - 列表 */
    String SYS_CATEGORY_LIST = "flowable:workflow:category:list";



    /** 系统 - 文件信息管理 - 列表 */
    String SYS_FILE_INFO_LIST = "system:file:list";
    /** 系统 - 文件信息管理 - 详情 */
    String SYS_FILE_INFO_SINGLE = "system:file:single";
    /** 系统 - 文件信息管理 - 新增 */
    String SYS_FILE_INFO_ADD = "system:file:add";
    /** 系统 - 文件信息管理 - 修改 */
    String SYS_FILE_INFO_EDIT = "system:file:edit";
    /** 系统 - 文件信息管理 - 修改状态 */
    String SYS_FILE_INFO_ES = "system:file:es";
    /** 系统 - 文件信息管理 - 删除 */
    String SYS_FILE_INFO_DEL = "system:file:delete";
    /** 系统 - 文件信息管理 - 删除 */
    String SYS_FILE_INFO_DEL_REPLY = "system:file:batchReplyList";

    /** 系统 - 文件借阅记录管理 - 列表 */
    String SYS_FILE_BORROW_RECORD_LIST = "file:record:list";
    /** 系统 - 文件借阅记录管理 - 详情 */
    String SYS_FILE_BORROW_RECORD_SINGLE = "file:record:single";
    /** 系统 - 文件借阅记录管理 - 新增 */
    String SYS_FILE_BORROW_RECORD_ADD = "file:record:add";
    /** 系统 - 文件借阅记录管理 - 修改 */
    String SYS_FILE_BORROW_RECORD_EDIT = "file:record:edit";
    /** 系统 - 文件借阅记录管理 - 修改状态 */
    String SYS_FILE_BORROW_RECORD_ES = "file:record:es";
    /** 系统 - 文件借阅记录管理 - 删除 */
    String SYS_FILE_BORROW_RECORD_DEL = "file:record:delete";

    /** 系统 - 文件角色权限关联管理 - 列表 */
    String SYS_FILE_ROLE_MERGE_LIST = "file:merge:list";
    /** 系统 - 文件角色权限关联管理 - 详情 */
    String SYS_FILE_ROLE_MERGE_SINGLE = "file:merge:single";
    /** 系统 - 文件角色权限关联管理 - 新增 */
    String SYS_FILE_ROLE_MERGE_ADD = "file:merge:add";
    /** 系统 - 文件角色权限关联管理 - 修改 */
    String SYS_FILE_ROLE_MERGE_EDIT = "file:merge:edit";
    /** 系统 - 文件角色权限关联管理 - 修改状态 */
    String SYS_FILE_ROLE_MERGE_ES = "file:merge:es";
    /** 系统 - 文件角色权限关联管理 - 删除 */
    String SYS_FILE_ROLE_MERGE_DEL = "file:merge:delete";

    /** 系统 - 文件存储位置管理 - 列表 */
    String SYS_FILE_POSITION_LIST = "system:position:list";
    /** 系统 - 文件存储位置管理 - 详情 */
    String SYS_FILE_POSITION_SINGLE = "system:position:single";
    /** 系统 - 文件存储位置管理 - 新增 */
    String SYS_FILE_POSITION_ADD = "system:position:add";
    /** 系统 - 文件存储位置管理 - 修改 */
    String SYS_FILE_POSITION_EDIT = "system:position:edit";
    /** 系统 - 文件存储位置管理 - 修改状态 */
    String SYS_FILE_POSITION_ES = "system:position:es";
    /** 系统 - 文件存储位置管理 - 删除 */
    String SYS_FILE_POSITION_DEL = "system:position:delete";

    /** 系统 - 文件分类管理 - 列表 */
    String SYS_FILE_CLASSIFY_LIST = "file:classify:list";
    /** 系统 - 文件分类管理 - 详情 */
    String SYS_FILE_CLASSIFY_SINGLE = "file:classify:single";
    /** 系统 - 文件分类管理 - 新增 */
    String SYS_FILE_CLASSIFY_ADD = "file:classify:add";
    /** 系统 - 文件分类管理 - 修改 */
    String SYS_FILE_CLASSIFY_EDIT = "file:classify:edit";
    /** 系统 - 文件分类管理 - 修改状态 */
    String SYS_FILE_CLASSIFY_ES = "file:classify:es";
    /** 系统 - 文件分类管理 - 删除 */
    String SYS_FILE_CLASSIFY_DEL = "file:classify:delete";

    /** 系统 - 文件操作记录管理 - 列表 */
    String SYS_FILE_RECORD_LIST = "file:record:list";
    /** 系统 - 文件操作记录管理 - 详情 */
    String SYS_FILE_RECORD_SINGLE = "file:record:single";
    /** 系统 - 文件操作记录管理 - 新增 */
    String SYS_FILE_RECORD_ADD = "file:record:add";
    /** 系统 - 文件操作记录管理 - 修改 */
    String SYS_FILE_RECORD_EDIT = "file:record:edit";
    /** 系统 - 文件操作记录管理 - 修改状态 */
    String SYS_FILE_RECORD_ES = "file:record:es";
    /** 系统 - 文件操作记录管理 - 删除 */
    String SYS_FILE_RECORD_DEL = "file:record:delete";

    /** 系统 - 转发短信管理 - 列表 */
    String SYS_TELEPHONE_CODE_LIST = "system:telephoneCode:list";
    /** 系统 - 转发短信管理 - 详情 */
    String SYS_TELEPHONE_CODE_SINGLE = "system:telephoneCode:single";
    /** 系统 - 转发短信管理 - 新增 */
    String SYS_TELEPHONE_CODE_ADD = "system:telephoneCode:add";

    /** 系统 - 手机号管理 - 列表 */
    String SYS_PHONE_NUMBER_INFO_LIST = "system:phone:list";
    /** 系统 - 手机号管理 - 详情 */
    String SYS_PHONE_NUMBER_INFO_SINGLE = "system:phone:single";
    /** 系统 - 手机号管理 - 新增 */
    String SYS_PHONE_NUMBER_INFO_ADD = "system:phone:add";
    /** 系统 - 手机号管理 - 修改 */
    String SYS_PHONE_NUMBER_INFO_EDIT = "system:phone:edit";
    /** 系统 - 手机号管理 - 修改状态 */
    String SYS_PHONE_NUMBER_INFO_ES = "system:phone:es";
    /** 系统 - 手机号管理 - 删除 */
    String SYS_PHONE_NUMBER_INFO_DEL = "system:phone:delete";

    /** 系统 - 手机号授权管理 - 列表 */
    String SYS_PHO_PERMISSION_RECORD_LIST = "system:phone:record:list";
    /** 系统 - 手机号授权管理 - 详情 */
    String SYS_PHO_PERMISSION_RECORD_SINGLE = "system:phone:record:single";
    /** 系统 - 手机号授权管理 - 新增 */
    String SYS_PHO_PERMISSION_RECORD_ADD = "system:phone:record:add";
    /** 系统 - 手机号授权管理 - 修改 */
    String SYS_PHO_PERMISSION_RECORD_EDIT = "system:phone:record:edit";
    /** 系统 - 手机号授权管理 - 修改状态 */
    String SYS_PHO_PERMISSION_RECORD_ES = "system:phone:record:es";
    /** 系统 - 手机号授权管理 - 删除 */
    String SYS_PHO_PERMISSION_RECORD_DEL = "system:phone:record:delete";


    /** 系统 - 客户信息审核管理 - 列表 */
    String SYS_APPROVAL_CUSTOMERINFO_LIST = "system:customerinfo:list";
    /** 系统 - 客户信息审核管理 - 详情 */
    String SYS_APPROVAL_CUSTOMERINFO_SINGLE = "system:customerinfo:single";
    /** 系统 - 客户信息审核管理 - 新增 */
    String SYS_APPROVAL_CUSTOMERINFO_ADD = "system:customerinfo:add";
    /** 系统 - 客户信息审核管理 - 修改 */
    String SYS_APPROVAL_CUSTOMERINFO_EDIT = "system:customerinfo:edit";
    /** 系统 - 客户信息审核管理 - 删除 */
    String SYS_APPROVAL_CUSTOMERINFO_DEL = "system:customerinfo:delete";
    /** 系统 - 客户信息审核管理 - 审批通过 */
    String SYS_APPROVAL_CUSTOMERINFO_PASS = "system:customerinfo:pass";
    /** 系统 - 客户信息审核管理 - 审批驳回 */
    String SYS_APPROVAL_CUSTOMERINFO_REJECT = "system:customerinfo:reject";

    /** 系统 - 工商年报管理 - 列表 */
    String SYS_ANNUAL_REPORT_LIST = "system:report:list";
    /** 系统 - 工商年报管理 - 详情 */
    String SYS_ANNUAL_REPORT_SINGLE = "system:report:single";
    /** 系统 - 工商年报管理 - 新增 */
    String SYS_ANNUAL_REPORT_ADD = "system:report:add";
    /** 系统 - 工商年报管理 - 修改 */
    String SYS_ANNUAL_REPORT_EDIT = "system:report:edit";
    /** 系统 - 工商年报管理 - 修改状态 */
    String SYS_ANNUAL_REPORT_ES = "system:report:es";
    /** 系统 - 工商年报管理 - 删除 */
    String SYS_ANNUAL_REPORT_DEL = "system:report:delete";

    /** 系统 - 显隐列管理 - 列表 */
    String SYS_DISPLAY_INFO_LIST = "system:display:list";
    /** 系统 - 显隐列管理 - 详情 */
    String SYS_DISPLAY_INFO_SINGLE = "system:display:single";
    /** 系统 - 显隐列管理 - 新增 */
    String SYS_DISPLAY_INFO_ADD = "system:display:add";
    /** 系统 - 显隐列管理 - 修改 */
    String SYS_DISPLAY_INFO_EDIT = "system:display:edit";
    /** 系统 - 显隐列管理 - 删除 */
    String SYS_DISPLAY_INFO_DEL = "system:display:delete";

    /** 系统 - 行政申请管理 - 列表 */
    String SYS_APPLY_RECORD_LIST = "system:bus:apply:list";
    /** 系统 - 行政申请管理 - 详情 */
    String SYS_APPLY_RECORD_SINGLE = "system:bus:apply:single";
    /** 系统 - 行政申请管理 - 新增 */
    String SYS_APPLY_RECORD_ADD = "system:bus:apply:add";
    /** 系统 - 行政申请管理 - 修改 */
    String SYS_APPLY_RECORD_EDIT = "system:bus:apply:edit";
    /** 系统 - 行政申请管理 - 修改状态 */
    String SYS_APPLY_RECORD_ES = "system:bus:apply:es";
    /** 系统 - 行政申请管理 - 删除 */
    String SYS_APPLY_RECORD_DEL = "system:bus:apply:delete";

}
