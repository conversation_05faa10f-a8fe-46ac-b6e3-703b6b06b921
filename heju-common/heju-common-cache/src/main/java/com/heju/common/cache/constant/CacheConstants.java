package com.heju.common.cache.constant;

import com.heju.common.core.utils.core.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 缓存通用常量
 *
 * <AUTHOR>
 */
public class CacheConstants {

    /** 缓存类型 */
    @Getter
    @AllArgsConstructor
    public enum CacheType {

        SYS_DICT_KEY("system:dict", "字典"),
        SYS_CONFIG_KEY("system:config", "参数"),
        TE_STRATEGY_KEY("system:strategy", "源策略组"),
        TE_SOURCE_KEY("system:source", "数据源"),
        SYS_COMPANY_KEY("system:company","公司信息"),
        SYS_THIRD_AUTH_KEY("system:thirdAuth","第三方认证信息"),
        SYS_COMPANY_TENANT_KEY("system:company:tenant","公司租户关联"),
        SYS_COMPANY_THIRD_AUTH_KEY("system:company:thirdAuth","第三方认证信息id与公司id关联"),
        SYS_TELEPHONE_CODE_KEY("telephone:code","手机号验证码"),
        SYS_ENTITY_UUID_KEY("system:entity:uuid","实体信息导入uuid"),
        SYS_APPROVAL_TENANT_KEY("tenant:approval:uuid","租户审批uuid");

        private final String code;
        private final String info;

        public static CacheType getByCode(String code) {
            return EnumUtil.getByCode(CacheType.class, code);
        }

    }

}
