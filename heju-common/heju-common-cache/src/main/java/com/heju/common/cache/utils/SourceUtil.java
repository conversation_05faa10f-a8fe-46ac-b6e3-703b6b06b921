package com.heju.common.cache.utils;

import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.cache.service.CacheService;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.SpringUtil;
import com.heju.system.api.model.Source;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;

import java.util.List;

/**
 * 源策略组缓存管理工具类
 *
 * <AUTHOR>
 */
public class SourceUtil {

    /**
     * 源策略组查询
     *
     * @param id 源策略组Id
     */
    public static Source getSourceCache(Long id) {
        TeStrategyDto strategy = getTeStrategyCache(id);
        if (ObjectUtil.isNull(strategy))
            return null;
        Source source = new Source();
        source.setId(strategy.getId());
        source.setSourceId(strategy.getSourceId());
        source.setMaster(strategy.getSourceSlave());
        return source;
    }

    /**
     * 获取源策略缓存
     *
     * @param id 源策略组Id
     * @return 源策略对象
     */
    public static TeStrategyDto getTeStrategyCache(Long id) {
        return SpringUtil.getBean(CacheService.class).getCacheObject(CacheConstants.CacheType.TE_STRATEGY_KEY, id.toString());
    }

    /**
     * 获取数据源缓存
     *
     * @param slave 数据源编码
     * @return 数据源对象
     */
    public static TeSourceDto getTeSourceCache(String slave) {
//        SpringUtil.getBean(CacheService.class).getCacheList(CacheConstants.CacheType.TE_SOURCE_KEY)
        return SpringUtil.getBean(CacheService.class).getCacheObject(CacheConstants.CacheType.TE_SOURCE_KEY, slave);
    }
}
