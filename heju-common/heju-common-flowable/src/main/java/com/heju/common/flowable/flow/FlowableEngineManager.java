package com.heju.common.flowable.flow;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FlowableEngineManager {
    private static final Map<String, ProcessEngine> ENGINE_MAP = new ConcurrentHashMap<>();

    public static ProcessEngine getEngine(String slave) {
        return ENGINE_MAP.get(slave);
    }

    public static void initEngine(String slave, DataSource ds) {
        ProcessEngineConfiguration cfg = ProcessEngineConfiguration
                .createStandaloneProcessEngineConfiguration()
                .setDataSource(ds)
                .setDatabaseSchemaUpdate("false")
                .setAsyncExecutorActivate(false);

        ProcessEngine engine = cfg.buildProcessEngine();
        ENGINE_MAP.put(slave, engine);
    }
}
