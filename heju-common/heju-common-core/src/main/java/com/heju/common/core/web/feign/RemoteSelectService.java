package com.heju.common.core.web.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.common.core.web.result.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 远程查询服务层
 *
 * @param <D> Dto
 * <AUTHOR>
 */
public interface RemoteSelectService<D extends BaseEntity> {

    /**
     * 根据Id查询数据信息
     *
     * @param id Id
     * @return 数据信息
     */
    @GetMapping(value = "/id", headers = SecurityConstants.FROM_SOURCE_INNER)
    R<D> selectByIdInner(@RequestParam("id") Serializable id);

    /**
     * 根据Ids查询数据信息集合
     *
     * @param ids Ids
     * @return 数据信息集合
     */
    @GetMapping(value = "/list/ids", headers = SecurityConstants.FROM_SOURCE_INNER)
    R<List<D>> selectListByIdsInner(@RequestParam("ids") Collection<? extends Serializable> ids);
}
