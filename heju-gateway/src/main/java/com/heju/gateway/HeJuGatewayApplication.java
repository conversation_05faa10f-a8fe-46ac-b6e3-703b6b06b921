package com.heju.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 网关启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class HeJuGatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(HeJuGatewayApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  网关模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " ('-. .-.          \n" +
                "( OO )  /          \n" +
                ",--. ,--.     ,--. \n" +
                "|  | |  | .-')| ,| \n" +
                "|   .|  |( OO |(_| \n" +
                "|       || `-'|  | \n" +
                "|  .-.  |,--. |  | \n" +
                "|  | |  ||  '-'  / \n" +
                "`--' `--' `-----'  \n");
    }
}
