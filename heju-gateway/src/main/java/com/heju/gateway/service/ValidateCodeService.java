package com.heju.gateway.service;

import com.heju.common.core.exception.CaptchaException;
import com.heju.common.core.web.result.AjaxResult;

import java.io.IOException;

/**
 * 验证码处理
 *
 * <AUTHOR>
 */
public interface ValidateCodeService {

    /**
     * 生成验证码
     */
    AjaxResult createCaptcha() throws IOException, CaptchaException;

    /**
     * 校验验证码
     */
    void checkCaptcha(String key, String value) throws CaptchaException;
}
