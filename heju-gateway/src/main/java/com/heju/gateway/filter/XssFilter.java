package com.heju.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.heju.common.core.utils.core.CollUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.utils.html.EscapeUtil;
import com.heju.gateway.config.properties.XssProperties;
import io.netty.buffer.ByteBufAllocator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * 跨站脚本过滤器
 *
 * <AUTHOR>
 */
@Component
public class XssFilter implements GlobalFilter, Ordered {

    // 跨站脚本的 xss 配置，nacos自行添加
    @Autowired
    private XssProperties xss;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        // xss开关未开启 或 通过nacos关闭，不过滤
        if (!xss.getEnabled()) {
            return chain.filter(exchange);
        }
        // GET DELETE 不过滤
        HttpMethod method = request.getMethod();
        if (method == null || method == HttpMethod.GET || method == HttpMethod.DELETE) {
            return chain.filter(exchange);
        }
        // 非json类型，不过滤
        if (!isJsonRequest(exchange)) {
            return chain.filter(exchange);
        }
        // excludeUrls 不过滤
        String url = request.getURI().getPath();
        if (CollUtil.contains(xss.getExcludeUrls(), url)) {
            return chain.filter(exchange);
        }
        ServerHttpRequestDecorator httpRequestDecorator = requestDecorator(exchange);
        return chain.filter(exchange.mutate().request(httpRequestDecorator).build());

    }

    private ServerHttpRequestDecorator requestDecorator(ServerWebExchange exchange) {
        ServerHttpRequestDecorator serverHttpRequestDecorator = new ServerHttpRequestDecorator(exchange.getRequest()) {
            /*@Override
            public Flux<DataBuffer> getBody() {
                Flux<DataBuffer> body = super.getBody();
                return body.buffer().map(dataBuffers -> {
                    DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
                    DataBuffer join = dataBufferFactory.join(dataBuffers);
                    byte[] content = new byte[join.readableByteCount()];
                    join.read(content);
                    DataBufferUtils.release(join);
                    String bodyStr = new String(content, StandardCharsets.UTF_8);
                    // 防xss攻击过滤
                    JSONObject json = JSON.parseObject(bodyStr);
                    // 不过滤 bpmnXml
                    String bpmnXml = json.getString("bpmnXml");
                    // 清洗其他字段
                    for (String key : json.keySet()) {
                        if (!"bpmnXml".equals(key)) {
                            String value = json.getString(key);
                            json.put(key, EscapeUtil.clean(value));
                        }
                    }
                    json.put("bpmnXml", bpmnXml);
                    bodyStr = json.toJSONString();
                    // 转成字节
                    byte[] bytes = bodyStr.getBytes();
                    NettyDataBufferFactory nettyDataBufferFactory = new NettyDataBufferFactory(ByteBufAllocator.DEFAULT);
                    DataBuffer buffer = nettyDataBufferFactory.allocateBuffer(bytes.length);
                    buffer.write(bytes);
                    return buffer;
                });
            }*/

            @Override
            public Flux<DataBuffer> getBody() {
                Flux<DataBuffer> body = super.getBody();
                return body.buffer().map(dataBuffers -> {
                    DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
                    DataBuffer join = dataBufferFactory.join(dataBuffers);
                    byte[] content = new byte[join.readableByteCount()];
                    join.read(content);
                    DataBufferUtils.release(join);
                    String bodyStr = new String(content, StandardCharsets.UTF_8);

                    try {
                        JSONObject json = JSON.parseObject(bodyStr);
                        String bpmnXml = null;
                        if (json.containsKey("bpmnXml")) {
                            bpmnXml = json.getString("bpmnXml");
                        }

                        for (String key : json.keySet()) {
                            if ("bpmnXml".equals(key)) continue;
//                            StringUtils.equals("bpmnXml",key);

                            Object value = json.getObject(key, Object.class);
                            if (value instanceof String) {
                                json.put(key, EscapeUtil.clean((String) value));
                            } else if (value instanceof JSONObject) {
                                // 如果是嵌套 JSON，递归清洗
                                deepClean((JSONObject) value);
                            }
                            // 其他类型（数字、null、数组等）不做清洗
                        }

                        if (bpmnXml != null) {
                            json.put("bpmnXml", bpmnXml);
                        }

                        bodyStr = json.toJSONString();

                    } catch (Exception e) {
                        // 日志可选：log.warn("XSS清洗失败: {}", e.getMessage());
                        // 失败则返回原始 bodyStr，不影响正常接口
                    }

                    byte[] bytes = bodyStr.getBytes(StandardCharsets.UTF_8);
                    NettyDataBufferFactory nettyDataBufferFactory = new NettyDataBufferFactory(ByteBufAllocator.DEFAULT);
                    DataBuffer buffer = nettyDataBufferFactory.allocateBuffer(bytes.length);
                    buffer.write(bytes);
                    return buffer;
                });
            }

            private void deepClean(JSONObject json) {
                for (String key : json.keySet()) {
                    if ("bpmnXml".equals(key)) continue;

                    Object value = json.getObject(key, Object.class);
                    if (value instanceof String) {
                        json.put(key, EscapeUtil.clean((String) value));
                    } else if (value instanceof JSONObject) {
                        deepClean((JSONObject) value); // 递归清洗
                    }
                    // 其他类型不做处理
                }
            }

            @Override
            public HttpHeaders getHeaders() {
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.putAll(super.getHeaders());
                // 由于修改了请求体的body，导致content-length长度不确定，因此需要删除原先的content-length
                httpHeaders.remove(HttpHeaders.CONTENT_LENGTH);
                httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
                return httpHeaders;
            }
        };
        return serverHttpRequestDecorator;
    }

    /**
     * 是否是Json请求
     *
     * @param exchange HTTP请求
     */
    public boolean isJsonRequest(ServerWebExchange exchange) {
        String header = exchange.getRequest().getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);
        return StrUtil.startWithIgnoreCase(header, MediaType.APPLICATION_JSON_VALUE);
    }

    @Override
    public int getOrder() {
        return -100;
    }
}
