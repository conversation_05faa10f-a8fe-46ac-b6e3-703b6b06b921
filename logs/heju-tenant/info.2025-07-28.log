09:05:50.509 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:51.626 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a431999-03af-499d-baab-a7c379000d16_config-0
09:05:51.701 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:51.735 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:51.745 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:51.754 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:51.763 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:51.776 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:51.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:51.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002ac013b7d80
09:05:51.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002ac013b8000
09:05:51.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:51.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:51.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:53.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664752753_127.0.0.1_11186
09:05:53.015 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Notify connected event to listeners.
09:05:53.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:53.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a431999-03af-499d-baab-a7c379000d16_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002ac014f20a0
09:05:53.290 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:03.423 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:06:03.425 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:03.426 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:03.789 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:05.364 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:06:05.366 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:06:05.367 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:06:11.174 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:06:17.752 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 193243e1-53e1-4286-9642-************
09:06:17.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] RpcClient init label, labels = {module=naming, source=sdk}
09:06:17.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:17.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:17.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:17.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:17.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Success to connect to server [localhost:8848] on start up, connectionId = 1753664777761_127.0.0.1_11346
09:06:17.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:17.876 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Notify connected event to listeners.
09:06:17.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002ac014f20a0
09:06:17.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:06:17.969 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:06:18.136 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 28.584 seconds (JVM running for 30.768)
09:06:18.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:06:18.154 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:06:18.155 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:06:18.406 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:06:18.419 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:11:41.872 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:11:50.913 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:11:50.914 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 14
11:57:32.028 [nacos-grpc-client-executor-2061] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:57:32.057 [nacos-grpc-client-executor-2061] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:59:25.710 [nacos-grpc-client-executor-2085] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 21
11:59:25.726 [nacos-grpc-client-executor-2085] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 21
15:45:52.364 [nacos-grpc-client-executor-4799] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 23
15:45:52.384 [nacos-grpc-client-executor-4799] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 23
15:46:31.639 [nacos-grpc-client-executor-4807] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 25
15:46:31.655 [nacos-grpc-client-executor-4807] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 25
17:28:46.405 [nacos-grpc-client-executor-6033] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 28
17:28:46.420 [nacos-grpc-client-executor-6033] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 28
17:29:12.240 [nacos-grpc-client-executor-6039] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Receive server push request, request = NotifySubscriberRequest, requestId = 31
17:29:12.248 [nacos-grpc-client-executor-6039] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [193243e1-53e1-4286-9642-************] Ack server push request, request = NotifySubscriberRequest, requestId = 31
19:28:45.109 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:45.123 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:45.457 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:45.457 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2dd927c1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:45.457 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753664777761_127.0.0.1_11346
19:28:45.459 [nacos-grpc-client-executor-7475] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753664777761_127.0.0.1_11346]Ignore complete event,isRunning:false,isAbandon=false
19:28:45.463 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@61bc3836[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 7476]
19:28:45.631 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:28:45.639 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:28:45.641 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:28:45.641 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
