10:18:47.098 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:47.929 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0
10:18:48.006 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:48.037 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:48.049 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:48.061 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:48.072 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:48.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:48.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:48.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001a2213b7d80
10:18:48.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a2213b8000
10:18:48.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:48.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:48.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:48.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:48.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:48.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:48.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a2214c8000
10:18:48.985 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:18:49.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:49.363 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:49.678 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:50.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:50.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:51.078 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:51.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:51.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:52.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:53.713 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:54.384 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:18:54.385 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:18:54.385 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:18:54.746 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:18:54.812 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:55.550 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:18:55.551 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:18:55.552 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:18:56.030 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:57.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:58.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:59.323 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:19:00.255 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:01.820 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:02.800 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5089ead9-b4e6-4dad-8d76-ebf1982fda3c
10:19:02.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] RpcClient init label, labels = {module=naming, source=sdk}
10:19:02.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:02.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:02.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:02.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:02.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:02.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:02.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:02.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:19:02.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a2214c8000
10:19:02.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:03.167 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:19:03.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:03.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:03.500 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:03.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:04.155 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:19:04.155 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@135fb22d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:19:04.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5089ead9-b4e6-4dad-8d76-ebf1982fda3c] Client is shutdown, stop reconnect to server
10:19:04.157 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@461e0652[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
10:19:04.161 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1dc7b045-3f8f-456d-a3a2-5c42228b65cc
10:19:04.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] RpcClient init label, labels = {module=naming, source=sdk}
10:19:04.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:04.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:04.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:04.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:04.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:04.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:04.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:04.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:19:04.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a2214c8000
10:19:04.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:04.564 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:19:04.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:04.570 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:19:04.589 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:19:04.590 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:19:04.615 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
10:19:04.616 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:19:04.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
10:19:04.652 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
10:19:04.879 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:05.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dda9ae3b-6b32-4107-b0e0-c8590e4570c5_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:05.286 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1dc7b045-3f8f-456d-a3a2-5c42228b65cc] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:20:17.693 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:20:18.455 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8aca333d-1872-4af8-ab39-7553e04a1008_config-0
10:20:18.512 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
10:20:18.543 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:20:18.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:20:18.562 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:20:18.572 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:20:18.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:20:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:20:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000187813bf8e0
10:20:18.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000187813bfb00
10:20:18.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:20:18.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:20:18.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:19.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752978019432_127.0.0.1_1567
10:20:19.669 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Notify connected event to listeners.
10:20:19.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:19.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aca333d-1872-4af8-ab39-7553e04a1008_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000187814f9450
10:20:19.863 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:20:23.651 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:20:23.652 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:20:23.652 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:20:23.867 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:20:24.714 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:20:24.716 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:20:24.716 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:20:28.336 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:20:31.498 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0577816b-e6c8-4b0c-888c-38f93957aa3b
10:20:31.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] RpcClient init label, labels = {module=naming, source=sdk}
10:20:31.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:20:31.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:20:31.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:20:31.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:31.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Success to connect to server [localhost:8848] on start up, connectionId = 1752978031515_127.0.0.1_1610
10:20:31.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:31.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Notify connected event to listeners.
10:20:31.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000187814f9450
10:20:31.681 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:20:31.718 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:20:31.859 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 14.788 seconds (JVM running for 15.826)
10:20:31.871 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:20:31.873 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:20:31.874 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:20:32.051 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:20:32.219 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:20:32.240 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:23:07.877 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Receive server push request, request = NotifySubscriberRequest, requestId = 13
10:23:07.877 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0577816b-e6c8-4b0c-888c-38f93957aa3b] Ack server push request, request = NotifySubscriberRequest, requestId = 13
13:31:49.968 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:31:49.971 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:31:50.310 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:31:50.310 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7fa47bc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:31:50.310 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752978031515_127.0.0.1_1610
13:31:50.313 [nacos-grpc-client-executor-1594] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752978031515_127.0.0.1_1610]Ignore complete event,isRunning:false,isAbandon=false
13:31:50.316 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d2deeae[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1595]
13:31:50.470 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:31:50.473 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:31:50.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:31:50.481 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:32:26.674 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:32:27.219 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0
13:32:27.257 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
13:32:27.273 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
13:32:27.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
13:32:27.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:32:27.298 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:32:27.303 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
13:32:27.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:32:27.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002ab583bfd80
13:32:27.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002ab583c0000
13:32:27.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:32:27.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:32:27.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:32:27.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752989547763_127.0.0.1_7127
13:32:27.942 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Notify connected event to listeners.
13:32:27.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:32:27.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6414bc83-2fb3-4f67-b001-087f0f7a69a5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002ab584fa0a0
13:32:28.044 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:32:30.027 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:32:30.027 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:32:30.027 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:32:30.135 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:32:30.684 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:32:30.685 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:32:30.686 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:32:32.448 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:32:34.306 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c6bf6c9c-9a78-45a8-b047-8088eabfa7e3
13:32:34.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] RpcClient init label, labels = {module=naming, source=sdk}
13:32:34.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:32:34.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:32:34.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:32:34.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:32:34.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Success to connect to server [localhost:8848] on start up, connectionId = 1752989554315_127.0.0.1_7136
13:32:34.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:32:34.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002ab584fa0a0
13:32:34.438 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Notify connected event to listeners.
13:32:34.478 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:32:34.500 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
13:32:34.582 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 8.344 seconds (JVM running for 9.139)
13:32:34.592 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
13:32:34.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
13:32:34.596 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
13:32:34.981 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Receive server push request, request = NotifySubscriberRequest, requestId = 24
13:32:34.996 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Ack server push request, request = NotifySubscriberRequest, requestId = 24
13:32:35.092 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:51:01.478 [nacos-grpc-client-executor-236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Receive server push request, request = NotifySubscriberRequest, requestId = 30
13:51:01.480 [nacos-grpc-client-executor-236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Ack server push request, request = NotifySubscriberRequest, requestId = 30
13:55:06.395 [nacos-grpc-client-executor-285] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Receive server push request, request = NotifySubscriberRequest, requestId = 31
13:55:06.411 [nacos-grpc-client-executor-285] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Ack server push request, request = NotifySubscriberRequest, requestId = 31
13:55:26.148 [nacos-grpc-client-executor-289] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Receive server push request, request = NotifySubscriberRequest, requestId = 36
13:55:26.193 [nacos-grpc-client-executor-289] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bf6c9c-9a78-45a8-b047-8088eabfa7e3] Ack server push request, request = NotifySubscriberRequest, requestId = 36
17:57:56.822 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:56.836 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:57.180 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:57.181 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d94ed88[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:57.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752989554315_127.0.0.1_7136
17:57:57.186 [nacos-grpc-client-executor-3204] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752989554315_127.0.0.1_7136]Ignore complete event,isRunning:false,isAbandon=false
17:57:57.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ffb102c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3205]
17:57:57.399 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:57:57.410 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:57:57.412 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:57:57.412 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
