09:15:23.257 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:15:25.354 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8a5879a9-730a-497b-9709-5455585d58c9_config-0
09:15:25.558 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 93 ms to scan 1 urls, producing 3 keys and 6 values 
09:15:25.642 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 4 keys and 9 values 
09:15:25.692 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 10 values 
09:15:25.722 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
09:15:25.748 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:15:25.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 2 keys and 8 values 
09:15:25.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:25.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002648939cfb8
09:15:25.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002648939d1d8
09:15:25.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:25.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:25.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:27.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754356527209_127.0.0.1_8775
09:15:27.501 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Notify connected event to listeners.
09:15:27.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:27.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a5879a9-730a-497b-9709-5455585d58c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000026489514f98
09:15:27.788 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:15:37.094 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:15:37.096 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:15:37.096 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:15:37.568 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:39.315 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:15:39.318 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:15:39.320 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:15:45.122 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:50.207 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 17e30368-2c23-42e4-abd7-a7d4f8732c5f
09:15:50.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] RpcClient init label, labels = {module=naming, source=sdk}
09:15:50.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:50.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:50.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:50.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:50.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Success to connect to server [localhost:8848] on start up, connectionId = 1754356550221_127.0.0.1_9087
09:15:50.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:50.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Notify connected event to listeners.
09:15:50.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000026489514f98
09:15:50.419 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:15:50.477 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:15:50.805 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 28.885 seconds (JVM running for 41.666)
09:15:50.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:15:50.840 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:15:50.842 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:15:50.895 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:15:50.923 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:25:10.258 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:16.101 [nacos-grpc-client-executor-124] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:25:16.102 [nacos-grpc-client-executor-124] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 14
14:32:56.324 [nacos-grpc-client-executor-3820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 20
14:32:56.341 [nacos-grpc-client-executor-3820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 20
14:33:42.776 [nacos-grpc-client-executor-3829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 24
14:33:42.790 [nacos-grpc-client-executor-3829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 24
14:38:16.400 [nacos-grpc-client-executor-3884] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 29
14:38:16.411 [nacos-grpc-client-executor-3884] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 29
14:38:41.486 [nacos-grpc-client-executor-3889] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:38:41.497 [nacos-grpc-client-executor-3889] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:44:16.839 [nacos-grpc-client-executor-3956] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:44:16.850 [nacos-grpc-client-executor-3956] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:44:36.545 [nacos-grpc-client-executor-3960] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:44:36.558 [nacos-grpc-client-executor-3960] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 42
15:04:59.980 [nacos-grpc-client-executor-4206] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 47
15:04:59.990 [nacos-grpc-client-executor-4206] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 47
15:05:18.793 [nacos-grpc-client-executor-4210] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 51
15:05:18.808 [nacos-grpc-client-executor-4210] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 51
16:16:59.192 [nacos-grpc-client-executor-5070] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 57
16:16:59.211 [nacos-grpc-client-executor-5070] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 57
16:17:34.711 [nacos-grpc-client-executor-5077] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 62
16:17:34.727 [nacos-grpc-client-executor-5077] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 62
17:29:13.317 [nacos-grpc-client-executor-5935] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 66
17:29:13.332 [nacos-grpc-client-executor-5935] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 66
17:29:39.262 [nacos-grpc-client-executor-5940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 71
17:29:39.276 [nacos-grpc-client-executor-5940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 71
17:34:13.027 [nacos-grpc-client-executor-5995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 75
17:34:13.052 [nacos-grpc-client-executor-5995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 75
17:34:34.200 [nacos-grpc-client-executor-5999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 80
17:34:34.208 [nacos-grpc-client-executor-5999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 80
17:36:17.343 [nacos-grpc-client-executor-6020] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 84
17:36:17.347 [nacos-grpc-client-executor-6020] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 84
17:36:44.782 [nacos-grpc-client-executor-6026] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 89
17:36:44.798 [nacos-grpc-client-executor-6026] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 89
17:44:20.964 [nacos-grpc-client-executor-6117] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 93
17:44:20.992 [nacos-grpc-client-executor-6117] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 93
17:44:51.420 [nacos-grpc-client-executor-6123] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 97
17:44:51.433 [nacos-grpc-client-executor-6123] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 97
17:45:53.309 [nacos-grpc-client-executor-6136] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 102
17:45:53.326 [nacos-grpc-client-executor-6136] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 102
17:46:11.703 [nacos-grpc-client-executor-6140] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 107
17:46:11.721 [nacos-grpc-client-executor-6140] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 107
17:48:47.126 [nacos-grpc-client-executor-6171] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 111
17:48:47.145 [nacos-grpc-client-executor-6171] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 111
17:49:08.862 [nacos-grpc-client-executor-6178] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Receive server push request, request = NotifySubscriberRequest, requestId = 115
17:49:08.880 [nacos-grpc-client-executor-6178] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17e30368-2c23-42e4-abd7-a7d4f8732c5f] Ack server push request, request = NotifySubscriberRequest, requestId = 115
20:24:48.818 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:24:48.828 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@75d056e9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754356550221_127.0.0.1_9087
20:24:49.162 [nacos-grpc-client-executor-8046] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754356550221_127.0.0.1_9087]Ignore complete event,isRunning:false,isAbandon=false
20:24:49.167 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@516350f4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 8047]
20:24:49.314 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:24:49.322 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:24:49.322 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:24:49.324 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
