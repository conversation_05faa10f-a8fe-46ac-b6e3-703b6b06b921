09:15:57.998 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:15:59.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 784749fd-64ca-4bd8-97b0-c98a08041978_config-0
09:15:59.901 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 72 ms to scan 1 urls, producing 3 keys and 6 values 
09:15:59.950 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 4 keys and 9 values 
09:15:59.973 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:15:59.997 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 5 values 
09:16:00.028 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 1 keys and 7 values 
09:16:00.045 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:16:00.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:16:00.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001af3439f268
09:16:00.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001af3439f488
09:16:00.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:16:00.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:16:00.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.788 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:01.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:01.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001af344e9650
09:16:01.920 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.137 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.457 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.881 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.399 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.862 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:16:04.023 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:04.739 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:05.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:06.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:07.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:08.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:10.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:10.402 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:16:10.404 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:16:10.404 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:16:10.804 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:16:11.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:13.261 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.678 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:18.454 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:20.329 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:22.338 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:24.408 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:26.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:28.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:31.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [784749fd-64ca-4bd8-97b0-c98a08041978_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:32.850 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:16:32.931 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:18:36.197 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:37.194 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0
09:18:37.278 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:37.320 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:37.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:37.346 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:37.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:37.371 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:37.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:37.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001bf0139f8e0
09:18:37.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bf0139fb00
09:18:37.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:37.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:37.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:38.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752110318464_127.0.0.1_4238
09:18:38.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Notify connected event to listeners.
09:18:38.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:38.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd88957-d9ba-431f-8c75-ccee0fa3c9f5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001bf01519a90
09:18:39.044 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:43.686 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:18:43.687 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:43.687 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:43.991 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:45.350 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:18:45.352 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:18:45.352 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:52.632 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:56.915 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 942aa0bc-4ea6-45fd-8b59-2cff85c22a7c
09:18:56.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] RpcClient init label, labels = {module=naming, source=sdk}
09:18:56.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:56.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:56.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:56.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:57.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Success to connect to server [localhost:8848] on start up, connectionId = 1752110336931_127.0.0.1_4380
09:18:57.053 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Notify connected event to listeners.
09:18:57.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:57.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001bf01519a90
09:18:57.109 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:18:57.142 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:18:57.286 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 22.036 seconds (JVM running for 23.311)
09:18:57.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:18:57.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:18:57.308 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:18:57.623 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:18:57.641 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:18:57.777 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:35:51.673 [nacos-grpc-client-executor-217] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:35:51.673 [nacos-grpc-client-executor-217] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [942aa0bc-4ea6-45fd-8b59-2cff85c22a7c] Ack server push request, request = NotifySubscriberRequest, requestId = 13
13:38:02.243 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:02.251 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:02.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:02.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3523ae9f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:02.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752110336931_127.0.0.1_4380
13:38:02.586 [nacos-grpc-client-executor-3119] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752110336931_127.0.0.1_4380]Ignore complete event,isRunning:false,isAbandon=false
13:38:02.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@72a4a630[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3120]
13:38:02.769 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:38:02.769 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:38:02.792 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:38:02.792 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:44:57.594 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:45:01.423 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0
13:45:01.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
13:45:01.559 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
13:45:01.592 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 10 values 
13:45:01.605 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
13:45:01.620 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
13:45:01.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
13:45:01.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:45:01.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000023f9a3cfd80
13:45:01.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023f9a3d0000
13:45:01.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:45:01.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:45:01.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:04.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752126304036_127.0.0.1_10401
13:45:04.290 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Notify connected event to listeners.
13:45:04.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:04.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ba2ee7f-9d08-49c0-9f18-ea035a3470b5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023f9a509ca0
13:45:04.459 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:45:14.829 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:45:14.829 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:45:14.833 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:45:15.332 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:45:17.439 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:45:17.442 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:45:17.443 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:45:24.658 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:45:30.559 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d00ba195-0408-459c-af6d-d905c7b03977
13:45:30.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] RpcClient init label, labels = {module=naming, source=sdk}
13:45:30.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:45:30.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:45:30.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:45:30.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:30.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Success to connect to server [localhost:8848] on start up, connectionId = 1752126330574_127.0.0.1_10471
13:45:30.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:30.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023f9a509ca0
13:45:30.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Notify connected event to listeners.
13:45:30.774 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:45:30.824 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
13:45:31.055 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 34.15 seconds (JVM running for 35.325)
13:45:31.074 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
13:45:31.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
13:45:31.099 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
13:45:31.289 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Receive server push request, request = NotifySubscriberRequest, requestId = 25
13:45:31.309 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d00ba195-0408-459c-af6d-d905c7b03977] Ack server push request, request = NotifySubscriberRequest, requestId = 25
13:45:31.409 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:37.933 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:37.935 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6be8388c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752126330574_127.0.0.1_10471
14:20:38.263 [nacos-grpc-client-executor-431] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752126330574_127.0.0.1_10471]Ignore complete event,isRunning:false,isAbandon=false
14:20:38.265 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4e6db6d7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 432]
14:20:38.383 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:20:38.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:20:38.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:20:38.383 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:31:41.900 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:42.802 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0
14:31:42.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:42.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:42.904 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:42.911 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:42.929 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:42.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:42.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:42.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002253b39fd80
14:31:42.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002253b3a0000
14:31:42.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:42.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:42.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:44.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752129103835_127.0.0.1_1653
14:31:44.084 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Notify connected event to listeners.
14:31:44.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:44.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81fb7d4e-7600-4583-bf8b-a7bb95bf4b69_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002253b51abd8
14:31:44.226 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:48.151 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:31:48.156 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:48.156 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:31:48.341 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:49.300 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:31:49.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:31:49.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:31:52.613 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:31:55.360 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f5deae99-3ef0-43a1-a5d3-056612caa15d
14:31:55.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] RpcClient init label, labels = {module=naming, source=sdk}
14:31:55.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:55.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:55.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:55.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:55.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Success to connect to server [localhost:8848] on start up, connectionId = 1752129115374_127.0.0.1_1712
14:31:55.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:55.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Notify connected event to listeners.
14:31:55.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002253b51abd8
14:31:55.548 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:31:55.584 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
14:31:55.720 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 15.142 seconds (JVM running for 16.405)
14:31:55.732 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:31:55.752 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:31:55.752 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:31:56.029 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Receive server push request, request = NotifySubscriberRequest, requestId = 41
14:31:56.046 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5deae99-3ef0-43a1-a5d3-056612caa15d] Ack server push request, request = NotifySubscriberRequest, requestId = 41
14:31:56.125 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:58:53.179 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:58:53.186 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:58:53.529 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:58:53.529 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@435e437f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:58:53.530 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752129115374_127.0.0.1_1712
14:58:53.533 [nacos-grpc-client-executor-334] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752129115374_127.0.0.1_1712]Ignore complete event,isRunning:false,isAbandon=false
14:58:53.536 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f2f386a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 335]
14:58:53.685 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:58:53.688 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:58:53.697 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:58:53.697 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:21:18.275 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:21:18.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a80f9cc5-a756-4775-af3f-d95999276937_config-0
16:21:18.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
16:21:18.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
16:21:18.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
16:21:18.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:21:18.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
16:21:19.003 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
16:21:19.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:21:19.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000212e439f8e0
16:21:19.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000212e439fb00
16:21:19.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:21:19.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:21:19.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:21:19.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752135679498_127.0.0.1_9905
16:21:19.662 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Notify connected event to listeners.
16:21:19.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:21:19.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a80f9cc5-a756-4775-af3f-d95999276937_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000212e4519948
16:21:19.746 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:21:21.854 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:21:21.854 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:21:21.855 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:21:21.970 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:21:22.779 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:21:22.781 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:21:22.781 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:21:24.728 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:21:26.637 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc9ff345-cec4-4d84-a9e6-233157b359ca
16:21:26.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] RpcClient init label, labels = {module=naming, source=sdk}
16:21:26.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:21:26.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:21:26.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:21:26.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:21:26.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Success to connect to server [localhost:8848] on start up, connectionId = 1752135686648_127.0.0.1_9919
16:21:26.758 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Notify connected event to listeners.
16:21:26.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:21:26.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000212e4519948
16:21:26.798 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:21:26.819 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
16:21:26.900 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 9.18 seconds (JVM running for 10.378)
16:21:26.907 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
16:21:26.909 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
16:21:26.910 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
16:21:27.158 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:21:27.317 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Receive server push request, request = NotifySubscriberRequest, requestId = 56
16:21:27.333 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Ack server push request, request = NotifySubscriberRequest, requestId = 56
19:29:39.846 [nacos-grpc-client-executor-2268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Receive server push request, request = NotifySubscriberRequest, requestId = 80
19:29:39.847 [nacos-grpc-client-executor-2268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc9ff345-cec4-4d84-a9e6-233157b359ca] Ack server push request, request = NotifySubscriberRequest, requestId = 80
20:41:31.058 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:31.064 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:31.387 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:31.388 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48b0ec61[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:31.388 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752135686648_127.0.0.1_9919
20:41:31.396 [nacos-grpc-client-executor-3132] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752135686648_127.0.0.1_9919]Ignore complete event,isRunning:false,isAbandon=false
20:41:31.399 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@360c7525[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3133]
20:41:31.554 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:41:31.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:41:31.563 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:41:31.563 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
