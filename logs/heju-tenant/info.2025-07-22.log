09:18:24.032 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:25.120 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a54eae88-19b8-4e45-bd9d-1744c400344f_config-0
09:18:25.183 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:25.207 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:25.224 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:25.229 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:25.246 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:25.257 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:25.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:25.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000019f0139d500
09:18:25.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019f0139d720
09:18:25.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:25.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:25.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:26.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753147106172_127.0.0.1_13178
09:18:26.401 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Notify connected event to listeners.
09:18:26.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:26.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019f01515418
09:18:26.564 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:30.570 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:18:30.571 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:30.572 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:31.080 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:33.115 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:18:33.117 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:18:33.118 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:37.222 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:40.366 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 540dfe1f-e1b2-4b3c-ba89-f7cff7459300
09:18:40.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] RpcClient init label, labels = {module=naming, source=sdk}
09:18:40.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:40.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:40.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:40.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:40.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Success to connect to server [localhost:8848] on start up, connectionId = 1753147120383_127.0.0.1_13335
09:18:40.505 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Notify connected event to listeners.
09:18:40.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:40.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019f01515418
09:18:40.545 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:18:40.597 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:18:40.779 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.624 seconds (JVM running for 24.457)
09:18:40.795 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:18:40.795 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:18:40.800 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:18:41.134 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:18:41.134 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:23:45.016 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:23:51.667 [nacos-grpc-client-executor-79] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:23:51.670 [nacos-grpc-client-executor-79] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Ack server push request, request = NotifySubscriberRequest, requestId = 14
20:39:57.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:39:57.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:39:57.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.516 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.735 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.738 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.471 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.004 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.640 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.653 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:00.371 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:00.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:01.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:01.215 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:02.109 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:02.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:03.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:03.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:04.242 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:04.257 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:05.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:05.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:06.767 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:06.781 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:08.183 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:08.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.697 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a54eae88-19b8-4e45-bd9d-1744c400344f_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.936 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:40:10.258 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:40:10.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:40:10.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@57e9a51[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:40:10.587 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753147120383_127.0.0.1_13335
20:40:10.587 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [540dfe1f-e1b2-4b3c-ba89-f7cff7459300] Client is shutdown, stop reconnect to server
20:40:10.587 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d72a763[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 8207]
20:40:10.760 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:40:10.763 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:40:10.764 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:40:10.764 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
