09:43:52.935 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:43:55.028 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e388e21e-3840-47e7-9412-ccaff88236d4_config-0
09:43:55.201 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 89 ms to scan 1 urls, producing 3 keys and 6 values 
09:43:55.291 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 4 keys and 9 values 
09:43:55.340 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 3 keys and 10 values 
09:43:55.372 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:43:55.408 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 1 keys and 7 values 
09:43:55.433 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
09:43:55.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:43:55.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002314239da58
09:43:55.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002314239dc78
09:43:55.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:43:55.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:43:55.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:43:57.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753926237407_127.0.0.1_6003
09:43:57.814 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Notify connected event to listeners.
09:43:57.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:43:57.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e388e21e-3840-47e7-9412-ccaff88236d4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023142515f18
09:43:58.169 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:10.954 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:44:10.956 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:44:10.956 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:11.436 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:13.524 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:44:13.529 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:44:13.529 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:44:24.076 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:44:30.608 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of afcdfa7a-f786-4abd-973d-0a502f8a8b77
09:44:30.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] RpcClient init label, labels = {module=naming, source=sdk}
09:44:30.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:30.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:30.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:30.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:30.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Success to connect to server [localhost:8848] on start up, connectionId = 1753926270622_127.0.0.1_6514
09:44:30.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:30.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023142515f18
09:44:30.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Notify connected event to listeners.
09:44:31.116 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:44:31.264 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:44:31.439 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:44:31.457 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [afcdfa7a-f786-4abd-973d-0a502f8a8b77] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:44:31.494 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 39.911 seconds (JVM running for 52.125)
09:44:31.529 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:44:31.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:44:31.537 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:44:56.074 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:59:26.743 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:59:26.748 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:59:27.084 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:59:27.084 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2e0daf1a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:59:27.084 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753926270622_127.0.0.1_6514
14:59:27.086 [nacos-grpc-client-executor-3785] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753926270622_127.0.0.1_6514]Ignore complete event,isRunning:false,isAbandon=false
14:59:27.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@695777bf[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3786]
14:59:27.123 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:59:27.128 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:59:27.139 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:59:27.139 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:07:19.413 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:21.361 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0
15:07:21.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 69 ms to scan 1 urls, producing 3 keys and 6 values 
15:07:21.535 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
15:07:21.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
15:07:21.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
15:07:21.587 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
15:07:21.602 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
15:07:21.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:21.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002731e3b8200
15:07:21.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002731e3b8420
15:07:21.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:21.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:21.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:23.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753945643466_127.0.0.1_9284
15:07:23.767 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Notify connected event to listeners.
15:07:23.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:23.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9444f6f6-db0a-451f-85ce-29ffc58d00ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002731e4f26e0
15:07:23.976 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:29.420 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:07:29.421 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:07:29.421 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:07:29.724 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:07:30.898 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:07:30.900 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:07:30.901 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:07:35.832 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:07:39.118 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cece4c1-33fb-4802-9955-9135c7236a6c
15:07:39.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] RpcClient init label, labels = {module=naming, source=sdk}
15:07:39.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:39.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:39.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:39.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:39.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Success to connect to server [localhost:8848] on start up, connectionId = 1753945659136_127.0.0.1_9345
15:07:39.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:39.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Notify connected event to listeners.
15:07:39.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002731e4f26e0
15:07:39.290 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:07:39.315 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
15:07:39.445 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.978 seconds (JVM running for 25.03)
15:07:39.457 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:07:39.459 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:07:39.461 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:07:39.779 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Receive server push request, request = NotifySubscriberRequest, requestId = 44
15:07:39.795 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cece4c1-33fb-4802-9955-9135c7236a6c] Ack server push request, request = NotifySubscriberRequest, requestId = 44
20:46:46.139 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:46.144 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:46.483 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:46.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7fdce17e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:46.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753945659136_127.0.0.1_9345
20:46:46.486 [nacos-grpc-client-executor-4076] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753945659136_127.0.0.1_9345]Ignore complete event,isRunning:false,isAbandon=false
20:46:46.490 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 4077]
20:46:46.528 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:46:46.534 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:46:46.547 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:46:46.547 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
