09:04:18.906 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:04:20.552 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9f1db7f5-2479-4789-b35e-89ee496a686e_config-0
09:04:20.665 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 51 ms to scan 1 urls, producing 3 keys and 6 values 
09:04:20.728 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 4 keys and 9 values 
09:04:20.748 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:04:20.766 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:04:20.782 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:04:20.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:04:20.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:20.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000248543cfd80
09:04:20.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000248543d0000
09:04:20.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:20.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:20.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:22.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059862588_127.0.0.1_1647
09:04:22.895 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Notify connected event to listeners.
09:04:22.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:22.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f1db7f5-2479-4789-b35e-89ee496a686e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002485450a0a0
09:04:23.268 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:04:30.128 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:04:30.129 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:04:30.129 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:04:30.438 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:04:32.287 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:04:32.290 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:04:32.291 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:04:36.518 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:04:39.977 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4609263-e81b-4e18-baa5-5e7a896f8310
09:04:39.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] RpcClient init label, labels = {module=naming, source=sdk}
09:04:39.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:04:39.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:04:39.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:04:39.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:40.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Success to connect to server [localhost:8848] on start up, connectionId = 1753059879989_127.0.0.1_1709
09:04:40.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Notify connected event to listeners.
09:04:40.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:40.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002485450a0a0
09:04:40.169 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:04:40.204 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:04:40.342 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 22.767 seconds (JVM running for 24.939)
09:04:40.357 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:04:40.360 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:04:40.360 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:04:40.682 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:04:40.690 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:04:40.699 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:06:55.762 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:06:55.763 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4609263-e81b-4e18-baa5-5e7a896f8310] Ack server push request, request = NotifySubscriberRequest, requestId = 16
17:57:14.058 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:14.069 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:14.399 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:14.399 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5399daf9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:14.399 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753059879989_127.0.0.1_1709
17:57:14.399 [nacos-grpc-client-executor-6394] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753059879989_127.0.0.1_1709]Ignore complete event,isRunning:false,isAbandon=false
17:57:14.408 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@66937e5d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6395]
17:57:14.557 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:57:14.564 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:57:14.564 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:57:14.564 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
