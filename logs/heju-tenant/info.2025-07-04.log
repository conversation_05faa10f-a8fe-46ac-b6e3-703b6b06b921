09:24:00.890 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:03.388 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0
09:24:03.528 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 72 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:03.597 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:03.640 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:03.669 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:03.701 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:03.722 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:03.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:03.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002049339d7b8
09:24:03.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002049339d9d8
09:24:03.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:03.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:03.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:06.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:06.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:06.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:06.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:06.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000204934ab910
09:24:07.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:07.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:07.612 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:09.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:09.284 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:09.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:10.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:11.707 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:12.737 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:14.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:16.283 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:18.164 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:19.719 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:21.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:21.653 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:24:21.655 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:21.655 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:21.996 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:23.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:23.415 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:24:23.418 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:24:23.418 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:25.149 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:27.074 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:28.102 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:29.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:31.325 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:33.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:34.259 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1
09:24:34.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] RpcClient init label, labels = {module=naming, source=sdk}
09:24:34.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:34.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:34.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:34.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:34.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:34.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:34.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:34.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:34.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000204934ab910
09:24:34.427 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:34.646 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:24:34.651 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:34.971 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:35.408 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:35.657 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:35.659 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@247added[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:35.659 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d7fe300[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:24:35.659 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4442ad9d-3ab5-47c1-b49c-2e05fa0cbcc1] Client is shutdown, stop reconnect to server
09:24:35.663 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d73f63e7-8aa4-4dd1-a96d-0911c665ba90
09:24:35.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] RpcClient init label, labels = {module=naming, source=sdk}
09:24:35.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:35.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:35.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:35.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:35.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:35.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:35.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:35.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:35.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000204934ab910
09:24:35.721 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d2eb9f4-eeea-45ce-825d-f55a41d23320_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:35.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.015 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.041 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:24:36.046 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:24:36.060 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:24:36.060 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:24:36.081 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:24:36.081 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:24:36.115 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:24:36.119 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:24:36.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.751 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73f63e7-8aa4-4dd1-a96d-0911c665ba90] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:15.281 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:16.393 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0
09:25:16.486 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
09:25:16.521 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:25:16.539 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:25:16.557 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:25:16.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:25:16.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:25:16.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:16.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001fd0139eff8
09:25:16.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fd0139f218
09:25:16.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:16.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:16.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:18.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592317764_127.0.0.1_9516
09:25:18.009 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Notify connected event to listeners.
09:25:18.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:18.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4930e75-cca2-4960-8832-77f2bcb5dcaf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001fd01518fb0
09:25:18.263 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:25:23.477 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:25:23.477 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:25:23.478 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:25:23.862 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:25:25.020 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:25:25.022 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:25:25.022 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:25:29.753 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:25:34.003 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8864272a-4022-49f0-8279-bee42cae556d
09:25:34.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] RpcClient init label, labels = {module=naming, source=sdk}
09:25:34.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:34.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:34.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:34.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:34.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Success to connect to server [localhost:8848] on start up, connectionId = 1751592334025_127.0.0.1_9927
09:25:34.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Notify connected event to listeners.
09:25:34.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:34.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001fd01518fb0
09:25:34.235 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:25:34.729 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:25:34.730 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8864272a-4022-49f0-8279-bee42cae556d] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:25:34.892 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:25:34.892 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d44025b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:25:34.893 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592334025_127.0.0.1_9927
09:25:34.898 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@71a18feb[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 6]
09:25:34.901 [grpc-nio-worker-ELG-1-4] INFO  c.a.n.s.i.g.i.AbstractClientStream - [inboundTrailersReceived,391] - Received trailers on closed stream:
 Metadata()
 {2}
09:25:34.904 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 847f7a5e-c711-4e1d-942f-03e5030de771
09:25:34.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] RpcClient init label, labels = {module=naming, source=sdk}
09:25:34.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:34.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:34.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:34.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:35.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Success to connect to server [localhost:8848] on start up, connectionId = 1751592334916_127.0.0.1_9935
09:25:35.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:35.046 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Notify connected event to listeners.
09:25:35.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [847f7a5e-c711-4e1d-942f-03e5030de771] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001fd01518fb0
09:25:35.109 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:25:35.113 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:25:35.124 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:25:35.125 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:25:35.127 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:25:35.128 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:25:35.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:25:35.163 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:28:03.146 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:28:04.121 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0
09:28:04.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
09:28:04.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:28:04.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:28:04.256 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:28:04.276 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:28:04.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:28:04.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:28:04.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000217b239fd80
09:28:04.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000217b23a0000
09:28:04.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:28:04.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:28:04.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:05.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592485347_127.0.0.1_11586
09:28:05.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Notify connected event to listeners.
09:28:05.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:05.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000217b251a0a0
09:28:05.789 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:28:09.835 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:28:09.837 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:09.837 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:28:10.211 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:11.122 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:28:11.124 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:28:11.124 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:28:14.937 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:28:18.709 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c3f47812-3343-4cc9-8f21-80014dc9c9b8
09:28:18.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] RpcClient init label, labels = {module=naming, source=sdk}
09:28:18.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:28:18.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:28:18.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:28:18.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:18.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Success to connect to server [localhost:8848] on start up, connectionId = 1751592498728_127.0.0.1_11715
09:28:18.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:18.861 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Notify connected event to listeners.
09:28:18.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000217b251a0a0
09:28:18.960 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:28:19.008 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:28:19.174 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 16.918 seconds (JVM running for 18.243)
09:28:19.194 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:28:19.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:28:19.198 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:28:19.502 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:28:19.525 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:28:19.658 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:41:47.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Server healthy check fail, currentConnection = 1751592498728_127.0.0.1_11715
11:41:47.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Server healthy check fail, currentConnection = 1751592485347_127.0.0.1_11586
11:41:47.712 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.730 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.860 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Success to connect a server [localhost:8848], connectionId = 1751600507739_127.0.0.1_13710
11:41:47.861 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751592485347_127.0.0.1_11586
11:41:47.861 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592485347_127.0.0.1_11586
11:41:47.878 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Notify disconnected event to listeners
11:41:47.879 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Notify connected event to listeners.
11:41:47.896 [nacos-grpc-client-executor-1614] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751592485347_127.0.0.1_11586]Ignore complete event,isRunning:true,isAbandon=true
11:41:47.964 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Success to connect a server [localhost:8848], connectionId = 1751600507755_127.0.0.1_13711
11:41:47.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Abandon prev connection, server is localhost:8848, connectionId is 1751592498728_127.0.0.1_11715
11:41:47.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592498728_127.0.0.1_11715
11:41:48.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Notify disconnected event to listeners
11:41:48.058 [nacos-grpc-client-executor-1607] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751592498728_127.0.0.1_11715]Ignore complete event,isRunning:true,isAbandon=true
11:41:48.058 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Notify connected event to listeners.
11:41:51.327 [nacos-grpc-client-executor-1610] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:41:51.328 [nacos-grpc-client-executor-1610] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Ack server push request, request = NotifySubscriberRequest, requestId = 29
13:04:24.837 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Server healthy check fail, currentConnection = 1751600507739_127.0.0.1_13710
13:04:24.837 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:31.199 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Server healthy check fail, currentConnection = 1751600507755_127.0.0.1_13711
13:04:31.199 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:54.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Success to connect a server [localhost:8848], connectionId = 1751605494160_127.0.0.1_6550
13:04:54.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751600507739_127.0.0.1_13710
13:04:54.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507739_127.0.0.1_13710
13:04:54.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Notify disconnected event to listeners
13:04:54.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2495d8c-c4a7-4673-b90d-1bcddba9e75f_config-0] Notify connected event to listeners.
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Success to connect a server [localhost:8848], connectionId = 1751605494277_127.0.0.1_6557
13:04:54.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Abandon prev connection, server is localhost:8848, connectionId is 1751600507755_127.0.0.1_13711
13:04:54.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507755_127.0.0.1_13711
13:04:54.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Notify disconnected event to listeners
13:04:54.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Notify connected event to listeners.
13:04:58.907 [nacos-grpc-client-executor-2606] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Receive server push request, request = NotifySubscriberRequest, requestId = 70
13:04:58.975 [nacos-grpc-client-executor-2606] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f47812-3343-4cc9-8f21-80014dc9c9b8] Ack server push request, request = NotifySubscriberRequest, requestId = 70
15:09:03.475 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:03.484 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:03.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:03.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6cad597f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:03.827 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751605494277_127.0.0.1_6557
15:09:03.839 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@19f9b299[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4096]
15:09:03.841 [nacos-grpc-client-executor-4096] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751605494277_127.0.0.1_6557]Ignore complete event,isRunning:false,isAbandon=false
15:09:04.113 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:09:04.126 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:09:04.168 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:09:04.169 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
