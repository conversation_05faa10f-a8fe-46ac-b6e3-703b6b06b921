09:40:18.402 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:19.424 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0
09:40:19.501 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 40 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:19.546 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:19.558 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:19.570 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:19.585 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:19.597 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:19.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:19.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000021bdd3a0200
09:40:19.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000021bdd3a0420
09:40:19.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:19.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:19.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:20.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:20.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:20.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:20.812 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:20.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021bdd4f4430
09:40:20.937 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:21.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:21.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:21.894 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:22.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:23.020 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:23.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:23.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:24.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:25.486 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:26.511 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:27.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:40:27.691 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:40:27.691 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:40:27.708 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:27.959 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:40:28.835 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:40:28.837 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:40:28.837 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:40:29.036 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:30.423 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:31.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:32.941 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:40:33.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:35.260 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:37.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:39.020 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:39.397 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fde8382e-fbf1-4fa9-8e9d-8a2ca1783431
09:40:39.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] RpcClient init label, labels = {module=naming, source=sdk}
09:40:39.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:40:39.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:40:39.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:40:39.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:39.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:39.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:39.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:39.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:39.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021bdd4f4430
09:40:39.573 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:39.784 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:40:39.788 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:40.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:40.524 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:40.784 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:40:40.785 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1f81b020[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:40:40.786 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@56c95789[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:40:40.787 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fde8382e-fbf1-4fa9-8e9d-8a2ca1783431] Client is shutdown, stop reconnect to server
09:40:40.790 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4a8ebd4e-02a5-4015-8b8d-ce43c63e468b
09:40:40.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] RpcClient init label, labels = {module=naming, source=sdk}
09:40:40.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:40:40.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:40:40.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:40:40.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:40.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:40.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:40.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:40.825 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:40.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021bdd4f4430
09:40:40.942 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f22c6c7-a910-4872-a0a7-3ec4737e88d4_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:40.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:41.177 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:41.219 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:40:41.228 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:40:41.255 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:40:41.255 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:40:41.295 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:40:41.295 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:40:41.361 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:40:41.371 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:40:41.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:41.925 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a8ebd4e-02a5-4015-8b8d-ce43c63e468b] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:51.039 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:52.107 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0
09:40:52.195 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:52.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:52.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:52.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:52.278 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:52.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:52.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:52.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001b7e039f8e0
09:40:52.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b7e039fb00
09:40:52.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:52.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:52.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:53.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:53.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:53.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:53.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:53.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b7e051a068
09:40:54.109 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:54.347 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:54.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:55.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:55.653 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:56.002 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:56.284 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:57.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751506857002_127.0.0.1_14857
09:40:57.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c992a66-b728-4fce-afb9-9bd6d07811ac_config-0] Notify connected event to listeners.
09:41:00.724 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:41:00.726 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:41:00.726 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:41:01.026 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:41:01.980 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:41:01.982 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:41:01.982 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:41:06.298 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:41:10.330 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b3de0cda-d432-44a3-9481-7060d05284cd
09:41:10.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] RpcClient init label, labels = {module=naming, source=sdk}
09:41:10.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:41:10.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:41:10.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:41:10.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:10.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506870339_127.0.0.1_14982
09:41:10.486 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Notify connected event to listeners.
09:41:10.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:10.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b7e051a068
09:41:10.567 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:41:10.614 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:41:10.802 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 20.671 seconds (JVM running for 22.105)
09:41:10.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:41:10.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:41:10.823 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:41:11.133 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:41:11.150 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:41:11.304 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:08:32.297 [nacos-grpc-client-executor-1782] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Receive server push request, request = NotifySubscriberRequest, requestId = 114
12:08:32.299 [nacos-grpc-client-executor-1782] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Ack server push request, request = NotifySubscriberRequest, requestId = 114
12:15:51.442 [nacos-grpc-client-executor-1870] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Receive server push request, request = NotifySubscriberRequest, requestId = 127
12:15:51.463 [nacos-grpc-client-executor-1870] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Ack server push request, request = NotifySubscriberRequest, requestId = 127
12:16:23.189 [nacos-grpc-client-executor-1876] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Receive server push request, request = NotifySubscriberRequest, requestId = 134
12:16:23.213 [nacos-grpc-client-executor-1876] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Ack server push request, request = NotifySubscriberRequest, requestId = 134
12:25:09.753 [nacos-grpc-client-executor-1982] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Receive server push request, request = NotifySubscriberRequest, requestId = 140
12:25:09.782 [nacos-grpc-client-executor-1982] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Ack server push request, request = NotifySubscriberRequest, requestId = 140
12:25:41.658 [nacos-grpc-client-executor-1990] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Receive server push request, request = NotifySubscriberRequest, requestId = 144
12:25:41.681 [nacos-grpc-client-executor-1990] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3de0cda-d432-44a3-9481-7060d05284cd] Ack server push request, request = NotifySubscriberRequest, requestId = 144
12:32:13.910 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:13.913 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:14.259 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:14.260 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6597a862[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:14.260 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751506870339_127.0.0.1_14982
12:32:14.261 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3cee3b5f[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2070]
12:32:14.506 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:32:14.513 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:32:14.525 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:32:14.525 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:33:14.053 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:33:15.147 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0
12:33:15.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
12:33:15.279 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
12:33:15.303 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
12:33:15.318 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
12:33:15.334 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:33:15.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:33:15.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:15.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000018ce13b7268
12:33:15.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000018ce13b7488
12:33:15.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:15.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:15.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:16.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517196306_127.0.0.1_11266
12:33:16.539 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Notify connected event to listeners.
12:33:16.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:16.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3b0a35d-4a7d-48cc-8544-18d89eeef151_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018ce14f0fb0
12:33:16.715 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:21.119 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
12:33:21.120 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:33:21.120 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:33:21.349 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:33:22.318 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:33:22.320 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:33:22.320 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:33:26.250 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:33:29.862 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3dd7a93d-0e22-4f04-a05c-9805a107c2f3
12:33:29.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] RpcClient init label, labels = {module=naming, source=sdk}
12:33:29.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:33:29.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:33:29.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:33:29.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:30.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517209880_127.0.0.1_11373
12:33:30.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:30.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Notify connected event to listeners.
12:33:30.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018ce14f0fb0
12:33:30.071 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
12:33:30.110 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
12:33:30.291 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.107 seconds (JVM running for 18.61)
12:33:30.310 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
12:33:30.315 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
12:33:30.316 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
12:33:30.579 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Receive server push request, request = NotifySubscriberRequest, requestId = 161
12:33:30.607 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Ack server push request, request = NotifySubscriberRequest, requestId = 161
12:37:31.556 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:37:33.916 [nacos-grpc-client-executor-58] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Receive server push request, request = NotifySubscriberRequest, requestId = 168
12:37:33.918 [nacos-grpc-client-executor-58] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dd7a93d-0e22-4f04-a05c-9805a107c2f3] Ack server push request, request = NotifySubscriberRequest, requestId = 168
13:33:05.881 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:05.884 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:06.217 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:06.218 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b5442fb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:06.218 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517209880_127.0.0.1_11373
13:33:06.221 [nacos-grpc-client-executor-742] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517209880_127.0.0.1_11373]Ignore complete event,isRunning:false,isAbandon=false
13:33:06.226 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1f91c974[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 743]
13:33:06.390 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:33:06.393 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:33:06.410 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:33:06.410 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:04:02.584 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:03.572 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0
14:04:03.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
14:04:03.685 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:04:03.697 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:04:03.710 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:04:03.731 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
14:04:03.742 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:04:03.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:03.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001df4839fd80
14:04:03.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001df483a0000
14:04:03.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:03.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:03.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:07.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:10.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:13.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:13.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:13.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001df484e9cf0
14:04:15.553 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:04:19.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:20.254 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:04:20.256 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:20.256 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:04:20.513 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:21.370 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:04:21.372 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:04:21.373 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:04:22.819 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:26.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:27.758 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:04:29.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:32.303 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2044c384-2e1f-4781-bb2d-01f2743ed105
14:04:32.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] RpcClient init label, labels = {module=naming, source=sdk}
14:04:32.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:32.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:32.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:32.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:33.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:35.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:36.762 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:38.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:40.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:41.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:41.342 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:41.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001df484e9cf0
14:04:41.691 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:04:42.613 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:04:42.614 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@273cb729[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:04:42.614 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@76603c44[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:04:42.627 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 124b2c63-44b0-4485-b35f-75e55bb606bc
14:04:42.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] RpcClient init label, labels = {module=naming, source=sdk}
14:04:42.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:42.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:42.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:42.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:42.719 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2044c384-2e1f-4781-bb2d-01f2743ed105] Client is shutdown, stop reconnect to server
14:04:44.297 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:45.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:48.210 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:48.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:51.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:51.677 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:51.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [124b2c63-44b0-4485-b35f-75e55bb606bc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001df484e9cf0
14:04:52.038 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:04:52.042 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:04:52.057 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:04:52.057 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:04:52.081 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
14:04:52.081 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:04:52.112 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
14:04:52.117 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
14:04:52.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70a4d15a-0b01-475b-9ad5-9c4ec35b0ac9_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:35.047 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:09:36.007 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0
14:09:36.096 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
14:09:36.128 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:09:36.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:09:36.152 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:09:36.169 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
14:09:36.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:09:36.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:09:36.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000297b139f8e0
14:09:36.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000297b139fb00
14:09:36.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:09:36.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:09:36.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:09:39.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:09:42.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:09:45.972 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:09:45.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:09:45.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000297b14e9678
14:09:48.033 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:09:52.100 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:52.176 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:09:52.176 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:09:52.177 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:09:52.400 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:09:53.216 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:09:53.219 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:09:53.219 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:09:55.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:57.653 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:09:58.650 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:02.070 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:03.302 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 80d1d026-f08b-4e5e-ae65-088f067d09ef
14:10:03.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] RpcClient init label, labels = {module=naming, source=sdk}
14:10:03.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:03.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:03.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:03.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:05.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:06.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:09.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:09.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:12.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:12.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:12.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000297b14e9678
14:10:12.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:10:12.935 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:13.609 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:10:13.609 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@461e0652[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:10:13.610 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3b9d5218[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:10:13.617 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-104a-4c09-93bf-bf6a8c864f4c
14:10:13.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] RpcClient init label, labels = {module=naming, source=sdk}
14:10:13.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:13.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:13.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:13.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:13.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80d1d026-f08b-4e5e-ae65-088f067d09ef] Client is shutdown, stop reconnect to server
14:10:16.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:16.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:19.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:20.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbf83b99-d659-4d5a-ae83-0782df80f36a_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:22.651 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:22.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:22.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-104a-4c09-93bf-bf6a8c864f4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000297b14e9678
14:10:23.025 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:10:23.028 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:10:23.045 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:10:23.046 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:10:23.082 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
14:10:23.083 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:10:23.118 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
14:10:23.125 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
14:28:19.450 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:28:20.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b9219190-d2ee-454b-a412-1999ff4e850b_config-0
14:28:20.489 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
14:28:20.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
14:28:20.545 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:28:20.559 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:28:20.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:28:20.585 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:28:20.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:28:20.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000271a439f8e0
14:28:20.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000271a439fb00
14:28:20.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:28:20.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:28:20.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:24.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:27.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:30.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:30.406 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:30.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000271a44f4430
14:28:32.575 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:28:36.535 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:36.848 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:28:36.849 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:28:36.849 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:28:37.087 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:37.992 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:28:37.993 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:28:37.994 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:28:39.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:42.002 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:28:43.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:46.009 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bbd77761-d44b-455e-ad34-c5b8f4ab6f46
14:28:46.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] RpcClient init label, labels = {module=naming, source=sdk}
14:28:46.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:46.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:46.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:46.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:46.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:49.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:50.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:52.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:53.650 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:55.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:55.274 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:55.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000271a44f4430
14:28:55.615 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:28:56.334 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:28:56.334 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@16ea56ab[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:28:56.335 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@135fb22d[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:28:56.339 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ba08132-5ed8-483e-a537-60dafa774ff2
14:28:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] RpcClient init label, labels = {module=naming, source=sdk}
14:28:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:56.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:56.452 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd77761-d44b-455e-ad34-c5b8f4ab6f46] Client is shutdown, stop reconnect to server
14:28:57.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:59.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:29:01.177 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:29:02.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:29:05.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9219190-d2ee-454b-a412-1999ff4e850b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:29:05.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:29:05.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000271a44f4430
14:29:05.382 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba08132-5ed8-483e-a537-60dafa774ff2] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:29:05.751 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:29:05.757 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:29:05.771 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:29:05.771 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:29:05.789 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
14:29:05.789 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:29:05.817 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
14:29:05.824 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
14:33:39.077 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:40.154 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf12f1b7-a30c-4641-9324-6e363173aa11_config-0
14:33:40.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:40.275 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:40.298 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:40.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:40.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:40.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:40.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:40.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001f6e33b7268
14:33:40.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f6e33b7488
14:33:40.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:40.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:40.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:41.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524421453_127.0.0.1_6369
14:33:41.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Notify connected event to listeners.
14:33:41.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:41.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf12f1b7-a30c-4641-9324-6e363173aa11_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f6e34f0d48
14:33:41.857 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:46.677 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:33:46.678 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:46.679 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:33:46.969 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:47.951 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:33:47.953 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:33:47.954 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:33:52.330 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:33:56.293 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9159fc82-53a3-40d5-b9df-79061eed5bd5
14:33:56.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] RpcClient init label, labels = {module=naming, source=sdk}
14:33:56.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:33:56.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:33:56.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:33:56.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:56.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Success to connect to server [localhost:8848] on start up, connectionId = 1751524436313_127.0.0.1_6518
14:33:56.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:56.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Notify connected event to listeners.
14:33:56.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f6e34f0d48
14:33:56.504 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:33:56.549 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
14:33:56.727 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 18.527 seconds (JVM running for 19.966)
14:33:56.747 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:33:56.753 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:33:56.754 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:33:57.004 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Receive server push request, request = NotifySubscriberRequest, requestId = 173
14:33:57.034 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Ack server push request, request = NotifySubscriberRequest, requestId = 173
14:48:39.346 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:48:41.669 [nacos-grpc-client-executor-184] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Receive server push request, request = NotifySubscriberRequest, requestId = 190
14:48:41.670 [nacos-grpc-client-executor-184] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Ack server push request, request = NotifySubscriberRequest, requestId = 190
15:48:27.647 [nacos-grpc-client-executor-903] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Receive server push request, request = NotifySubscriberRequest, requestId = 226
15:48:27.678 [nacos-grpc-client-executor-903] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Ack server push request, request = NotifySubscriberRequest, requestId = 226
15:48:59.101 [nacos-grpc-client-executor-910] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Receive server push request, request = NotifySubscriberRequest, requestId = 230
15:48:59.126 [nacos-grpc-client-executor-910] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Ack server push request, request = NotifySubscriberRequest, requestId = 230
15:57:29.588 [nacos-grpc-client-executor-1013] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Receive server push request, request = NotifySubscriberRequest, requestId = 235
15:57:29.606 [nacos-grpc-client-executor-1013] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Ack server push request, request = NotifySubscriberRequest, requestId = 235
15:58:46.355 [nacos-grpc-client-executor-1028] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Receive server push request, request = NotifySubscriberRequest, requestId = 243
15:58:46.487 [nacos-grpc-client-executor-1028] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9159fc82-53a3-40d5-b9df-79061eed5bd5] Ack server push request, request = NotifySubscriberRequest, requestId = 243
16:14:21.919 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:14:21.923 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:14:22.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:14:22.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4dbe0164[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:14:22.253 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751524436313_127.0.0.1_6518
16:14:22.254 [nacos-grpc-client-executor-1217] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751524436313_127.0.0.1_6518]Ignore complete event,isRunning:false,isAbandon=false
16:14:22.257 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@652fe2c5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1218]
16:14:22.401 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:14:22.403 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:14:22.409 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:14:22.409 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:51:15.453 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:51:18.276 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb686c70-41a2-4854-a70b-f22f617c2653_config-0
16:51:18.492 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 107 ms to scan 1 urls, producing 3 keys and 6 values 
16:51:18.613 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 4 keys and 9 values 
16:51:18.676 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 10 values 
16:51:18.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 1 keys and 5 values 
16:51:18.769 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 1 keys and 7 values 
16:51:18.832 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 2 keys and 8 values 
16:51:18.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:51:18.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000020a0939da58
16:51:18.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000020a0939dc78
16:51:18.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:51:18.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:51:18.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:23.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532682567_127.0.0.1_3845
16:51:23.086 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Notify connected event to listeners.
16:51:23.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:23.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000020a09515f18
16:51:23.477 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:51:32.472 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:51:32.474 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:51:32.474 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:51:32.887 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:51:34.420 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:51:34.422 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:51:34.422 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:51:41.114 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:51:46.939 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 91e05b79-6559-4010-acd2-e793367e86ac
16:51:46.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] RpcClient init label, labels = {module=naming, source=sdk}
16:51:46.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:51:46.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:51:46.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:51:46.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:47.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Success to connect to server [localhost:8848] on start up, connectionId = 1751532706952_127.0.0.1_3919
16:51:47.086 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Notify connected event to listeners.
16:51:47.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:47.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000020a09515f18
16:51:47.176 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:51:47.248 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
16:51:47.447 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 36.021 seconds (JVM running for 43.197)
16:51:47.465 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
16:51:47.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
16:51:47.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
16:51:47.692 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Receive server push request, request = NotifySubscriberRequest, requestId = 285
16:51:47.718 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Ack server push request, request = NotifySubscriberRequest, requestId = 285
20:17:45.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Server healthy check fail, currentConnection = 1751532682567_127.0.0.1_3845
20:17:45.987 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:17:46.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Success to connect a server [localhost:8848], connectionId = 1751545066023_127.0.0.1_12229
20:17:46.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751532682567_127.0.0.1_3845
20:17:46.218 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532682567_127.0.0.1_3845
20:17:46.282 [nacos-grpc-client-executor-2487] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532682567_127.0.0.1_3845]Ignore complete event,isRunning:false,isAbandon=true
20:17:46.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Notify disconnected event to listeners
20:17:46.361 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb686c70-41a2-4854-a70b-f22f617c2653_config-0] Notify connected event to listeners.
20:24:56.743 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:26:52.531 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Server healthy check fail, currentConnection = 1751532706952_127.0.0.1_3919
20:26:52.532 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:26:53.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Success to connect a server [localhost:8848], connectionId = 1751545613617_127.0.0.1_13440
20:26:53.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Abandon prev connection, server is localhost:8848, connectionId is 1751532706952_127.0.0.1_3919
20:26:53.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532706952_127.0.0.1_3919
20:26:53.822 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Notify disconnected event to listeners
20:26:53.831 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Notify connected event to listeners.
20:26:53.859 [nacos-grpc-client-executor-2584] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532706952_127.0.0.1_3919]Ignore complete event,isRunning:true,isAbandon=true
20:26:56.957 [nacos-grpc-client-executor-2587] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Receive server push request, request = NotifySubscriberRequest, requestId = 324
20:26:56.959 [nacos-grpc-client-executor-2587] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e05b79-6559-4010-acd2-e793367e86ac] Ack server push request, request = NotifySubscriberRequest, requestId = 324
20:46:23.769 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:23.772 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:24.101 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:24.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e27b8aa[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:24.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751545613617_127.0.0.1_13440
20:46:24.108 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4aa50222[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2822]
20:46:24.135 [nacos-grpc-client-executor-2822] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751545613617_127.0.0.1_13440]Ignore complete event,isRunning:false,isAbandon=false
20:46:24.142 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:46:24.145 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:46:24.156 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:46:24.156 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
