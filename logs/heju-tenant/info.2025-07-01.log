13:26:12.287 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:26:15.833 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0
13:26:16.020 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 106 ms to scan 1 urls, producing 3 keys and 6 values 
13:26:16.093 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 4 keys and 9 values 
13:26:16.142 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 10 values 
13:26:16.168 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
13:26:16.194 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
13:26:16.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
13:26:16.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:26:16.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001620339cfb8
13:26:16.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001620339d1d8
13:26:16.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:26:16.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:26:16.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:26:19.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751347578677_127.0.0.1_11277
13:26:19.054 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Notify connected event to listeners.
13:26:19.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:26:19.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000016203514f98
13:26:19.393 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:26:28.098 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:26:28.099 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:26:28.100 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:26:28.751 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:26:30.794 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:26:30.797 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:26:30.797 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:26:41.312 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:26:50.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a3331cc8-2b39-452f-9a84-d2442b42efde
13:26:50.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] RpcClient init label, labels = {module=naming, source=sdk}
13:26:50.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:26:50.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:26:50.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:26:50.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:26:50.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751347610129_127.0.0.1_11349
13:26:50.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:26:50.272 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Notify connected event to listeners.
13:26:50.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000016203514f98
13:26:50.412 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:26:50.533 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
13:26:50.903 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Receive server push request, request = NotifySubscriberRequest, requestId = 3
13:26:50.946 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Ack server push request, request = NotifySubscriberRequest, requestId = 3
13:26:51.083 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 42.623 seconds (JVM running for 59.946)
13:26:51.111 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
13:26:51.121 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
13:26:51.122 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
17:24:36.726 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Server healthy check fail, currentConnection = 1751347578677_127.0.0.1_11277
17:24:36.761 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:24:37.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751361876923_127.0.0.1_10150
17:24:37.144 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751347578677_127.0.0.1_11277
17:24:37.145 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751347578677_127.0.0.1_11277
17:24:37.167 [nacos-grpc-client-executor-2874] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751347578677_127.0.0.1_11277]Ignore complete event,isRunning:false,isAbandon=true
17:24:37.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Notify disconnected event to listeners
17:24:37.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c6b18-b1ea-430c-acc2-5c492990eee3_config-0] Notify connected event to listeners.
21:14:48.894 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:14:53.890 [nacos-grpc-client-executor-5623] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Receive server push request, request = NotifySubscriberRequest, requestId = 54
21:14:53.891 [nacos-grpc-client-executor-5623] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3331cc8-2b39-452f-9a84-d2442b42efde] Ack server push request, request = NotifySubscriberRequest, requestId = 54
21:23:31.580 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:23:31.591 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:23:31.930 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:23:31.930 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6db81e16[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:23:31.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751347610129_127.0.0.1_11349
21:23:31.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6b940142[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5737]
21:23:32.078 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:23:32.081 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:23:32.085 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:23:32.085 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
