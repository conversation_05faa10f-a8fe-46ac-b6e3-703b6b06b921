09:14:03.619 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:05.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 98ed10e7-c439-4885-9409-834a357ba5cd_config-0
09:14:05.810 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 78 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:05.885 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:05.919 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:05.946 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:05.975 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:05.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:05.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:06.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002361939cfb8
09:14:06.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002361939d1d8
09:14:06.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:06.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:06.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:07.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753751647613_127.0.0.1_10627
09:14:07.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Notify connected event to listeners.
09:14:07.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:07.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98ed10e7-c439-4885-9409-834a357ba5cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023619515b10
09:14:08.241 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:13.962 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:14:13.963 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:13.963 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:14:14.330 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:14:15.989 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:14:15.991 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:14:15.992 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:14:20.634 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:14:24.392 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 057fd59c-c128-4bea-92ad-fcd54dc6ddbc
09:14:24.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] RpcClient init label, labels = {module=naming, source=sdk}
09:14:24.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:14:24.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:14:24.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:14:24.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:24.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Success to connect to server [localhost:8848] on start up, connectionId = 1753751664405_127.0.0.1_10790
09:14:24.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:24.533 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Notify connected event to listeners.
09:14:24.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023619515b10
09:14:24.586 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:14:24.619 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:14:24.768 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 23.214 seconds (JVM running for 29.746)
09:14:24.782 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:14:24.785 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:14:24.785 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:14:25.081 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:14:25.096 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:15:04.849 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:15:10.356 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:15:10.357 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:38:33.595 [nacos-grpc-client-executor-1021] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:38:33.610 [nacos-grpc-client-executor-1021] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:39:05.621 [nacos-grpc-client-executor-1028] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:39:05.641 [nacos-grpc-client-executor-1028] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:46:18.273 [nacos-grpc-client-executor-1114] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:46:18.274 [nacos-grpc-client-executor-1114] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 24
10:49:54.894 [nacos-grpc-client-executor-1158] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 28
10:49:54.910 [nacos-grpc-client-executor-1158] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:08:50.068 [nacos-grpc-client-executor-1385] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:08:50.080 [nacos-grpc-client-executor-1385] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 31
11:09:11.873 [nacos-grpc-client-executor-1389] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Receive server push request, request = NotifySubscriberRequest, requestId = 35
11:09:11.888 [nacos-grpc-client-executor-1389] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [057fd59c-c128-4bea-92ad-fcd54dc6ddbc] Ack server push request, request = NotifySubscriberRequest, requestId = 35
11:41:03.875 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:41:03.877 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:41:04.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:41:04.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@61224878[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:41:04.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753751664405_127.0.0.1_10790
11:41:04.189 [nacos-grpc-client-executor-1773] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753751664405_127.0.0.1_10790]Ignore complete event,isRunning:false,isAbandon=false
11:41:04.191 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3b9acde0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1774]
11:41:04.360 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:41:04.363 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:41:04.370 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:41:04.370 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:45:01.176 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:45:01.996 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0
11:45:02.111 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 73 ms to scan 1 urls, producing 3 keys and 6 values 
11:45:02.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:45:02.155 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:45:02.180 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
11:45:02.195 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
11:45:02.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 2 keys and 8 values 
11:45:02.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:45:02.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002e12c39f8e0
11:45:02.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002e12c39fb00
11:45:02.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:45:02.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:45:02.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:03.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753760703049_127.0.0.1_7260
11:45:03.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Notify connected event to listeners.
11:45:03.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:03.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a084732-a0c5-4c9b-aacc-650b8ed12618_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002e12c519a90
11:45:03.367 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:45:07.065 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:45:07.065 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:45:07.066 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:45:07.219 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:45:07.920 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:45:07.921 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:45:07.922 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:45:10.244 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:45:12.659 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e8c7e8d2-d432-4dbf-996d-f605db692ddb
11:45:12.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] RpcClient init label, labels = {module=naming, source=sdk}
11:45:12.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:12.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:12.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:12.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:12.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Success to connect to server [localhost:8848] on start up, connectionId = 1753760712673_127.0.0.1_7338
11:45:12.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:12.797 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Notify connected event to listeners.
11:45:12.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002e12c519a90
11:45:12.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
11:45:12.856 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
11:45:12.997 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 12.632 seconds (JVM running for 13.833)
11:45:13.009 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
11:45:13.024 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
11:45:13.024 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
11:45:13.363 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Receive server push request, request = NotifySubscriberRequest, requestId = 39
11:45:13.380 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8c7e8d2-d432-4dbf-996d-f605db692ddb] Ack server push request, request = NotifySubscriberRequest, requestId = 39
11:45:13.548 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:27:08.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:27:08.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e995c83[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753760712673_127.0.0.1_7338
20:27:08.334 [nacos-grpc-client-executor-6260] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753760712673_127.0.0.1_7338]Ignore complete event,isRunning:false,isAbandon=false
20:27:08.345 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@294581ff[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6261]
20:27:08.514 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:27:08.521 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:27:08.523 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:27:08.524 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
