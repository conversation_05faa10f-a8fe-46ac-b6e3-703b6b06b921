09:21:09.163 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:10.427 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c73d6bce-9b40-4408-856a-8b9fd721df39_config-0
09:21:10.528 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:10.572 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:10.597 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:10.627 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:10.646 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:10.659 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:10.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:10.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002319a3cf8e0
09:21:10.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002319a3cfb00
09:21:10.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:10.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:10.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:12.136 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751851271866_127.0.0.1_10645
09:21:12.137 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Notify connected event to listeners.
09:21:12.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:12.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73d6bce-9b40-4408-856a-8b9fd721df39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002319a509a90
09:21:12.313 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:17.071 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:21:17.072 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:17.072 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:21:17.295 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:18.256 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:21:18.256 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:21:18.256 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:21:22.416 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:26.010 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 462d3940-cac7-404b-9ed7-b1fc265bb7ce
09:21:26.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] RpcClient init label, labels = {module=naming, source=sdk}
09:21:26.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:26.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:26.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:26.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:26.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Success to connect to server [localhost:8848] on start up, connectionId = 1751851286020_127.0.0.1_10798
09:21:26.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Notify connected event to listeners.
09:21:26.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:26.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002319a509a90
09:21:26.188 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:21:26.219 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:21:26.409 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 18.454 seconds (JVM running for 20.406)
09:21:26.427 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:21:26.432 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:21:26.432 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:21:26.627 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:21:26.752 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:21:26.769 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [462d3940-cac7-404b-9ed7-b1fc265bb7ce] Ack server push request, request = NotifySubscriberRequest, requestId = 7
13:30:04.059 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:30:04.066 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:30:04.407 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:30:04.409 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@34c05298[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:30:04.409 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751851286020_127.0.0.1_10798
13:30:04.409 [nacos-grpc-client-executor-2992] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751851286020_127.0.0.1_10798]Ignore complete event,isRunning:false,isAbandon=false
13:30:04.419 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27c766d1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2993]
13:30:04.634 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:30:04.638 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:30:04.670 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:30:04.673 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:21:18.790 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:21.887 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d38d596c-09ef-4f3e-822a-98b231c4f788_config-0
15:21:22.188 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 145 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:22.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 68 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:22.407 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:22.483 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:22.572 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:22.622 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:22.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:22.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000021a0c3b78e0
15:21:22.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021a0c3b7b00
15:21:22.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:22.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:22.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:28.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751872887686_127.0.0.1_11854
15:21:28.278 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Notify connected event to listeners.
15:21:28.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:28.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d38d596c-09ef-4f3e-822a-98b231c4f788_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021a0c4f1450
15:21:28.858 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:41.314 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:21:41.315 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:41.316 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:42.026 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:44.069 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:21:44.072 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:21:44.072 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:21:51.334 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:22:01.013 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca
15:22:01.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] RpcClient init label, labels = {module=naming, source=sdk}
15:22:01.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:22:01.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:22:01.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:22:01.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:22:01.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Success to connect to server [localhost:8848] on start up, connectionId = 1751872921042_127.0.0.1_12056
15:22:01.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:22:01.177 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Notify connected event to listeners.
15:22:01.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021a0c4f1450
15:22:01.278 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:22:01.353 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
15:22:02.123 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Receive server push request, request = NotifySubscriberRequest, requestId = 16
15:22:02.150 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a11fef3-d3dc-4c68-aa06-c2ec2b1c18ca] Ack server push request, request = NotifySubscriberRequest, requestId = 16
15:22:02.399 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 45.696 seconds (JVM running for 50.065)
15:22:02.440 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:22:02.447 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:22:02.449 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:43:55.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:43:55.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:43:55.996 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:43:55.996 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@237d9fc8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:43:55.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751872921042_127.0.0.1_12056
15:43:55.999 [nacos-grpc-client-executor-272] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751872921042_127.0.0.1_12056]Ignore complete event,isRunning:false,isAbandon=false
15:43:56.001 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@605493d6[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 273]
15:43:56.024 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:43:56.027 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:43:56.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:43:56.037 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:45:55.839 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:46:00.608 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0
15:46:01.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 207 ms to scan 1 urls, producing 3 keys and 6 values 
15:46:01.207 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 81 ms to scan 1 urls, producing 4 keys and 9 values 
15:46:01.258 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 10 values 
15:46:01.318 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 1 keys and 5 values 
15:46:01.387 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 59 ms to scan 1 urls, producing 1 keys and 7 values 
15:46:01.475 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 63 ms to scan 1 urls, producing 2 keys and 8 values 
15:46:01.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:46:01.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000274013f0b08
15:46:01.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000274013f0d28
15:46:01.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:46:01.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:46:01.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:07.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874367234_127.0.0.1_3815
15:46:07.775 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Notify connected event to listeners.
15:46:07.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:46:07.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027401568ad8
15:46:09.657 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:47:12.228 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:47:12.245 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:47:12.260 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:47:16.264 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:47:35.430 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:47:35.460 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:47:35.473 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:48:05.595 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Server healthy check fail, currentConnection = 1751874367234_127.0.0.1_3815
15:48:05.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:48:05.948 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Success to connect a server [localhost:8848], connectionId = 1751874485604_127.0.0.1_4540
15:48:05.949 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751874367234_127.0.0.1_3815
15:48:05.949 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874367234_127.0.0.1_3815
15:48:06.002 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Notify disconnected event to listeners
15:48:06.004 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e3075a8-e6cc-49d2-a97e-fae377f65569_config-0] Notify connected event to listeners.
15:48:12.409 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:48:21.169 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 408e422a-d03e-4354-9cda-4271f3f7040e
15:48:21.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] RpcClient init label, labels = {module=naming, source=sdk}
15:48:21.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:48:21.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:48:21.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:48:21.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:21.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Success to connect to server [localhost:8848] on start up, connectionId = 1751874501332_127.0.0.1_4671
15:48:21.543 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Notify connected event to listeners.
15:48:21.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:21.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027401568ad8
15:48:21.602 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:48:21.641 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
15:48:22.341 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Receive server push request, request = NotifySubscriberRequest, requestId = 35
15:48:22.462 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [408e422a-d03e-4354-9cda-4271f3f7040e] Ack server push request, request = NotifySubscriberRequest, requestId = 35
15:48:22.776 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 151.108 seconds (JVM running for 158.892)
15:48:22.802 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:48:22.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:48:22.822 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:48:24.884 [RMI TCP Connection(17)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:02:19.659 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:19.667 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:19.992 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:19.992 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@64ba3422[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:19.998 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874501332_127.0.0.1_4671
19:02:19.998 [nacos-grpc-client-executor-2333] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751874501332_127.0.0.1_4671]Ignore complete event,isRunning:false,isAbandon=false
19:02:19.998 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@949ecf0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2334]
19:02:20.225 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:02:20.232 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:02:20.257 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:02:20.257 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
