10:18:51.124 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:52.227 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0
10:18:52.308 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:52.342 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:52.368 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:52.387 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:52.412 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:52.429 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:52.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:52.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001b1be39cfb8
10:18:52.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b1be39d1d8
10:18:52.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:52.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:52.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:18:53.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558733525_127.0.0.1_5517
10:18:53.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Notify connected event to listeners.
10:18:53.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:53.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b1be519450
10:18:54.034 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:59.065 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:18:59.066 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:18:59.067 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:18:59.339 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:19:00.451 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:19:00.453 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:19:00.581 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:19:07.067 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:19:12.977 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f10ff75-9edc-45cc-930b-6997e1da180d
10:19:12.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] RpcClient init label, labels = {module=naming, source=sdk}
10:19:12.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:12.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:12.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:12.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:19:13.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558752991_127.0.0.1_5548
10:19:13.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:13.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Notify connected event to listeners.
10:19:13.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b1be519450
10:19:13.194 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:19:13.254 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
10:19:13.491 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 23.442 seconds (JVM running for 30.162)
10:19:13.513 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:19:13.517 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:19:13.518 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:19:13.662 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:19:13.685 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Ack server push request, request = NotifySubscriberRequest, requestId = 6
12:07:13.521 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:55:55.308 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.308 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.510 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.510 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.729 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.729 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.478 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.478 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.607 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.340 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.111 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.113 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.271 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.271 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf596fc-6cdb-4b88-9195-94f9daa02742_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.881 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:56:04.206 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:56:04.527 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:56:04.527 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5a137f99[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:56:04.527 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750558752991_127.0.0.1_5548
17:56:04.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f10ff75-9edc-45cc-930b-6997e1da180d] Client is shutdown, stop reconnect to server
17:56:04.529 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4a915877[Running, pool size = 27, active threads = 0, queued tasks = 0, completed tasks = 5513]
17:56:04.688 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:56:04.700 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:56:04.707 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:56:04.707 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
