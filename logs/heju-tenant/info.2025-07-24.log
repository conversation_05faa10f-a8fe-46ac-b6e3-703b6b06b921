09:12:20.260 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:21.627 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0
09:12:21.730 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 50 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:21.781 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:21.803 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:21.823 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:21.842 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:21.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:21.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:21.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001b3ca3cf8e0
09:12:21.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b3ca3cfb00
09:12:21.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:21.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:21.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:23.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753319543006_127.0.0.1_12623
09:12:23.250 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Notify connected event to listeners.
09:12:23.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:23.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7811091f-389a-45e4-a6f3-d95112b6e0f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b3ca509ea0
09:12:23.444 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:30.034 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:12:30.035 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:30.036 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:30.356 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:31.497 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:12:31.499 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:12:31.500 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:12:36.080 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:39.355 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 606187d6-6c96-4295-ac00-bc45c600d9ac
09:12:39.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] RpcClient init label, labels = {module=naming, source=sdk}
09:12:39.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:39.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:39.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:39.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:39.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Success to connect to server [localhost:8848] on start up, connectionId = 1753319559365_127.0.0.1_12767
09:12:39.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Notify connected event to listeners.
09:12:39.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:39.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b3ca509ea0
09:12:39.534 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:12:39.574 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:12:39.715 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.177 seconds (JVM running for 23.747)
09:12:39.728 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:12:39.730 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:12:39.731 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:12:40.034 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:12:40.054 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:12:40.206 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:16:21.802 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:16:21.808 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 14
11:49:17.955 [nacos-grpc-client-executor-1896] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:49:17.981 [nacos-grpc-client-executor-1896] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 19
11:49:45.304 [nacos-grpc-client-executor-1903] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 23
11:49:45.322 [nacos-grpc-client-executor-1903] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 23
11:51:27.479 [nacos-grpc-client-executor-1924] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 28
11:51:27.503 [nacos-grpc-client-executor-1924] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:51:53.056 [nacos-grpc-client-executor-1929] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:51:53.078 [nacos-grpc-client-executor-1929] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 32
13:28:55.933 [nacos-grpc-client-executor-3093] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 37
13:28:55.951 [nacos-grpc-client-executor-3093] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 37
13:29:39.240 [nacos-grpc-client-executor-3101] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 41
13:29:39.249 [nacos-grpc-client-executor-3101] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 41
13:31:40.251 [nacos-grpc-client-executor-3125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 46
13:31:40.271 [nacos-grpc-client-executor-3125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 46
13:31:58.819 [nacos-grpc-client-executor-3130] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 50
13:31:58.835 [nacos-grpc-client-executor-3130] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 50
13:37:01.866 [nacos-grpc-client-executor-3193] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 55
13:37:01.887 [nacos-grpc-client-executor-3193] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 55
13:37:18.886 [nacos-grpc-client-executor-3196] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 59
13:37:18.899 [nacos-grpc-client-executor-3196] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 59
13:40:17.535 [nacos-grpc-client-executor-3232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 65
13:40:17.552 [nacos-grpc-client-executor-3232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 65
13:40:32.374 [nacos-grpc-client-executor-3235] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Receive server push request, request = NotifySubscriberRequest, requestId = 70
13:40:32.386 [nacos-grpc-client-executor-3235] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [606187d6-6c96-4295-ac00-bc45c600d9ac] Ack server push request, request = NotifySubscriberRequest, requestId = 70
13:41:02.253 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:41:02.255 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:41:02.571 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:41:02.571 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5a1b51b4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:41:02.572 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753319559365_127.0.0.1_12767
13:41:02.574 [nacos-grpc-client-executor-3243] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753319559365_127.0.0.1_12767]Ignore complete event,isRunning:false,isAbandon=false
13:41:02.577 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12b67e40[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3244]
13:41:02.720 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:41:02.723 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:41:02.732 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:41:02.732 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:41:31.182 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:41:31.826 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0
13:41:31.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
13:41:31.914 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
13:41:31.932 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
13:41:31.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
13:41:31.951 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
13:41:31.959 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:41:31.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:41:31.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002070c39fd80
13:41:31.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002070c3a0000
13:41:31.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:41:31.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:41:31.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:33.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335692894_127.0.0.1_1405
13:41:33.117 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Notify connected event to listeners.
13:41:33.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:33.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08e3897c-0e77-44e7-a8a7-0c2ceb250173_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002070c51a768
13:41:33.327 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:41:40.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:41:40.429 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:41:40.430 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:41:41.281 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:41:43.444 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:41:43.447 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:41:43.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:41:49.924 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:41:57.811 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 91e6d2fb-4730-4d65-92c7-a004f99bcb9a
13:41:57.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] RpcClient init label, labels = {module=naming, source=sdk}
13:41:57.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:41:57.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:41:57.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:41:57.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:57.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Success to connect to server [localhost:8848] on start up, connectionId = 1753335717834_127.0.0.1_1485
13:41:57.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:57.976 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Notify connected event to listeners.
13:41:57.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002070c51a768
13:41:58.076 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:41:58.153 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
13:41:58.545 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 27.99 seconds (JVM running for 29.591)
13:41:58.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
13:41:58.580 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
13:41:58.582 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
13:41:58.628 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Receive server push request, request = NotifySubscriberRequest, requestId = 3
13:41:58.654 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91e6d2fb-4730-4d65-92c7-a004f99bcb9a] Ack server push request, request = NotifySubscriberRequest, requestId = 3
13:41:59.043 [RMI TCP Connection(11)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:42:17.728 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:42:17.733 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:42:18.063 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:42:18.064 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@784603ea[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:42:18.064 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335717834_127.0.0.1_1485
20:42:18.065 [nacos-grpc-client-executor-5047] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335717834_127.0.0.1_1485]Ignore complete event,isRunning:false,isAbandon=false
20:42:18.066 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@360f2638[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5048]
20:42:18.220 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:42:18.224 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:42:18.227 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:42:18.227 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
