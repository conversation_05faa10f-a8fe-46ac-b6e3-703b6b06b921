09:37:07.534 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:37:09.765 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0
09:37:09.888 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 58 ms to scan 1 urls, producing 3 keys and 6 values 
09:37:09.941 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:37:09.981 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 10 values 
09:37:10.001 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 5 values 
09:37:10.024 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:37:10.044 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:37:10.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:37:10.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000018bce3b8b08
09:37:10.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000018bce3b8d28
09:37:10.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:37:10.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:37:10.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:37:11.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457031370_127.0.0.1_3284
09:37:11.638 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Notify connected event to listeners.
09:37:11.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:11.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59ff1ed0-6f28-47f0-9e20-52e78f269d2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018bce4f2958
09:37:12.011 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:37:16.475 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:37:16.477 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:37:16.477 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:37:16.703 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:37:17.580 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:37:17.582 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:37:17.583 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:37:20.891 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:37:24.035 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fcdc00cd-8743-4b06-b4a4-79ff44585a1a
09:37:24.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] RpcClient init label, labels = {module=naming, source=sdk}
09:37:24.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:37:24.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:37:24.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:37:24.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:37:24.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Success to connect to server [localhost:8848] on start up, connectionId = 1752457044048_127.0.0.1_3318
09:37:24.166 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Notify connected event to listeners.
09:37:24.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:24.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018bce4f2958
09:37:24.238 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:37:24.280 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:37:24.439 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 19.753 seconds (JVM running for 23.989)
09:37:24.452 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:37:24.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:37:24.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:37:24.704 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:37:24.723 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcdc00cd-8743-4b06-b4a4-79ff44585a1a] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:42:38.613 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:44:14.756 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:44:14.760 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:44:15.093 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:44:15.094 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6112f793[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:44:15.094 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752457044048_127.0.0.1_3318
09:44:15.097 [nacos-grpc-client-executor-95] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752457044048_127.0.0.1_3318]Ignore complete event,isRunning:false,isAbandon=false
09:44:15.133 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@30ca4717[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 96]
09:44:15.158 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:44:15.161 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:44:15.170 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:44:15.170 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:50:20.755 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:50:21.820 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6301db51-a9a7-4189-8c45-ab8f33676457_config-0
09:50:21.900 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:50:21.926 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:50:21.938 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:50:21.949 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:50:21.959 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:50:21.974 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:50:21.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:50:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001d6a33b78e0
09:50:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d6a33b7b00
09:50:21.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:50:21.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:50:21.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:23.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457823298_127.0.0.1_5705
09:50:23.548 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Notify connected event to listeners.
09:50:23.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:23.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6301db51-a9a7-4189-8c45-ab8f33676457_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d6a34f8fb0
09:50:23.706 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:50:29.296 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:50:29.298 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:50:29.298 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:50:29.680 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:50:31.030 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:50:31.033 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:50:31.033 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:50:36.913 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:50:42.979 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 484a3645-492a-47f2-aa1e-8c756bd81d1b
09:50:42.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] RpcClient init label, labels = {module=naming, source=sdk}
09:50:42.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:50:42.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:50:42.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:50:42.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:43.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Success to connect to server [localhost:8848] on start up, connectionId = 1752457843003_127.0.0.1_5814
09:50:43.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:43.133 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Notify connected event to listeners.
09:50:43.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d6a34f8fb0
09:50:43.201 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:50:43.244 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:50:43.493 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 23.526 seconds (JVM running for 24.895)
09:50:43.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:50:43.522 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:50:43.524 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:50:43.677 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:50:43.698 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [484a3645-492a-47f2-aa1e-8c756bd81d1b] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:50:43.991 [RMI TCP Connection(22)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:09:27.655 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:09:27.665 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:09:27.989 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:09:27.990 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@11915797[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:09:27.990 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752457843003_127.0.0.1_5814
18:09:27.993 [nacos-grpc-client-executor-5987] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752457843003_127.0.0.1_5814]Ignore complete event,isRunning:false,isAbandon=false
18:09:27.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@358fdb9c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5988]
18:09:28.152 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:09:28.165 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:09:28.167 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:09:28.167 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
