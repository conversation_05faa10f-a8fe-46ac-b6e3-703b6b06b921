11:43:36.012 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:37.062 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0
11:43:37.144 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:37.174 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:37.187 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:37.201 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:37.238 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:37.250 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:37.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:37.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000019c813cf8e0
11:43:37.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019c813cfb00
11:43:37.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:37.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:37.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:40.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:43.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:46.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:43:46.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:46.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66f478fa-76b5-4c3d-ad59-d25d48065d1e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019c814e4430
11:43:49.058 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:43:52.370 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:43:52.490 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:43:52.498 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:53:45.723 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:53:46.807 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0
11:53:46.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
11:53:46.929 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:53:46.943 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
11:53:46.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
11:53:46.984 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
11:53:46.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:53:47.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:53:47.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001ff0139f268
11:53:47.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ff0139f488
11:53:47.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:53:47.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:53:47.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:48.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305228131_127.0.0.1_1563
11:53:48.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Notify connected event to listeners.
11:53:48.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:48.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ff01518668
11:53:48.599 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:53:52.920 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:53:52.921 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:53:52.921 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:53:53.158 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:53:54.043 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:53:54.044 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:53:54.044 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:53:58.153 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:54:02.121 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9fbb120e-a5e5-4d9c-97ba-fe7f590b8547
11:54:02.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] RpcClient init label, labels = {module=naming, source=sdk}
11:54:02.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:54:02.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:54:02.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:54:02.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:54:02.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305242143_127.0.0.1_1602
11:54:02.267 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Notify connected event to listeners.
11:54:02.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:54:02.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ff01518668
11:54:02.328 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
11:54:02.380 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
11:54:02.572 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.752 seconds (JVM running for 19.18)
11:54:02.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
11:54:02.600 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
11:54:02.602 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
11:54:02.908 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Receive server push request, request = NotifySubscriberRequest, requestId = 9
11:54:02.950 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fbb120e-a5e5-4d9c-97ba-fe7f590b8547] Ack server push request, request = NotifySubscriberRequest, requestId = 9
11:54:03.072 [RMI TCP Connection(2)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:19:29.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Server healthy check fail, currentConnection = 1750305228131_127.0.0.1_1563
13:19:29.129 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:19:31.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750310369146_127.0.0.1_14204
13:19:31.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750305228131_127.0.0.1_1563
13:19:31.198 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305228131_127.0.0.1_1563
13:19:31.206 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Notify disconnected event to listeners
13:19:31.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d9c4d7e-a48f-48ff-8f14-5afae8e7ed12_config-0] Notify connected event to listeners.
13:19:31.211 [nacos-grpc-client-executor-1040] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305228131_127.0.0.1_1563]Ignore complete event,isRunning:true,isAbandon=true
15:45:38.617 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:45:38.624 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:45:38.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:45:38.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@51ea68af[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:45:38.972 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305242143_127.0.0.1_1602
15:45:38.975 [nacos-grpc-client-executor-2776] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305242143_127.0.0.1_1602]Ignore complete event,isRunning:false,isAbandon=false
15:45:38.978 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3b2b2d40[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2776]
15:45:39.215 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:45:39.231 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:45:39.267 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:45:39.269 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:38:35.517 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:38:36.517 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0
16:38:36.599 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:38:36.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:38:36.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:38:36.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:38:36.670 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
16:38:36.688 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
16:38:36.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:38:36.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000221b33b7d80
16:38:36.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000221b33b8000
16:38:36.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:38:36.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:38:36.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:37.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:37.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:37.717 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:38:37.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:37.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000221b34c0c30
16:38:37.841 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:38.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:38.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:38.803 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:39.317 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:39.835 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:38:39.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:40.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:41.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:42.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:43.616 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:45.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:46.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:47.325 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:38:47.327 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:38:47.328 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:38:47.788 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:38:47.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:49.436 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:49.563 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:38:49.566 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:38:49.566 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:38:51.047 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:52.816 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:54.651 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:55.732 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:38:56.643 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:58.660 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:00.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.109 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.177 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2261f5d-8ef5-41ed-9c71-2d95960ee007
16:39:03.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] RpcClient init label, labels = {module=naming, source=sdk}
16:39:03.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:03.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:03.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:03.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:03.209 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:03.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000221b34c0c30
16:39:03.329 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.555 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:39:03.857 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.278 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.553 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:04.554 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2623ad23[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:04.555 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2261f5d-8ef5-41ed-9c71-2d95960ee007] Client is shutdown, stop reconnect to server
16:39:04.555 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2e03b52f[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
16:39:04.559 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-d9fa-4b9f-af2f-44234e49bfcf
16:39:04.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] RpcClient init label, labels = {module=naming, source=sdk}
16:39:04.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:04.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:04.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:04.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:04.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:04.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:04.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:04.588 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:04.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000221b34c0c30
16:39:04.711 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.938 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.975 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:39:04.975 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:39:04.996 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:39:04.996 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:39:05.018 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
16:39:05.018 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:39:05.047 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
16:39:05.054 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
16:39:05.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:05.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89ecc548-97a9-49d6-8003-8602ccc3af5d_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:05.659 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d9fa-4b9f-af2f-44234e49bfcf] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:40:54.830 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:40:55.944 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0
16:40:56.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:56.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:56.106 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:56.124 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:56.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:56.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:56.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:56.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000265ad39f8e0
16:40:56.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000265ad39fb00
16:40:56.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:56.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:56.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:57.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322457441_127.0.0.1_9215
16:40:57.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:57.695 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Notify connected event to listeners.
16:40:57.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000265ad519450
16:40:57.875 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:41:03.872 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:41:03.873 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:41:03.873 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:41:04.147 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:41:05.138 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:41:05.140 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:41:05.140 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:41:09.862 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:41:15.063 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9893d1fc-2498-48dd-933e-f4a902c19ec0
16:41:15.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] RpcClient init label, labels = {module=naming, source=sdk}
16:41:15.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:41:15.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:41:15.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:41:15.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:41:15.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322475082_127.0.0.1_9292
16:41:15.216 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Notify connected event to listeners.
16:41:15.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:15.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000265ad519450
16:41:15.295 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:41:15.365 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
16:41:15.554 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.831 seconds (JVM running for 23.328)
16:41:15.576 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
16:41:15.581 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
16:41:15.582 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
16:41:15.777 [RMI TCP Connection(9)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:41:15.842 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Receive server push request, request = NotifySubscriberRequest, requestId = 5
16:41:15.872 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Ack server push request, request = NotifySubscriberRequest, requestId = 5
17:08:20.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Server healthy check fail, currentConnection = 1750322457441_127.0.0.1_9215
17:08:20.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.856 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:20.930 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.075 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.473 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.828 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.963 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.584 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.790 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:08:25.115 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:08:25.449 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:08:25.450 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66a25a37[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:08:25.450 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750322475082_127.0.0.1_9292
17:08:25.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9893d1fc-2498-48dd-933e-f4a902c19ec0] Client is shutdown, stop reconnect to server
17:08:25.450 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@796259c0[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 357]
17:08:25.462 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1603b3a7-c375-4cbc-83cd-5942c3061f51_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:25.608 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:08:25.608 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:08:25.629 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:08:25.631 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
