09:36:30.518 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:31.370 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0
09:36:31.454 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:36:31.491 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:36:31.502 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:36:31.513 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:36:31.528 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:36:31.536 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:36:31.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:31.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000024c013a0638
09:36:31.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024c013a0858
09:36:31.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:31.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:31.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:32.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:32.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:32.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:32.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:32.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000024c014f4430
09:36:32.512 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:32.728 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:33.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:33.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:33.979 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:34.445 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:36:34.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:35.314 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:36.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.040 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.054 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.324 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:36:38.324 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:36:38.324 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:36:38.649 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:36:39.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.484 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:36:39.485 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:36:39.486 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:36:40.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:41.951 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:42.998 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:36:43.564 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:45.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:46.905 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:48.725 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:48.836 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 03d28270-b8df-4eb2-8855-225f2ef410a9
09:36:48.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] RpcClient init label, labels = {module=naming, source=sdk}
09:36:48.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:48.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:48.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:48.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:48.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:48.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:48.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:48.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:48.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000024c014f4430
09:36:49.043 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:49.255 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:36:49.265 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:49.581 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:49.998 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:50.265 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:36:50.265 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@da8853c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:36:50.265 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@fb79241[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:36:50.265 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [03d28270-b8df-4eb2-8855-225f2ef410a9] Client is shutdown, stop reconnect to server
09:36:50.270 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ebf7fe97-a33e-4a6f-93a7-542dbab8417b
09:36:50.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] RpcClient init label, labels = {module=naming, source=sdk}
09:36:50.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:50.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:50.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:50.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:50.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:50.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:50.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:50.312 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:50.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000024c014f4430
09:36:50.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:50.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93d428ee-5a60-44a3-ae73-28fd69bcb2c8_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:50.656 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:50.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:36:50.696 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:36:50.712 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:36:50.713 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:36:50.742 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:36:50.743 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:36:50.778 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:36:50.785 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:36:50.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:51.409 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebf7fe97-a33e-4a6f-93a7-542dbab8417b] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:33.423 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:45:34.463 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0
09:45:34.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
09:45:34.560 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:45:34.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:45:34.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:45:34.597 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:45:34.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:45:34.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:45:34.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002af4139f8e0
09:45:34.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002af4139fb00
09:45:34.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:45:34.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:45:34.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:35.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:35.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:35.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:35.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:35.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002af414e9cc0
09:45:35.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.447 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:37.672 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:37.962 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:45:38.315 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.119 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.987 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:42.236 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:43.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:44.668 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.964 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:46.577 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:45:46.578 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:45:46.578 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:45:46.902 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:45:47.722 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:48.325 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:45:48.327 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:45:48.328 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:45:49.353 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:51.397 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:53.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:55.166 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:45:55.828 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:58.299 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:00.799 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:02.583 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1e908a52-be0c-4f6b-941f-b4748850913f
09:46:02.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] RpcClient init label, labels = {module=naming, source=sdk}
09:46:02.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:46:02.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:46:02.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:46:02.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:02.910 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:03.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:03.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:03.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:46:03.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002af414e9cc0
09:46:03.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:46:03.636 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:46:03.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:03.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:04.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:04.634 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:46:04.635 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7f8614ce[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:46:04.635 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@11261fc9[Running, pool size = 14, active threads = 0, queued tasks = 0, completed tasks = 14]
09:46:04.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e908a52-be0c-4f6b-941f-b4748850913f] Client is shutdown, stop reconnect to server
09:46:04.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bcb62c8d-37ac-49dc-882d-************
09:46:04.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] RpcClient init label, labels = {module=naming, source=sdk}
09:46:04.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:46:04.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:46:04.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:46:04.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:04.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:04.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:04.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:46:04.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002af414e9cc0
09:46:04.710 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:46:04.957 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:05.120 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:46:05.163 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:46:05.182 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:46:05.182 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:46:05.339 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:05.356 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9df5dd2a-71ed-47a9-8536-01399dce8cf0_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:05.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:46:05.386 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:46:05.413 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:46:05.422 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:46:05.691 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:06.112 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcb62c8d-37ac-49dc-882d-************] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:07.365 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:07.980 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0
10:08:08.040 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:08.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:08.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:08.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:08.103 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:08.111 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:08.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:08.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000018ae43cfd80
10:08:08.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018ae43d0000
10:08:08.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:08.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:08.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:09.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:09.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:09.032 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:09.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:09.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018ae44e0000
10:08:09.151 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:09.367 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:09.677 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:10.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:10.639 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:11.063 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:08:11.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:11.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.771 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:13.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.178 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:08:15.182 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:08:15.182 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:08:15.329 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:08:15.903 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.041 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:08:16.042 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:08:16.043 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:08:17.164 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:18.538 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:19.121 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:08:20.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:21.718 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:22.759 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ea24101a-1a3c-4a50-a812-5772a3e43730
10:08:22.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] RpcClient init label, labels = {module=naming, source=sdk}
10:08:22.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:22.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:22.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:22.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:22.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:22.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:22.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:22.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:22.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018ae44e0000
10:08:22.998 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:23.168 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:08:23.250 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:23.340 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:23.559 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:23.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:24.177 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:08:24.178 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@327fd5c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:08:24.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea24101a-1a3c-4a50-a812-5772a3e43730] Client is shutdown, stop reconnect to server
10:08:24.180 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5cb6fac3[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
10:08:24.182 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a6551543-e146-4bca-b78b-6ccca3a99e55
10:08:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] RpcClient init label, labels = {module=naming, source=sdk}
10:08:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:24.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:24.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:24.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:24.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:24.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:24.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018ae44e0000
10:08:24.425 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:24.606 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:08:24.611 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:08:24.621 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:08:24.621 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:08:24.638 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
10:08:24.638 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:08:24.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:24.660 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
10:08:24.663 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
10:08:24.954 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:25.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c78e75a-47e1-4de1-882e-5ff26ca82194_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:25.383 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6551543-e146-4bca-b78b-6ccca3a99e55] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:52.433 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:53.327 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0
10:08:53.406 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:53.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:53.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:53.462 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:53.481 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:53.491 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:53.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:53.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000027f8239fd80
10:08:53.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000027f823a0000
10:08:53.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:53.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:53.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:54.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751940534472_127.0.0.1_7851
10:08:54.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Notify connected event to listeners.
10:08:54.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:54.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca95bbdf-8caa-4959-8b77-67d3fc28db63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027f8251a0a0
10:08:54.904 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:08:59.758 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:08:59.759 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:08:59.759 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:08:59.971 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:09:00.749 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:09:00.749 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:09:00.749 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:09:03.869 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:09:07.288 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 208ac78e-59b3-4c91-bc48-f829c8d34fbd
10:09:07.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] RpcClient init label, labels = {module=naming, source=sdk}
10:09:07.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:09:07.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:09:07.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:09:07.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:07.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Success to connect to server [localhost:8848] on start up, connectionId = 1751940547305_127.0.0.1_8054
10:09:07.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:07.434 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Notify connected event to listeners.
10:09:07.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027f8251a0a0
10:09:07.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:09:07.567 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:09:07.735 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 16.026 seconds (JVM running for 17.217)
10:09:07.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:09:07.754 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:09:07.754 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:09:08.006 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:09:08.048 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Receive server push request, request = NotifySubscriberRequest, requestId = 7
10:09:08.069 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [208ac78e-59b3-4c91-bc48-f829c8d34fbd] Ack server push request, request = NotifySubscriberRequest, requestId = 7
13:11:25.128 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:11:25.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:11:25.468 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:11:25.469 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@307c7180[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:11:25.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751940547305_127.0.0.1_8054
13:11:25.473 [nacos-grpc-client-executor-2194] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751940547305_127.0.0.1_8054]Ignore complete event,isRunning:false,isAbandon=false
13:11:25.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@37fdaaae[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2195]
13:11:25.628 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:11:25.632 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:11:25.640 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:11:25.640 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:52:35.994 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:52:36.765 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0
15:52:36.826 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
15:52:36.850 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
15:52:36.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
15:52:36.880 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
15:52:36.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
15:52:36.909 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
15:52:36.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:52:36.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002250939dd00
15:52:36.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002250939df20
15:52:36.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:52:36.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:52:36.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:38.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:38.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:39.012 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:39.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:39.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000225094ab938
15:52:39.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:39.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:39.673 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:40.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:40.607 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:41.026 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:52:41.219 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:41.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:42.791 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:43.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:45.298 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.494 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.796 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:52:46.797 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:52:46.797 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:52:47.011 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:52:47.776 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.118 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:52:48.120 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:52:48.120 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:52:49.175 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:50.669 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:51.850 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:52:52.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:53.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:55.473 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a977d81-39e2-4f4b-9f11-dbe6f000e465
15:52:55.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] RpcClient init label, labels = {module=naming, source=sdk}
15:52:55.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:55.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:55.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:55.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:55.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:55.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:56.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:56.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:56.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:56.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000225094ab938
15:52:56.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:56.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:52:57.018 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:57.549 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:52:57.549 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@11a9f958[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:52:57.549 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7585531b[Running, pool size = 12, active threads = 0, queued tasks = 0, completed tasks = 12]
15:52:57.552 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:57.552 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e9ae104a-496d-43ff-8c87-b94d863cf2d3
15:52:57.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] RpcClient init label, labels = {module=naming, source=sdk}
15:52:57.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:57.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:57.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:57.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:57.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:57.855 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1342b5da-c7a9-458d-bef5-c6a64f695ae1_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:57.953 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a977d81-39e2-4f4b-9f11-dbe6f000e465] Client is shutdown, stop reconnect to server
15:52:57.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:57.996 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:57.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:57.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000225094ab938
15:52:58.372 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:52:58.379 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:52:58.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:58.397 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:52:58.398 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:52:58.415 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
15:52:58.416 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:52:58.442 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
15:52:58.450 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
15:52:58.648 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:58.971 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9ae104a-496d-43ff-8c87-b94d863cf2d3] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:31.319 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:53:32.255 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 80613866-f1aa-41f0-8df7-d5d4d974921b_config-0
15:53:32.329 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
15:53:32.358 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
15:53:32.370 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:53:32.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
15:53:32.404 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
15:53:32.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:53:32.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:53:32.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001d88139f8e0
15:53:32.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d88139fb00
15:53:32.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:53:32.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:53:32.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:33.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751961213399_127.0.0.1_6440
15:53:33.621 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Notify connected event to listeners.
15:53:33.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:33.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80613866-f1aa-41f0-8df7-d5d4d974921b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d881519a90
15:53:33.830 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:53:38.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:53:38.851 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:53:38.851 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:53:39.058 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:53:39.856 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:53:39.857 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:53:39.857 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:53:43.042 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:53:46.712 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d5b4a77-33f2-4f42-ae52-c5f1e3893787
15:53:46.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] RpcClient init label, labels = {module=naming, source=sdk}
15:53:46.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:53:46.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:53:46.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:53:46.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:46.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Success to connect to server [localhost:8848] on start up, connectionId = 1751961226725_127.0.0.1_6566
15:53:46.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:46.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d881519a90
15:53:46.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Notify connected event to listeners.
15:53:46.895 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:53:46.930 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
15:53:47.082 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 16.519 seconds (JVM running for 17.745)
15:53:47.094 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:53:47.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:53:47.099 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:53:47.191 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:53:47.394 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Receive server push request, request = NotifySubscriberRequest, requestId = 6
15:53:47.412 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5b4a77-33f2-4f42-ae52-c5f1e3893787] Ack server push request, request = NotifySubscriberRequest, requestId = 6
17:52:45.312 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:52:45.319 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:52:45.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:52:45.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@58c3f106[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:52:45.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751961226725_127.0.0.1_6566
17:52:45.649 [nacos-grpc-client-executor-1440] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751961226725_127.0.0.1_6566]Ignore complete event,isRunning:false,isAbandon=false
17:52:45.654 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@75e38f0d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1441]
17:52:45.802 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:52:45.807 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:52:45.816 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:52:45.816 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
