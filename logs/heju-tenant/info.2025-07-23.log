09:39:21.962 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:39:22.962 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0
09:39:23.026 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 6 values 
09:39:23.051 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:39:23.059 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:39:23.067 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:39:23.081 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:39:23.089 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:39:23.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:39:23.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001cd0139fd80
09:39:23.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cd013a0000
09:39:23.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:39:23.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:39:23.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:24.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234763941_127.0.0.1_3960
09:39:24.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Notify connected event to listeners.
09:39:24.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:24.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fbe6115-de45-493b-ae9b-cae91f1c7f5e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001cd0151a198
09:39:24.318 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:39:28.642 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:39:28.643 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:39:28.643 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:39:28.837 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:39:29.701 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:39:29.702 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:39:29.702 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:39:32.970 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:39:35.693 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 85bb6224-fb0e-42c8-a9b5-41046811b023
09:39:35.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] RpcClient init label, labels = {module=naming, source=sdk}
09:39:35.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:39:35.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:39:35.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:39:35.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:35.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Success to connect to server [localhost:8848] on start up, connectionId = 1753234775704_127.0.0.1_4085
09:39:35.821 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Notify connected event to listeners.
09:39:35.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:35.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001cd0151a198
09:39:35.904 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:39:35.950 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:39:36.114 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 15.074 seconds (JVM running for 17.505)
09:39:36.130 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:39:36.133 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:39:36.133 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:39:36.463 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:39:36.487 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:39:36.615 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:52:14.807 [nacos-grpc-client-executor-166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:52:14.809 [nacos-grpc-client-executor-166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:23:27.533 [nacos-grpc-client-executor-1256] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 24
11:23:27.553 [nacos-grpc-client-executor-1256] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 24
11:23:52.632 [nacos-grpc-client-executor-1261] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 27
11:23:52.653 [nacos-grpc-client-executor-1261] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 27
11:42:48.384 [nacos-grpc-client-executor-1489] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:42:48.406 [nacos-grpc-client-executor-1489] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 31
11:43:12.005 [nacos-grpc-client-executor-1493] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 34
11:43:12.022 [nacos-grpc-client-executor-1493] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 34
19:29:13.343 [nacos-grpc-client-executor-7076] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 38
19:29:13.363 [nacos-grpc-client-executor-7076] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 38
19:30:01.427 [nacos-grpc-client-executor-7085] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 41
19:30:01.433 [nacos-grpc-client-executor-7085] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 41
19:36:48.863 [nacos-grpc-client-executor-7167] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 46
19:36:48.885 [nacos-grpc-client-executor-7167] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 46
19:37:12.009 [nacos-grpc-client-executor-7171] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 50
19:37:12.021 [nacos-grpc-client-executor-7171] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 50
19:40:33.244 [nacos-grpc-client-executor-7211] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 53
19:40:33.261 [nacos-grpc-client-executor-7211] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 53
19:40:49.389 [nacos-grpc-client-executor-7215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Receive server push request, request = NotifySubscriberRequest, requestId = 56
19:40:49.404 [nacos-grpc-client-executor-7215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85bb6224-fb0e-42c8-a9b5-41046811b023] Ack server push request, request = NotifySubscriberRequest, requestId = 56
20:22:57.253 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:22:57.256 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:22:57.601 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:22:57.601 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63e63e51[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:22:57.601 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753234775704_127.0.0.1_4085
20:22:57.604 [nacos-grpc-client-executor-7722] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753234775704_127.0.0.1_4085]Ignore complete event,isRunning:false,isAbandon=false
20:22:57.606 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6b99f913[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 7723]
20:22:57.756 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:22:57.759 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:22:57.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:22:57.761 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
