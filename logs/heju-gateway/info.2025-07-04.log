09:24:36.399 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:37.187 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e22a6b62-b21a-4542-bd1b-71b06756203b_config-0
09:24:37.273 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:37.303 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:37.317 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:37.333 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:37.349 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:37.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:37.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:37.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001c72a3b44e8
09:24:37.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001c72a3b4708
09:24:37.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:37.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:37.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:39.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:39.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:39.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:39.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:39.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c72a4c2228
09:24:39.303 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:39.523 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:39.853 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:40.279 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:40.796 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:40.951 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:41.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:42.149 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:42.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:43.878 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:45.055 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:46.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:48.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:49.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:50.313 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:24:51.214 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:52.580 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:24:52.849 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:53.873 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0
09:24:53.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:53.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001c72a3b44e8
09:24:53.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001c72a3b4708
09:24:53.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:53.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:53.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.048 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:54.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:54.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c72a4c2228
09:24:54.182 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.403 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.601 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 252f3c67-f51d-4d39-be8b-8d945417e1d1
09:24:54.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] RpcClient init label, labels = {module=naming, source=sdk}
09:24:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:54.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:54.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c72a4c2228
09:24:54.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.811 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.015 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.755 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:56.200 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:56.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:56.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:56.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:57.014 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:57.221 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:57.221 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7444714e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:57.222 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6b3708f0[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 20]
09:24:57.223 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [252f3c67-f51d-4d39-be8b-8d945417e1d1] Client is shutdown, stop reconnect to server
09:24:57.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:58.020 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:58.813 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:00.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1edacb9e-e900-478e-b82d-e3a47a0ca441_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:00.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e22a6b62-b21a-4542-bd1b-71b06756203b_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:09.151 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:09.921 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 80a64752-597e-40a2-b9e1-0bfc484f3917_config-0
09:25:10.017 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:25:10.053 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:25:10.069 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:25:10.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:25:10.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:25:10.111 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:25:10.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:10.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001d6283bdd00
09:25:10.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001d6283bdf20
09:25:10.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:10.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:10.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:11.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592311238_127.0.0.1_9372
09:25:11.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Notify connected event to listeners.
09:25:11.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:11.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d6284f7b78
09:25:11.736 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:25:17.168 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:25:18.891 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:25:19.718 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 23266205-a095-49ce-a17a-4cd673ef1c7b_config-0
09:25:19.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:19.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001d6283bdd00
09:25:19.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001d6283bdf20
09:25:19.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:19.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:19.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:19.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592319731_127.0.0.1_9568
09:25:19.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:19.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Notify connected event to listeners.
09:25:19.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d6284f7b78
09:25:20.022 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 47dbb285-b676-4390-9e21-176ba7b9c275
09:25:20.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] RpcClient init label, labels = {module=naming, source=sdk}
09:25:20.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:20.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:20.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:20.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:20.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Success to connect to server [localhost:8848] on start up, connectionId = 1751592320039_127.0.0.1_9575
09:25:20.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:20.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Notify connected event to listeners.
09:25:20.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d6284f7b78
09:25:20.934 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:25:20.983 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.641 seconds (JVM running for 14.036)
09:25:20.994 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:25:20.995 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:25:20.996 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:25:21.053 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:25:21.054 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:25:21.442 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:25:21.443 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:25:51.065 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:25:51.065 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:25:51.076 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:25:51.076 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:26:20.976 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:26:20.978 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:28:20.992 [nacos-grpc-client-executor-91] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:28:20.993 [nacos-grpc-client-executor-91] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 11
11:05:16.829 [nacos-grpc-client-executor-1978] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 14
11:05:16.848 [nacos-grpc-client-executor-1978] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 14
11:05:37.403 [nacos-grpc-client-executor-1988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:05:37.418 [nacos-grpc-client-executor-1988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:41:47.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Server healthy check fail, currentConnection = 1751592319731_127.0.0.1_9568
11:41:47.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Server healthy check fail, currentConnection = 1751592320039_127.0.0.1_9575
11:41:47.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.838 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Success to connect a server [localhost:8848], connectionId = 1751600507723_127.0.0.1_13709
11:41:47.840 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751592319731_127.0.0.1_9568
11:41:47.840 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592319731_127.0.0.1_9568
11:41:47.858 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Notify disconnected event to listeners
11:41:47.859 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Notify connected event to listeners.
11:41:47.876 [nacos-grpc-client-executor-1643] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751592319731_127.0.0.1_9568]Ignore complete event,isRunning:true,isAbandon=true
11:41:47.932 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Success to connect a server [localhost:8848], connectionId = 1751600507763_127.0.0.1_13712
11:41:47.932 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Abandon prev connection, server is localhost:8848, connectionId is 1751592320039_127.0.0.1_9575
11:41:47.932 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592320039_127.0.0.1_9575
11:41:47.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Notify disconnected event to listeners
11:41:47.937 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Notify connected event to listeners.
11:41:48.536 [nacos-grpc-client-executor-2662] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:41:48.552 [nacos-grpc-client-executor-2662] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 19
11:41:48.554 [nacos-grpc-client-executor-2663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:41:48.555 [nacos-grpc-client-executor-2663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 20
11:41:48.556 [nacos-grpc-client-executor-2664] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 21
11:41:48.659 [nacos-grpc-client-executor-2664] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 21
11:41:48.670 [nacos-grpc-client-executor-2665] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 23
11:41:48.673 [nacos-grpc-client-executor-2665] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 23
11:41:48.676 [nacos-grpc-client-executor-2666] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 24
11:41:48.677 [nacos-grpc-client-executor-2666] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 24
11:41:51.106 [nacos-grpc-client-executor-2667] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 25
11:41:51.132 [nacos-grpc-client-executor-2667] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:41:51.313 [nacos-grpc-client-executor-2668] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 27
11:41:51.329 [nacos-grpc-client-executor-2668] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 27
11:42:04.200 [nacos-grpc-client-executor-2673] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 30
11:42:04.272 [nacos-grpc-client-executor-2673] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 30
11:43:00.786 [nacos-grpc-client-executor-2689] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:43:00.830 [nacos-grpc-client-executor-2689] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:55:24.954 [nacos-grpc-client-executor-2917] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:55:24.973 [nacos-grpc-client-executor-2917] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:55:48.405 [nacos-grpc-client-executor-2922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 38
11:55:48.498 [nacos-grpc-client-executor-2922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 38
11:57:39.026 [nacos-grpc-client-executor-2955] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 41
11:57:39.047 [nacos-grpc-client-executor-2955] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 41
11:58:07.951 [nacos-grpc-client-executor-2963] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 44
11:58:07.975 [nacos-grpc-client-executor-2963] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 44
11:59:27.941 [nacos-grpc-client-executor-2988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 46
11:59:27.958 [nacos-grpc-client-executor-2988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 46
11:59:54.699 [nacos-grpc-client-executor-2995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 48
11:59:54.734 [nacos-grpc-client-executor-2995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 48
12:01:59.242 [nacos-grpc-client-executor-3032] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 51
12:01:59.261 [nacos-grpc-client-executor-3032] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 51
12:02:22.441 [nacos-grpc-client-executor-3037] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 53
12:02:22.471 [nacos-grpc-client-executor-3037] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 53
12:28:39.845 [nacos-grpc-client-executor-3543] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 56
12:28:39.877 [nacos-grpc-client-executor-3543] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 56
12:29:00.055 [nacos-grpc-client-executor-3550] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 58
12:29:00.103 [nacos-grpc-client-executor-3550] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 58
12:34:11.569 [nacos-grpc-client-executor-3650] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 61
12:34:11.591 [nacos-grpc-client-executor-3650] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 61
12:34:30.505 [nacos-grpc-client-executor-3660] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 63
12:34:30.519 [nacos-grpc-client-executor-3660] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 63
13:04:31.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Server healthy check fail, currentConnection = 1751600507723_127.0.0.1_13709
13:04:31.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:35.874 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Server healthy check fail, currentConnection = 1751592311238_127.0.0.1_9372
13:04:35.874 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:36.236 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Server healthy check fail, currentConnection = 1751600507763_127.0.0.1_13712
13:04:36.236 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:53.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
13:04:54.064 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
13:04:54.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Success to connect a server [localhost:8848], connectionId = 1751605494088_127.0.0.1_6545
13:04:54.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751592311238_127.0.0.1_9372
13:04:54.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592311238_127.0.0.1_9372
13:04:54.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Notify disconnected event to listeners
13:04:54.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80a64752-597e-40a2-b9e1-0bfc484f3917_config-0] Notify connected event to listeners.
13:04:54.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Success to connect a server [localhost:8848], connectionId = 1751605494112_127.0.0.1_6544
13:04:54.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751600507723_127.0.0.1_13709
13:04:54.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507723_127.0.0.1_13709
13:04:54.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Notify disconnected event to listeners
13:04:54.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23266205-a095-49ce-a17a-4cd673ef1c7b_config-0] Notify connected event to listeners.
13:04:54.379 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Success to connect a server [localhost:8848], connectionId = 1751605494268_127.0.0.1_6556
13:04:54.379 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Abandon prev connection, server is localhost:8848, connectionId is 1751600507763_127.0.0.1_13712
13:04:54.379 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507763_127.0.0.1_13712
13:04:54.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Notify disconnected event to listeners
13:04:54.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Notify connected event to listeners.
13:04:54.383 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Server check success, currentServer is localhost:8848 
13:04:58.893 [nacos-grpc-client-executor-4223] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 71
13:04:58.893 [nacos-grpc-client-executor-4223] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 71
13:04:58.951 [nacos-grpc-client-executor-4225] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 72
13:04:58.951 [nacos-grpc-client-executor-4225] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 72
13:04:58.967 [nacos-grpc-client-executor-4228] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 73
13:04:58.967 [nacos-grpc-client-executor-4228] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 73
13:04:59.465 [nacos-grpc-client-executor-4231] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 74
13:04:59.853 [nacos-grpc-client-executor-4231] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 74
13:04:59.864 [nacos-grpc-client-executor-4232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 76
13:04:59.864 [nacos-grpc-client-executor-4232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 76
13:04:59.870 [nacos-grpc-client-executor-4233] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 78
13:04:59.881 [nacos-grpc-client-executor-4233] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 78
13:21:17.309 [nacos-grpc-client-executor-4523] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 81
13:21:17.326 [nacos-grpc-client-executor-4523] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 81
13:21:34.958 [nacos-grpc-client-executor-4527] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Receive server push request, request = NotifySubscriberRequest, requestId = 83
13:21:35.350 [nacos-grpc-client-executor-4527] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47dbb285-b676-4390-9e21-176ba7b9c275] Ack server push request, request = NotifySubscriberRequest, requestId = 83
15:09:03.031 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:03.040 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:03.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:03.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63a76fb9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:03.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751605494268_127.0.0.1_6556
15:09:03.391 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d69b992[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 6389]
