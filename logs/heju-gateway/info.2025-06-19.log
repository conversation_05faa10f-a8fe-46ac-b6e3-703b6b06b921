11:33:54.497 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:33:55.233 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4b7d8b62-0453-4881-befc-f41dee81d704_config-0
11:33:55.300 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 35 ms to scan 1 urls, producing 3 keys and 6 values 
11:33:55.335 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
11:33:55.347 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:33:55.372 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 21 ms to scan 1 urls, producing 1 keys and 5 values 
11:33:55.389 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:33:55.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:33:55.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:33:55.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000018558388b08
11:33:55.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$465/0x0000018558388d28
11:33:55.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:33:55.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:33:55.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:33:59.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:02.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:05.133 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:34:05.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:34:05.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$479/0x00000185584ac430
11:34:06.817 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:34:10.937 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:34:11.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:12.479 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:34:13.081 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 702545d6-da66-4ad5-83ae-b690de52ac78_config-0
11:34:13.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:34:13.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000018558388b08
11:34:13.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$465/0x0000018558388d28
11:34:13.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:34:13.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:34:13.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:14.480 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:16.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:17.808 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:19.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:21.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:22.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:34:22.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:34:22.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$479/0x00000185584ac430
11:34:22.452 [main] INFO  c.a.c.s.d.c.SentinelConverter - [convert,79] - converter can not convert rules because source is empty
11:34:22.570 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ebc50b92-4186-45cc-b9cf-1a27cb9fccef
11:34:22.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] RpcClient init label, labels = {module=naming, source=sdk}
11:34:22.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:34:22.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:34:22.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:34:22.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:24.762 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:25.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:28.258 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:28.386 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:28.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:31.465 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:31.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:34:31.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:34:31.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$479/0x00000185584ac430
11:34:32.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:33.497 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:34:33.497 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e834a5b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:34:33.498 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7fda84cf[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
11:34:33.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebc50b92-4186-45cc-b9cf-1a27cb9fccef] Client is shutdown, stop reconnect to server
11:34:34.792 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702545d6-da66-4ad5-83ae-b690de52ac78_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:34:35.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b7d8b62-0453-4881-befc-f41dee81d704_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:37:44.417 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:37:45.171 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0
11:37:45.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
11:37:45.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
11:37:45.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:37:45.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
11:37:45.324 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
11:37:45.335 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:37:45.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:37:45.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002bec7388fc8
11:37:45.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$465/0x000002bec73891e8
11:37:45.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:37:45.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:37:45.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:37:46.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750304266296_127.0.0.1_13572
11:37:46.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Notify connected event to listeners.
11:37:46.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:37:46.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2512279d-08b1-47ef-9fed-c1dd79763a4d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$479/0x000002bec74d1ca0
11:37:46.899 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:37:51.113 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:37:52.547 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:37:53.192 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0
11:37:53.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:37:53.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002bec7388fc8
11:37:53.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$465/0x000002bec73891e8
11:37:53.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:37:53.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:37:53.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:37:53.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750304273211_127.0.0.1_13588
11:37:53.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:37:53.335 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Notify connected event to listeners.
11:37:53.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf3f792-d38c-4961-bfa0-5a5b0b160c55_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$479/0x000002bec74d1ca0
11:37:53.525 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f64752b9-1665-4a3d-b56b-4e5957e2d350
11:37:53.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] RpcClient init label, labels = {module=naming, source=sdk}
11:37:53.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:37:53.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:37:53.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:37:53.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:37:53.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750304273557_127.0.0.1_13589
11:37:53.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:37:53.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Notify connected event to listeners.
11:37:53.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$479/0x000002bec74d1ca0
11:37:54.363 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
11:37:54.418 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.802 seconds (JVM running for 11.789)
11:37:54.426 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
11:37:54.428 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
11:37:54.429 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
11:37:54.614 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Receive server push request, request = NotifySubscriberRequest, requestId = 1
11:37:54.614 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Ack server push request, request = NotifySubscriberRequest, requestId = 1
11:37:54.886 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Receive server push request, request = NotifySubscriberRequest, requestId = 2
11:37:54.886 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f64752b9-1665-4a3d-b56b-4e5957e2d350] Ack server push request, request = NotifySubscriberRequest, requestId = 2
11:47:26.497 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:47:26.504 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:47:26.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:47:26.819 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c2bd2fd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:47:26.819 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750304273557_127.0.0.1_13589
11:47:26.821 [nacos-grpc-client-executor-179] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750304273557_127.0.0.1_13589]Ignore complete event,isRunning:false,isAbandon=false
11:47:26.824 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6707bb76[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 180]
11:47:38.166 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:47:38.974 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 111e579e-9fd4-4d22-a179-2f30f530890e_config-0
11:47:39.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
11:47:39.101 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:47:39.112 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
11:47:39.126 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:47:39.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:47:39.160 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
11:47:39.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:47:39.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001595b3cc958
11:47:39.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001595b3ccb78
11:47:39.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:47:39.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:47:39.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:47:40.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750304860183_127.0.0.1_14731
11:47:40.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Notify connected event to listeners.
11:47:40.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:47:40.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001595b5069a8
11:47:40.690 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:47:45.241 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:47:46.936 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:47:47.758 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0
11:47:47.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:47:47.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001595b3cc958
11:47:47.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001595b3ccb78
11:47:47.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:47:47.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:47:47.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:47:47.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750304867774_127.0.0.1_14747
11:47:47.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:47:47.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Notify connected event to listeners.
11:47:47.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001595b5069a8
11:47:48.029 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 705183f3-9586-4fc6-b4e4-2357f2babdbe
11:47:48.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] RpcClient init label, labels = {module=naming, source=sdk}
11:47:48.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:47:48.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:47:48.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:47:48.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:47:48.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750304868047_127.0.0.1_14748
11:47:48.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Notify connected event to listeners.
11:47:48.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:47:48.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001595b5069a8
11:47:48.738 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 3
11:47:48.738 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 3
11:47:48.778 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
11:47:48.839 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.491 seconds (JVM running for 12.843)
11:47:48.872 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
11:47:48.873 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
11:47:48.875 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
11:47:49.364 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 4
11:47:49.390 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 4
11:52:19.152 [nacos-grpc-client-executor-92] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 6
11:52:19.154 [nacos-grpc-client-executor-92] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 6
11:54:19.157 [nacos-grpc-client-executor-132] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 11
11:54:19.158 [nacos-grpc-client-executor-132] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 11
11:54:19.172 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 12
11:54:19.173 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 12
11:54:19.185 [nacos-grpc-client-executor-134] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 10
11:54:19.187 [nacos-grpc-client-executor-134] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 10
11:54:40.668 [nacos-grpc-client-executor-142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 14
11:54:40.701 [nacos-grpc-client-executor-142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 14
13:19:27.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Server healthy check fail, currentConnection = 1750304867774_127.0.0.1_14747
13:19:27.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:19:27.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Server healthy check fail, currentConnection = 1750304860183_127.0.0.1_14731
13:19:27.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:19:31.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750310370704_127.0.0.1_14206
13:19:31.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750304867774_127.0.0.1_14747
13:19:31.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750304867774_127.0.0.1_14747
13:19:31.199 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Notify disconnected event to listeners
13:19:31.200 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4d066d-9fd1-422d-abff-8b267ba39ed5_config-0] Notify connected event to listeners.
13:19:31.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750310370707_127.0.0.1_14208
13:19:31.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750304860183_127.0.0.1_14731
13:19:31.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750304860183_127.0.0.1_14731
13:19:31.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Notify disconnected event to listeners
13:19:31.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [111e579e-9fd4-4d22-a179-2f30f530890e_config-0] Notify connected event to listeners.
13:19:33.577 [nacos-grpc-client-executor-1763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 17
13:19:33.815 [nacos-grpc-client-executor-1763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 17
13:19:36.345 [nacos-grpc-client-executor-1764] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Receive server push request, request = NotifySubscriberRequest, requestId = 19
13:19:36.380 [nacos-grpc-client-executor-1764] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [705183f3-9586-4fc6-b4e4-2357f2babdbe] Ack server push request, request = NotifySubscriberRequest, requestId = 19
15:45:38.154 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:45:38.166 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:45:38.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:45:38.505 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c51a1de[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:45:38.507 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750304868047_127.0.0.1_14748
15:45:38.507 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@57d5bb21[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 4529]
16:38:44.607 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:38:46.219 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0
16:38:46.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 90 ms to scan 1 urls, producing 3 keys and 6 values 
16:38:46.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 4 keys and 9 values 
16:38:46.495 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
16:38:46.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
16:38:46.559 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 1 keys and 7 values 
16:38:46.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
16:38:46.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:38:46.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000014b893cbda8
16:38:46.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000014b893cbfc8
16:38:46.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:38:46.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:38:46.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:48.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:48.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:48.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:38:48.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:48.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000014b894db938
16:38:48.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:49.060 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:49.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:49.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:50.314 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:50.589 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:38:50.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:51.669 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:52.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:53.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:54.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:55.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:57.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:58.541 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:58.937 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:39:00.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:01.933 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:02.062 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:39:03.176 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f3352a05-69ee-4043-aec4-468c891f2b79_config-0
16:39:03.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:39:03.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000014b893cbda8
16:39:03.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000014b893cbfc8
16:39:03.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:39:03.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:39:03.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:03.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:03.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000014b894db938
16:39:03.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.558 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.668 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:03.832 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8549fac5-eb67-4557-b35d-5e54a0c98b1d
16:39:03.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] RpcClient init label, labels = {module=naming, source=sdk}
16:39:03.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:03.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:03.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:03.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:03.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:03.865 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:03.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000014b894db938
16:39:03.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.000 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.218 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.400 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.549 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.912 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:05.262 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:05.478 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:05.531 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.240 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.289 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:06.292 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@515b7335[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:06.293 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8549fac5-eb67-4557-b35d-5e54a0c98b1d] Client is shutdown, stop reconnect to server
16:39:06.293 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@102dff25[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 20]
16:39:07.047 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:07.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:07.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:09.099 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3352a05-69ee-4043-aec4-468c891f2b79_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:09.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4370f36-bd7e-4f15-a18e-c9c9f5b6e63c_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:18.670 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:39:19.593 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0
16:39:19.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:39:19.734 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
16:39:19.746 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
16:39:19.758 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:39:19.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
16:39:19.787 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
16:39:19.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:39:19.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002e55c3cb8c8
16:39:19.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002e55c3cbae8
16:39:19.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:39:19.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:39:19.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:21.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:21.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:21.078 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:21.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:21.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002e55c4db0e8
16:39:21.215 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:21.436 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:21.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:22.172 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:22.689 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:22.869 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:39:23.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:24.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:24.861 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:25.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:26.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:28.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:28.469 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:39:29.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:30.220 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:39:30.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:31.108 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0
16:39:31.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:39:31.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002e55c3cb8c8
16:39:31.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002e55c3cbae8
16:39:31.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:39:31.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:39:31.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:31.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:31.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:31.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:31.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002e55c4db0e8
16:39:31.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:31.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:31.515 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:31.649 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dceff872-4ec4-4439-8dbd-8797b769fa09
16:39:31.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] RpcClient init label, labels = {module=naming, source=sdk}
16:39:31.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:31.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:31.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:31.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:31.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:31.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:31.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:31.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002e55c4db0e8
16:39:31.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:31.847 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:31.847 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:32.059 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:32.256 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:32.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:32.370 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:32.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:32.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:33.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:33.400 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:33.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:33.949 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:33.999 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:34.000 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5b2c9a69[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:34.000 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@19983dd1[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 20]
16:39:34.001 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dceff872-4ec4-4439-8dbd-8797b769fa09] Client is shutdown, stop reconnect to server
16:39:34.115 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:34.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:35.428 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3756de0f-e32a-46ee-9eae-c9b1b353a831_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:35.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:36.947 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d89b11f4-d206-4ce2-b3e4-89eb9527b94b_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:40:45.749 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:40:46.553 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0
16:40:46.640 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:46.677 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:46.688 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:46.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:46.719 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:46.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:46.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:46.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001e1e93bb8c8
16:40:46.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001e1e93bbae8
16:40:46.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:46.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:46.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:48.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322447829_127.0.0.1_9163
16:40:48.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Notify connected event to listeners.
16:40:48.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:48.136 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001e1e94f5418
16:40:48.380 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:40:53.852 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:40:55.526 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:40:56.399 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 04197402-c5c5-400d-ac8f-3b643cdacae7_config-0
16:40:56.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:56.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001e1e93bb8c8
16:40:56.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001e1e93bbae8
16:40:56.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:56.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:56.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:56.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322456414_127.0.0.1_9210
16:40:56.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:56.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001e1e94f5418
16:40:56.531 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Notify connected event to listeners.
16:40:56.699 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 226b3524-fcec-4b84-8426-9f09823bc896
16:40:56.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] RpcClient init label, labels = {module=naming, source=sdk}
16:40:56.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:40:56.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:40:56.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:40:56.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:56.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322456715_127.0.0.1_9212
16:40:56.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:56.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001e1e94f5418
16:40:56.836 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Notify connected event to listeners.
16:40:57.847 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Receive server push request, request = NotifySubscriberRequest, requestId = 1
16:40:57.848 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Ack server push request, request = NotifySubscriberRequest, requestId = 1
16:40:57.857 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
16:40:57.917 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 13.016 seconds (JVM running for 14.373)
16:40:57.927 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
16:40:57.929 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
16:40:57.931 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
16:40:58.371 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Receive server push request, request = NotifySubscriberRequest, requestId = 2
16:40:58.372 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Ack server push request, request = NotifySubscriberRequest, requestId = 2
16:41:27.647 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Receive server push request, request = NotifySubscriberRequest, requestId = 7
16:41:27.649 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Ack server push request, request = NotifySubscriberRequest, requestId = 7
16:41:27.671 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Receive server push request, request = NotifySubscriberRequest, requestId = 10
16:41:27.672 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Ack server push request, request = NotifySubscriberRequest, requestId = 10
16:41:27.700 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Receive server push request, request = NotifySubscriberRequest, requestId = 8
16:41:27.700 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Ack server push request, request = NotifySubscriberRequest, requestId = 8
16:41:27.726 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Receive server push request, request = NotifySubscriberRequest, requestId = 9
16:41:27.728 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Ack server push request, request = NotifySubscriberRequest, requestId = 9
17:08:20.637 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.643 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.649 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Server healthy check fail, currentConnection = 1750322456414_127.0.0.1_9210
17:08:20.649 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:20.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:20.856 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.058 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.058 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.072 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.384 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.796 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.796 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.941 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.662 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.507 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.562 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:08:24.889 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:08:25.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:08:25.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@32233759[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:08:25.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750322456715_127.0.0.1_9212
17:08:25.231 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [226b3524-fcec-4b84-8426-9f09823bc896] Client is shutdown, stop reconnect to server
17:08:25.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5f0be72f[Running, pool size = 21, active threads = 0, queued tasks = 0, completed tasks = 592]
17:08:25.387 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:25.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:26.408 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313ada1a-e9c1-47e4-8ccf-55a91dc6fac8_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:26.434 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04197402-c5c5-400d-ac8f-3b643cdacae7_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
