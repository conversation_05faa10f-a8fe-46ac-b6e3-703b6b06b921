09:20:27.767 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:20:28.328 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0
09:20:28.395 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:20:28.419 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:20:28.425 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:20:28.435 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:20:28.442 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:20:28.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:20:28.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:28.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000020b553cb8c8
09:20:28.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000020b553cbae8
09:20:28.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:28.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:28.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:29.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751851229141_127.0.0.1_10193
09:20:29.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Notify connected event to listeners.
09:20:29.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:29.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b356c2-6d4b-4e42-a942-3d3747e8e5c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000020b55505418
09:20:29.530 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:20:32.493 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:20:33.612 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:20:34.146 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0
09:20:34.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:34.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000020b553cb8c8
09:20:34.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000020b553cbae8
09:20:34.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:34.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:34.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:34.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751851234157_127.0.0.1_10256
09:20:34.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:34.270 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Notify connected event to listeners.
09:20:34.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfe272a7-cd15-44b6-a296-8bc0fe417575_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000020b55505418
09:20:34.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fb356759-2050-42cb-95de-1deb98c9b993
09:20:34.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] RpcClient init label, labels = {module=naming, source=sdk}
09:20:34.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:20:34.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:20:34.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:20:34.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:34.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Success to connect to server [localhost:8848] on start up, connectionId = 1751851234437_127.0.0.1_10257
09:20:34.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:34.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000020b55505418
09:20:34.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Notify connected event to listeners.
09:20:35.149 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:20:35.200 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.115 seconds (JVM running for 10.455)
09:20:35.213 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:20:35.214 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:20:35.215 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:20:35.477 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:20:35.478 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:20:35.678 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:20:35.679 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:21:05.332 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:21:05.333 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:21:35.356 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:21:35.356 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:21:35.356 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:21:35.356 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:21:35.356 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:21:35.356 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb356759-2050-42cb-95de-1deb98c9b993] Ack server push request, request = NotifySubscriberRequest, requestId = 10
13:30:03.917 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:30:03.978 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:30:04.325 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:30:04.327 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1fb935bf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:30:04.327 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751851234437_127.0.0.1_10257
13:30:04.327 [nacos-grpc-client-executor-4985] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751851234437_127.0.0.1_10257]Ignore complete event,isRunning:false,isAbandon=false
13:30:04.337 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7dd98521[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4986]
15:21:03.366 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:05.365 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a938c023-aa8a-46e0-a76f-e9165e843d32_config-0
15:21:05.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 95 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:05.631 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:05.656 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:05.697 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:05.726 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:05.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:05.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:05.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d9a93b4fb8
15:21:05.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d9a93b51d8
15:21:05.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:05.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:05.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:08.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751872868443_127.0.0.1_11747
15:21:08.934 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Notify connected event to listeners.
15:21:08.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:08.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a938c023-aa8a-46e0-a76f-e9165e843d32_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d9a94ecf98
15:21:09.430 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:22.906 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:21:28.952 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:21:31.072 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0
15:21:31.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:31.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d9a93b4fb8
15:21:31.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d9a93b51d8
15:21:31.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:31.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:31.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:31.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751872891105_127.0.0.1_11876
15:21:31.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:31.234 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Notify connected event to listeners.
15:21:31.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76ce6e-8431-412f-a01d-e68d462a3ffa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d9a94ecf98
15:21:31.600 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d
15:21:31.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] RpcClient init label, labels = {module=naming, source=sdk}
15:21:31.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:21:31.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:21:31.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:21:31.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:31.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Success to connect to server [localhost:8848] on start up, connectionId = 1751872891639_127.0.0.1_11877
15:21:31.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:31.777 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Notify connected event to listeners.
15:21:31.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d9a94ecf98
15:21:32.411 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 13
15:21:32.413 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 13
15:21:33.623 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
15:21:33.765 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 32.437 seconds (JVM running for 40.15)
15:21:33.793 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:21:33.796 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:21:33.799 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:21:34.215 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 14
15:21:34.216 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 14
15:22:02.876 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 17
15:22:02.876 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 17
15:22:02.892 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 18
15:22:02.893 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 18
15:22:32.800 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 21
15:22:32.801 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 21
15:22:32.807 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 22
15:22:32.807 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 22
15:36:43.581 [nacos-grpc-client-executor-319] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 23
15:36:43.596 [nacos-grpc-client-executor-319] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 23
15:37:17.096 [nacos-grpc-client-executor-329] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 25
15:37:17.110 [nacos-grpc-client-executor-329] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 25
15:41:28.279 [nacos-grpc-client-executor-409] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 26
15:41:28.299 [nacos-grpc-client-executor-409] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 26
15:42:00.804 [nacos-grpc-client-executor-421] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Receive server push request, request = NotifySubscriberRequest, requestId = 27
15:42:00.821 [nacos-grpc-client-executor-421] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cef6aa-6aea-4ecf-a7b0-7a2c8c8d089d] Ack server push request, request = NotifySubscriberRequest, requestId = 27
15:43:55.639 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:43:55.642 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:43:55.978 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:43:55.980 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d23cf1f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:43:55.980 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751872891639_127.0.0.1_11877
15:43:55.982 [nacos-grpc-client-executor-460] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751872891639_127.0.0.1_11877]Ignore complete event,isRunning:false,isAbandon=false
15:43:55.983 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@19e80706[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 461]
15:45:54.746 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:45:58.362 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0
15:45:58.704 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 188 ms to scan 1 urls, producing 3 keys and 6 values 
15:45:58.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 63 ms to scan 1 urls, producing 4 keys and 9 values 
15:45:58.966 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 72 ms to scan 1 urls, producing 3 keys and 10 values 
15:45:59.019 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 1 keys and 5 values 
15:45:59.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 1 keys and 7 values 
15:45:59.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 2 keys and 8 values 
15:45:59.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:45:59.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002bcb33783f0
15:45:59.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002bcb3378610
15:45:59.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:45:59.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:45:59.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:05.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874364778_127.0.0.1_3799
15:46:05.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Notify connected event to listeners.
15:46:05.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:46:05.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0f23f8d-a058-4fda-bdf9-2afac8a02a8f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002bcb34f4688
15:46:06.011 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:47:17.640 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:47:47.373 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:47:56.289 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0
15:47:56.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:47:56.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002bcb33783f0
15:47:56.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002bcb3378610
15:47:56.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:47:56.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:47:56.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:47:57.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874476456_127.0.0.1_4466
15:47:57.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:47:57.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002bcb34f4688
15:47:57.593 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3e00ecf-f3c2-4646-8563-42e729ca0600_config-0] Notify connected event to listeners.
15:47:59.757 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2fe00c73-2afc-4a64-92d0-d2982169838d
15:47:59.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] RpcClient init label, labels = {module=naming, source=sdk}
15:47:59.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:47:59.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:47:59.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:47:59.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:01.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Success to connect to server [localhost:8848] on start up, connectionId = 1751874479844_127.0.0.1_4478
15:48:01.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:01.270 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Notify connected event to listeners.
15:48:01.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002bcb34f4688
15:48:03.007 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 30
15:48:03.020 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 30
15:48:05.214 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 32
15:48:05.215 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 32
15:48:07.301 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
15:48:07.351 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 136.689 seconds (JVM running for 144.032)
15:48:07.362 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:48:07.363 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:48:07.365 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:48:07.872 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 33
15:48:07.872 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 33
15:48:35.885 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 36
15:48:35.886 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 36
15:48:35.896 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 37
15:48:35.897 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 37
15:48:44.770 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 39
15:48:44.778 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 39
16:28:50.307 [nacos-grpc-client-executor-759] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 40
16:28:50.310 [nacos-grpc-client-executor-759] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 40
16:29:08.519 [nacos-grpc-client-executor-766] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 41
16:29:08.533 [nacos-grpc-client-executor-766] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 41
16:39:05.822 [nacos-grpc-client-executor-943] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 45
16:39:05.840 [nacos-grpc-client-executor-943] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 45
16:39:24.385 [nacos-grpc-client-executor-951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 47
16:39:24.438 [nacos-grpc-client-executor-951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 47
17:19:55.072 [nacos-grpc-client-executor-1677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 50
17:19:55.090 [nacos-grpc-client-executor-1677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 50
17:20:00.912 [nacos-grpc-client-executor-1678] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Receive server push request, request = NotifySubscriberRequest, requestId = 52
17:20:00.929 [nacos-grpc-client-executor-1678] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fe00c73-2afc-4a64-92d0-d2982169838d] Ack server push request, request = NotifySubscriberRequest, requestId = 52
19:02:19.510 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:19.510 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:19.848 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:19.850 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@320e18f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:19.850 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874479844_127.0.0.1_4478
19:02:19.852 [nacos-grpc-client-executor-3525] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751874479844_127.0.0.1_4478]Ignore complete event,isRunning:false,isAbandon=false
19:02:19.861 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60e1bb53[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 3526]
