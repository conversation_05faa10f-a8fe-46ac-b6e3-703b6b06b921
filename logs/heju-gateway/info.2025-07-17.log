09:12:24.170 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:24.988 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 813acec5-efc7-4d45-9254-62ad8b5fe486_config-0
09:12:25.074 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 31 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:25.101 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:25.110 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:25.122 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:25.138 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:25.149 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:25.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:25.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000026bc03cb650
09:12:25.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000026bc03cb870
09:12:25.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:25.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:25.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:26.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752714746133_127.0.0.1_7161
09:12:26.398 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Notify connected event to listeners.
09:12:26.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:26.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813acec5-efc7-4d45-9254-62ad8b5fe486_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000026bc0505490
09:12:26.602 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:31.667 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:12:33.797 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:12:35.016 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 50121afc-046b-414c-8e5d-786724b9fe97_config-0
09:12:35.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:35.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000026bc03cb650
09:12:35.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000026bc03cb870
09:12:35.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:35.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:35.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:35.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752714755031_127.0.0.1_7188
09:12:35.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:35.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Notify connected event to listeners.
09:12:35.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50121afc-046b-414c-8e5d-786724b9fe97_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000026bc0505490
09:12:35.416 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 363d421e-2a3a-4e0f-8f79-c1b888d98f77
09:12:35.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] RpcClient init label, labels = {module=naming, source=sdk}
09:12:35.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:35.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:35.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:35.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:35.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Success to connect to server [localhost:8848] on start up, connectionId = 1752714755441_127.0.0.1_7190
09:12:35.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:35.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000026bc0505490
09:12:35.568 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Notify connected event to listeners.
09:12:36.435 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:12:36.437 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:12:36.464 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.17:8081 register finished
09:12:36.519 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 13.191 seconds (JVM running for 16.045)
09:12:36.529 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:12:36.531 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:12:36.531 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:12:37.024 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:12:37.026 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:13:06.382 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:13:06.383 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:13:06.392 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:13:06.392 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:13:06.400 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:13:06.400 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:15:36.464 [nacos-grpc-client-executor-92] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:15:36.464 [nacos-grpc-client-executor-92] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [363d421e-2a3a-4e0f-8f79-c1b888d98f77] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:32:30.748 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:32:30.756 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:32:31.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:32:31.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f5945c2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:32:31.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752714755441_127.0.0.1_7190
09:32:31.093 [nacos-grpc-client-executor-416] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752714755441_127.0.0.1_7190]Ignore complete event,isRunning:false,isAbandon=false
09:32:31.100 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@50449813[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 417]
09:59:00.529 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:59:01.332 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0
09:59:01.422 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
09:59:01.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:59:01.464 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:59:01.479 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:59:01.492 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:59:01.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:59:01.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:59:01.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000201813b8d60
09:59:01.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000201813b8f80
09:59:01.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:59:01.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:59:01.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:59:02.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752717542454_127.0.0.1_14638
09:59:02.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Notify connected event to listeners.
09:59:02.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:59:02.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94f5e55d-d07b-4119-a6be-8abb5e2185eb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000201814f0668
09:59:02.858 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:59:11.410 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:59:15.900 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:59:16.991 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0
09:59:16.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:59:16.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000201813b8d60
09:59:16.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000201813b8f80
09:59:16.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:59:16.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:59:16.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:59:17.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752717557004_127.0.0.1_14676
09:59:17.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:59:17.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000201814f0668
09:59:17.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cd13a22-4f49-4cc0-bb4d-3184383ad0c0_config-0] Notify connected event to listeners.
09:59:17.272 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b5b7f5fb-dc0c-4716-9554-5e0d939271de
09:59:17.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] RpcClient init label, labels = {module=naming, source=sdk}
09:59:17.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:59:17.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:59:17.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:59:17.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:59:17.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Success to connect to server [localhost:8848] on start up, connectionId = 1752717557286_127.0.0.1_14680
09:59:17.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:59:17.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000201814f0668
09:59:17.409 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Notify connected event to listeners.
09:59:17.974 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:59:17.975 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:59:18.169 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:59:18.170 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:59:18.186 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:59:18.187 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:59:18.355 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.17:8081 register finished
09:59:18.409 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 18.656 seconds (JVM running for 21.571)
09:59:18.419 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:59:18.421 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:59:18.422 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:59:18.923 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:59:18.924 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:59:48.327 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:59:48.331 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:00:18.311 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:00:18.313 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 23
10:15:14.760 [nacos-grpc-client-executor-336] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:15:14.760 [nacos-grpc-client-executor-336] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 24
12:27:05.725 [nacos-grpc-client-executor-3049] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 28
12:27:05.742 [nacos-grpc-client-executor-3049] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 28
12:27:47.525 [nacos-grpc-client-executor-3063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 31
12:27:47.541 [nacos-grpc-client-executor-3063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 31
12:36:50.435 [nacos-grpc-client-executor-3249] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 33
12:36:50.448 [nacos-grpc-client-executor-3249] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 33
12:37:06.629 [nacos-grpc-client-executor-3252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 36
12:37:06.642 [nacos-grpc-client-executor-3252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:31:34.039 [nacos-grpc-client-executor-5601] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:31:34.066 [nacos-grpc-client-executor-5601] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:32:04.428 [nacos-grpc-client-executor-5611] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 41
14:32:04.450 [nacos-grpc-client-executor-5611] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 41
15:20:54.421 [nacos-grpc-client-executor-6561] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 43
15:20:54.446 [nacos-grpc-client-executor-6561] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 43
15:21:19.537 [nacos-grpc-client-executor-6566] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 46
15:21:19.556 [nacos-grpc-client-executor-6566] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 46
15:27:03.199 [nacos-grpc-client-executor-6683] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 48
15:27:03.213 [nacos-grpc-client-executor-6683] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 48
15:27:26.929 [nacos-grpc-client-executor-6695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 51
15:27:26.953 [nacos-grpc-client-executor-6695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 51
15:30:57.455 [nacos-grpc-client-executor-6760] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 53
15:30:57.482 [nacos-grpc-client-executor-6760] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 53
15:31:23.346 [nacos-grpc-client-executor-6769] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 56
15:31:23.369 [nacos-grpc-client-executor-6769] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 56
15:34:23.299 [nacos-grpc-client-executor-6828] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 58
15:34:23.332 [nacos-grpc-client-executor-6828] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 58
15:34:48.780 [nacos-grpc-client-executor-6836] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 61
15:34:48.800 [nacos-grpc-client-executor-6836] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 61
15:57:18.396 [nacos-grpc-client-executor-7259] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 63
15:57:18.418 [nacos-grpc-client-executor-7259] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 63
15:57:42.422 [nacos-grpc-client-executor-7270] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 66
15:57:42.443 [nacos-grpc-client-executor-7270] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 66
18:13:31.147 [nacos-grpc-client-executor-9841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 68
18:13:31.170 [nacos-grpc-client-executor-9841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 68
18:13:59.889 [nacos-grpc-client-executor-9849] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 70
18:13:59.905 [nacos-grpc-client-executor-9849] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 70
20:26:58.607 [nacos-grpc-client-executor-12356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 76
20:26:58.629 [nacos-grpc-client-executor-12356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 76
20:27:24.976 [nacos-grpc-client-executor-12363] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 80
20:27:25.002 [nacos-grpc-client-executor-12363] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 80
20:28:29.828 [nacos-grpc-client-executor-12388] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 83
20:28:29.850 [nacos-grpc-client-executor-12388] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 83
20:28:55.890 [nacos-grpc-client-executor-12394] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Receive server push request, request = NotifySubscriberRequest, requestId = 87
20:28:55.909 [nacos-grpc-client-executor-12394] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b7f5fb-dc0c-4716-9554-5e0d939271de] Ack server push request, request = NotifySubscriberRequest, requestId = 87
20:41:40.198 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:40.201 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:40.532 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:40.532 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@166fde92[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:40.532 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752717557286_127.0.0.1_14680
20:41:40.534 [nacos-grpc-client-executor-12628] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752717557286_127.0.0.1_14680]Ignore complete event,isRunning:false,isAbandon=false
20:41:40.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6525373e[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 12629]
