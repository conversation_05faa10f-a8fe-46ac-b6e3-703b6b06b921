09:06:41.027 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:06:41.832 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0
09:06:41.927 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 3 keys and 6 values 
09:06:41.962 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:06:41.975 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:06:41.989 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:06:42.004 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:06:42.018 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:06:42.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:42.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000235643b42b8
09:06:42.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000235643b44d8
09:06:42.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:42.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:42.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:43.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:43.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:43.447 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:06:43.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:43.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000235644c6228
09:06:43.573 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:43.792 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:44.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:44.534 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:45.190 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:45.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:46.156 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:46.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:47.742 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:48.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:49.832 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:51.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Success to connect a server [localhost:8848], connectionId = 1752800811042_127.0.0.1_6285
09:06:51.177 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd12cd7d-21b8-4598-a401-3e3c72407dfa_config-0] Notify connected event to listeners.
09:06:52.150 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:06:54.329 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:06:55.910 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0
09:06:55.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:55.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000235643b42b8
09:06:55.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000235643b44d8
09:06:55.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:55.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:55.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:56.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752800815935_127.0.0.1_6329
09:06:56.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:56.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Notify connected event to listeners.
09:06:56.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfde3d1f-7478-4bd9-bfe7-3d0421899341_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000235644c6228
09:06:56.377 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0dfbd9e7-2aad-4644-a153-9dab351bf174
09:06:56.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] RpcClient init label, labels = {module=naming, source=sdk}
09:06:56.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:56.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:56.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:56.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:56.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Success to connect to server [localhost:8848] on start up, connectionId = 1752800816409_127.0.0.1_6331
09:06:56.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:56.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Notify connected event to listeners.
09:06:56.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000235644c6228
09:06:57.619 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:06:57.662 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:06:57.664 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:06:57.681 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.564 seconds (JVM running for 18.969)
09:06:57.692 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:06:57.693 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:06:57.694 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:06:58.266 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:06:58.268 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:07:27.350 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:07:27.351 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:07:27.373 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:07:27.379 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:07:27.542 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:07:27.543 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:07:27.559 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:07:27.560 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:14:31.960 [nacos-grpc-client-executor-190] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:14:31.976 [nacos-grpc-client-executor-190] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:15:24.912 [nacos-grpc-client-executor-204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:15:24.924 [nacos-grpc-client-executor-204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:04:03.254 [nacos-grpc-client-executor-1155] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:04:03.267 [nacos-grpc-client-executor-1155] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 24
10:04:32.835 [nacos-grpc-client-executor-1166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 28
10:04:32.848 [nacos-grpc-client-executor-1166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:40:27.492 [nacos-grpc-client-executor-6302] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:40:27.518 [nacos-grpc-client-executor-6302] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:40:52.417 [nacos-grpc-client-executor-6313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 39
14:40:52.439 [nacos-grpc-client-executor-6313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 39
17:45:10.393 [nacos-grpc-client-executor-9714] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 43
17:45:10.393 [nacos-grpc-client-executor-9714] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 43
17:45:52.376 [nacos-grpc-client-executor-9727] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 48
17:45:52.390 [nacos-grpc-client-executor-9727] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 48
18:03:04.796 [nacos-grpc-client-executor-10050] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 52
18:03:04.812 [nacos-grpc-client-executor-10050] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 52
18:03:21.446 [nacos-grpc-client-executor-10057] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 57
18:03:21.460 [nacos-grpc-client-executor-10057] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 57
18:05:31.062 [nacos-grpc-client-executor-10094] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 61
18:05:31.067 [nacos-grpc-client-executor-10094] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 61
18:05:47.869 [nacos-grpc-client-executor-10102] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 66
18:05:47.884 [nacos-grpc-client-executor-10102] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 66
18:13:42.274 [nacos-grpc-client-executor-10237] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 71
18:13:42.287 [nacos-grpc-client-executor-10237] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 71
18:13:58.559 [nacos-grpc-client-executor-10240] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 76
18:13:58.577 [nacos-grpc-client-executor-10240] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 76
18:18:32.559 [nacos-grpc-client-executor-10317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 82
18:18:32.577 [nacos-grpc-client-executor-10317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 82
18:18:35.965 [nacos-grpc-client-executor-10321] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 89
18:18:35.981 [nacos-grpc-client-executor-10321] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 89
18:21:37.910 [nacos-grpc-client-executor-10375] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Receive server push request, request = NotifySubscriberRequest, requestId = 93
18:21:37.926 [nacos-grpc-client-executor-10375] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dfbd9e7-2aad-4644-a153-9dab351bf174] Ack server push request, request = NotifySubscriberRequest, requestId = 93
18:21:46.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:21:46.921 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:21:47.264 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:21:47.264 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@283b5a06[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:21:47.264 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752800816409_127.0.0.1_6331
18:21:47.264 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@123d5ce2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 10378]
18:23:47.877 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:23:48.262 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a379aa33-0205-489e-bb42-2616ad4e2829_config-0
18:23:48.316 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 6 values 
18:23:48.342 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
18:23:48.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
18:23:48.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
18:23:48.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
18:23:48.367 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
18:23:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:23:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001b39c3b8638
18:23:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001b39c3b8858
18:23:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:23:48.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:23:48.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:23:49.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752834228841_127.0.0.1_13655
18:23:49.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Notify connected event to listeners.
18:23:49.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:23:49.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a379aa33-0205-489e-bb42-2616ad4e2829_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001b39c4f0228
18:23:49.100 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:23:51.128 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
18:23:51.872 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
18:23:52.209 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 627da07a-86ab-47a0-85c2-0fc22de6a478_config-0
18:23:52.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:23:52.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001b39c3b8638
18:23:52.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001b39c3b8858
18:23:52.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:23:52.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:23:52.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:23:52.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752834232217_127.0.0.1_13661
18:23:52.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:23:52.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001b39c4f0228
18:23:52.325 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [627da07a-86ab-47a0-85c2-0fc22de6a478_config-0] Notify connected event to listeners.
18:23:52.400 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0112667b-1c42-4494-9ff3-882d233a71ae
18:23:52.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] RpcClient init label, labels = {module=naming, source=sdk}
18:23:52.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:23:52.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:23:52.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:23:52.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:23:52.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Success to connect to server [localhost:8848] on start up, connectionId = 1752834232409_127.0.0.1_13662
18:23:52.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:23:52.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Notify connected event to listeners.
18:23:52.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001b39c4f0228
18:23:52.814 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
18:23:52.840 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 5.352 seconds (JVM running for 7.495)
18:23:52.846 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
18:23:52.846 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
18:23:52.846 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
18:23:53.122 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Receive server push request, request = NotifySubscriberRequest, requestId = 98
18:23:53.122 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Ack server push request, request = NotifySubscriberRequest, requestId = 98
18:23:53.127 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Receive server push request, request = NotifySubscriberRequest, requestId = 100
18:23:53.128 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Ack server push request, request = NotifySubscriberRequest, requestId = 100
18:23:53.134 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Receive server push request, request = NotifySubscriberRequest, requestId = 99
18:23:53.135 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Ack server push request, request = NotifySubscriberRequest, requestId = 99
18:23:53.140 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Receive server push request, request = NotifySubscriberRequest, requestId = 101
18:23:53.140 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Ack server push request, request = NotifySubscriberRequest, requestId = 101
18:23:53.148 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Receive server push request, request = NotifySubscriberRequest, requestId = 102
18:23:53.150 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0112667b-1c42-4494-9ff3-882d233a71ae] Ack server push request, request = NotifySubscriberRequest, requestId = 102
18:47:19.353 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:47:19.355 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:47:19.691 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:47:19.691 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@59dae551[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:47:19.691 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752834232409_127.0.0.1_13662
18:47:19.693 [nacos-grpc-client-executor-519] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752834232409_127.0.0.1_13662]Ignore complete event,isRunning:false,isAbandon=false
18:47:19.699 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@61c439fd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 520]
