09:15:57.946 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:15:59.499 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0
09:15:59.661 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 72 ms to scan 1 urls, producing 3 keys and 6 values 
09:15:59.712 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
09:15:59.734 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:15:59.754 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:15:59.772 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:15:59.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:15:59.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:59.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000017b5a3cb8c8
09:15:59.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000017b5a3cbae8
09:15:59.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:59.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:59.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:01.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:01.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000017b5a4db0e8
09:16:01.808 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.026 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.765 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.448 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:16:03.918 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:04.627 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:05.498 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:06.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:07.428 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:08.687 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:10.029 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:10.927 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:16:11.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:12.792 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:16:13.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:13.954 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eb695df6-864c-4887-a849-f50e7e49c6dc_config-0
09:16:13.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:16:13.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000017b5a3cb8c8
09:16:13.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000017b5a3cbae8
09:16:13.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:16:13.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:16:13.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:13.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:13.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:13.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:13.980 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:13.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000017b5a4db0e8
09:16:14.111 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.420 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 625dfa31-fb4a-4779-a8ff-3df5c626d531
09:16:14.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] RpcClient init label, labels = {module=naming, source=sdk}
09:16:14.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:16:14.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:16:14.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:16:14.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:14.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:14.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:14.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:14.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:14.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000017b5a4db0e8
09:16:14.554 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.963 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.060 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.275 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.175 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.775 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:16:16.776 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6c2063e0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:16:16.776 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f3672eb[Running, pool size = 18, active threads = 0, queued tasks = 0, completed tasks = 18]
09:16:16.776 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [625dfa31-fb4a-4779-a8ff-3df5c626d531] Client is shutdown, stop reconnect to server
09:16:16.907 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.708 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:18.616 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:19.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e167634-16e4-41a1-8a74-a52bfac43dc2_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:19.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb695df6-864c-4887-a849-f50e7e49c6dc_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:18:28.223 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:28.903 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0
09:18:28.970 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:28.996 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:29.004 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:29.016 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:29.029 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:29.039 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:29.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:29.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019c953bc2b8
09:18:29.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019c953bc4d8
09:18:29.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:29.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:29.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:30.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752110310028_127.0.0.1_4181
09:18:30.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Notify connected event to listeners.
09:18:30.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:30.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d751ab8-f2b3-4af3-a144-dc10bd2dc418_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019c954f6350
09:18:30.540 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:35.026 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:18:36.605 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:18:37.285 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0
09:18:37.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:37.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019c953bc2b8
09:18:37.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019c953bc4d8
09:18:37.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:37.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:37.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:37.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752110317300_127.0.0.1_4234
09:18:37.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:37.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019c954f6350
09:18:37.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cd9b13-7bb0-485c-af3e-6219797d108b_config-0] Notify connected event to listeners.
09:18:37.581 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 510768dc-5963-49c1-9400-483882455c77
09:18:37.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] RpcClient init label, labels = {module=naming, source=sdk}
09:18:37.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:37.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:37.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:37.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:37.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Success to connect to server [localhost:8848] on start up, connectionId = 1752110317599_127.0.0.1_4236
09:18:37.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:37.718 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Notify connected event to listeners.
09:18:37.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019c954f6350
09:18:38.411 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
09:18:38.466 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.847 seconds (JVM running for 11.97)
09:18:38.474 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:18:38.476 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:18:38.477 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:18:38.760 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:18:38.761 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:18:38.939 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:18:38.940 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:19:08.513 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:19:08.513 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:19:08.544 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:19:08.544 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:19:08.553 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:19:08.553 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:19:08.566 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:19:08.566 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:40:38.889 [nacos-grpc-client-executor-478] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:40:38.889 [nacos-grpc-client-executor-478] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:53:42.075 [nacos-grpc-client-executor-759] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:53:42.098 [nacos-grpc-client-executor-759] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:54:01.377 [nacos-grpc-client-executor-763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:54:01.398 [nacos-grpc-client-executor-763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:56:01.896 [nacos-grpc-client-executor-803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:56:01.913 [nacos-grpc-client-executor-803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:10:17.738 [nacos-grpc-client-executor-1084] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:10:17.752 [nacos-grpc-client-executor-1084] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:54:28.121 [nacos-grpc-client-executor-1986] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:54:28.133 [nacos-grpc-client-executor-1986] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 19
11:48:11.731 [nacos-grpc-client-executor-3079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:48:11.732 [nacos-grpc-client-executor-3079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [510768dc-5963-49c1-9400-483882455c77] Ack server push request, request = NotifySubscriberRequest, requestId = 22
13:38:02.055 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:02.060 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:02.397 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:02.398 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d6c474c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:02.398 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752110317599_127.0.0.1_4236
13:38:02.402 [nacos-grpc-client-executor-5382] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752110317599_127.0.0.1_4236]Ignore complete event,isRunning:false,isAbandon=false
13:38:02.412 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b75c288[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5383]
13:45:23.418 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:45:24.844 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d96ee5b-6373-493f-8f09-616eef931ef1_config-0
13:45:24.999 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 83 ms to scan 1 urls, producing 3 keys and 6 values 
13:45:25.083 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 4 keys and 9 values 
13:45:25.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
13:45:25.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 5 values 
13:45:25.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
13:45:25.191 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
13:45:25.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:45:25.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001c2693cc2b8
13:45:25.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001c2693cc4d8
13:45:25.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:45:25.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:45:25.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:27.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752126327625_127.0.0.1_10468
13:45:27.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:27.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Notify connected event to listeners.
13:45:27.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d96ee5b-6373-493f-8f09-616eef931ef1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001c269506350
13:45:28.231 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:45:34.418 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:45:36.076 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:45:37.360 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 327343d5-2463-49fa-b2a6-a88d3c723d47_config-0
13:45:37.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:45:37.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001c2693cc2b8
13:45:37.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001c2693cc4d8
13:45:37.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:45:37.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:45:37.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:37.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752126337391_127.0.0.1_10503
13:45:37.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:37.539 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Notify connected event to listeners.
13:45:37.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [327343d5-2463-49fa-b2a6-a88d3c723d47_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001c269506350
13:45:37.739 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cdaf2e0d-4727-48ae-b118-b670ed2581c7
13:45:37.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] RpcClient init label, labels = {module=naming, source=sdk}
13:45:37.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:45:37.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:45:37.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:45:37.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:37.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Success to connect to server [localhost:8848] on start up, connectionId = 1752126337756_127.0.0.1_10504
13:45:37.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:37.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Notify connected event to listeners.
13:45:37.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001c269506350
13:45:38.444 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 27
13:45:38.444 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 27
13:45:38.727 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 29
13:45:38.729 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 29
13:45:38.737 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 28
13:45:38.737 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 28
13:45:38.773 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 30
13:45:38.773 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 30
13:45:39.102 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
13:45:39.175 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.33 seconds (JVM running for 20.349)
13:45:39.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
13:45:39.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
13:45:39.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
13:45:39.697 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 31
13:45:39.697 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 31
13:46:08.767 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 36
13:46:08.767 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 36
13:46:08.774 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Receive server push request, request = NotifySubscriberRequest, requestId = 35
13:46:08.775 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdaf2e0d-4727-48ae-b118-b670ed2581c7] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:20:37.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:37.884 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:38.208 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:38.209 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6b6b6731[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:38.209 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752126337756_127.0.0.1_10504
14:20:38.210 [nacos-grpc-client-executor-747] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752126337756_127.0.0.1_10504]Ignore complete event,isRunning:false,isAbandon=false
14:20:38.215 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@76e0c03e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 748]
14:31:31.542 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:32.171 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f992de74-851c-4c48-98b6-5918190dd85e_config-0
14:31:32.235 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:32.264 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:32.273 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:32.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:32.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:32.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:32.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:32.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019e393bb8c8
14:31:32.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019e393bbae8
14:31:32.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:32.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:32.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:33.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752129093204_127.0.0.1_1591
14:31:33.426 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Notify connected event to listeners.
14:31:33.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:33.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f992de74-851c-4c48-98b6-5918190dd85e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019e394f5418
14:31:33.555 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:37.280 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:31:38.615 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:31:39.322 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0
14:31:39.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:39.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019e393bb8c8
14:31:39.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019e393bbae8
14:31:39.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:39.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:39.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:39.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752129099337_127.0.0.1_1612
14:31:39.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:39.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019e394f5418
14:31:39.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e85fa042-4648-4fd7-8b0d-34f22e8e4780_config-0] Notify connected event to listeners.
14:31:39.601 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2671779-1618-4872-9c0b-704ad5eaf2dc
14:31:39.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] RpcClient init label, labels = {module=naming, source=sdk}
14:31:39.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:39.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:39.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:39.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:39.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Success to connect to server [localhost:8848] on start up, connectionId = 1752129099618_127.0.0.1_1616
14:31:39.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Notify connected event to listeners.
14:31:39.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:39.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019e394f5418
14:31:40.271 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
14:31:40.300 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:31:40.314 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:31:40.315 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 9.374 seconds (JVM running for 10.46)
14:31:40.339 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:31:40.340 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:31:40.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:31:44.865 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:31:44.867 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:32:02.290 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:32:02.334 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:32:10.476 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Receive server push request, request = NotifySubscriberRequest, requestId = 44
14:32:10.476 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Ack server push request, request = NotifySubscriberRequest, requestId = 44
14:32:10.478 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Receive server push request, request = NotifySubscriberRequest, requestId = 45
14:32:10.478 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Ack server push request, request = NotifySubscriberRequest, requestId = 45
14:32:10.497 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Receive server push request, request = NotifySubscriberRequest, requestId = 46
14:32:10.497 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2671779-1618-4872-9c0b-704ad5eaf2dc] Ack server push request, request = NotifySubscriberRequest, requestId = 46
14:58:53.110 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:58:53.114 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:58:53.449 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:58:53.449 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1977fc41[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:58:53.449 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752129099618_127.0.0.1_1616
14:58:53.452 [nacos-grpc-client-executor-543] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752129099618_127.0.0.1_1616]Ignore complete event,isRunning:false,isAbandon=false
14:58:53.456 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d3049a6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 544]
15:29:28.687 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:29:29.139 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0
15:29:29.194 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
15:29:29.211 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
15:29:29.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:29:29.225 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:29:29.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:29:29.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:29:29.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:29.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001a5833cc2b8
15:29:29.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001a5833cc4d8
15:29:29.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:29.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:29.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:30.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752132569832_127.0.0.1_5856
15:29:30.020 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Notify connected event to listeners.
15:29:30.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:30.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e63c8bc4-7c51-4ab1-bf1b-9841f4077542_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001a583506350
15:29:30.100 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:29:32.585 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:29:33.442 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:29:33.832 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0
15:29:33.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:33.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001a5833cc2b8
15:29:33.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001a5833cc4d8
15:29:33.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:33.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:33.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:33.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752132573842_127.0.0.1_5862
15:29:33.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:33.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001a583506350
15:29:33.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3caf332-08af-489c-b800-4d6f3f7f9d6f_config-0] Notify connected event to listeners.
15:29:34.036 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 56c2dc5d-7348-4044-967a-dbe285747bf1
15:29:34.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] RpcClient init label, labels = {module=naming, source=sdk}
15:29:34.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:29:34.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:29:34.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:29:34.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:34.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Success to connect to server [localhost:8848] on start up, connectionId = 1752132574051_127.0.0.1_5863
15:29:34.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:34.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001a583506350
15:29:34.169 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Notify connected event to listeners.
15:29:34.559 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
15:29:34.597 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 6.48 seconds (JVM running for 7.566)
15:29:34.603 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:29:34.604 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:29:34.604 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:29:34.715 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Receive server push request, request = NotifySubscriberRequest, requestId = 47
15:29:34.715 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Ack server push request, request = NotifySubscriberRequest, requestId = 47
15:30:05.252 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Receive server push request, request = NotifySubscriberRequest, requestId = 49
15:30:05.357 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c2dc5d-7348-4044-967a-dbe285747bf1] Ack server push request, request = NotifySubscriberRequest, requestId = 49
15:53:12.570 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:53:12.570 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:53:12.896 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:53:12.896 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c9c6b07[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:53:12.896 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752132574051_127.0.0.1_5863
15:53:12.901 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6d329a76[Running, pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 407]
15:53:12.901 [nacos-grpc-client-executor-407] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752132574051_127.0.0.1_5863]Ignore complete event,isRunning:false,isAbandon=false
15:54:53.712 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:54:54.359 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0fecfeec-a83f-48b6-84b5-1beb45353627_config-0
15:54:54.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
15:54:54.471 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:54:54.480 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:54:54.490 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:54:54.506 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
15:54:54.523 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
15:54:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:54:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000027a813b94f0
15:54:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000027a813b9710
15:54:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:54:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:54:54.537 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:54:55.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752134095353_127.0.0.1_7927
15:54:55.560 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Notify connected event to listeners.
15:54:55.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:54:55.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fecfeec-a83f-48b6-84b5-1beb45353627_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000027a814f0fb0
15:54:55.689 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:54:59.306 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:55:00.445 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:55:00.915 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0
15:55:00.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:55:00.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000027a813b94f0
15:55:00.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000027a813b9710
15:55:00.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:55:00.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:55:00.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:55:01.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752134100935_127.0.0.1_7929
15:55:01.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:55:01.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000027a814f0fb0
15:55:01.064 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ffaec24-094a-4a0b-99d2-09791e9567bd_config-0] Notify connected event to listeners.
15:55:01.205 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b62a0ad-ca73-4874-8e28-832d9e84748e
15:55:01.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] RpcClient init label, labels = {module=naming, source=sdk}
15:55:01.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:55:01.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:55:01.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:55:01.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:55:01.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Success to connect to server [localhost:8848] on start up, connectionId = 1752134101221_127.0.0.1_7930
15:55:01.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Notify connected event to listeners.
15:55:01.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:55:01.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000027a814f0fb0
15:55:01.756 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
15:55:01.804 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.776 seconds (JVM running for 18.709)
15:55:01.811 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:55:01.812 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:55:01.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:55:01.910 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 50
15:55:01.910 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 50
15:55:01.998 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 51
15:55:01.998 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 51
15:56:36.060 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 52
15:56:36.060 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 52
15:57:00.889 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 54
15:57:00.903 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 54
16:21:32.547 [nacos-grpc-client-executor-473] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 57
16:21:32.547 [nacos-grpc-client-executor-473] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 57
16:21:32.547 [nacos-grpc-client-executor-474] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 58
16:21:32.547 [nacos-grpc-client-executor-474] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 58
16:40:02.124 [nacos-grpc-client-executor-824] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 59
16:40:02.137 [nacos-grpc-client-executor-824] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 59
16:44:00.325 [nacos-grpc-client-executor-897] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 60
16:44:00.343 [nacos-grpc-client-executor-897] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 60
16:44:25.802 [nacos-grpc-client-executor-906] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 62
16:44:25.825 [nacos-grpc-client-executor-906] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 62
16:54:57.484 [nacos-grpc-client-executor-1091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 63
16:54:57.507 [nacos-grpc-client-executor-1091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 63
16:55:21.430 [nacos-grpc-client-executor-1099] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 64
16:55:21.447 [nacos-grpc-client-executor-1099] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 64
17:19:32.887 [nacos-grpc-client-executor-1548] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 66
17:19:32.911 [nacos-grpc-client-executor-1548] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 66
17:20:29.965 [nacos-grpc-client-executor-1568] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 68
17:20:29.991 [nacos-grpc-client-executor-1568] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 68
17:23:57.520 [nacos-grpc-client-executor-1633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 69
17:23:57.538 [nacos-grpc-client-executor-1633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 69
17:24:38.153 [nacos-grpc-client-executor-1648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 71
17:24:38.171 [nacos-grpc-client-executor-1648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 71
17:59:50.282 [nacos-grpc-client-executor-2256] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 72
17:59:50.297 [nacos-grpc-client-executor-2256] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 72
18:00:31.770 [nacos-grpc-client-executor-2267] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 74
18:00:31.787 [nacos-grpc-client-executor-2267] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 74
18:21:01.220 [nacos-grpc-client-executor-2620] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 76
18:21:01.255 [nacos-grpc-client-executor-2620] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 76
18:21:34.863 [nacos-grpc-client-executor-2635] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Receive server push request, request = NotifySubscriberRequest, requestId = 78
18:21:34.863 [nacos-grpc-client-executor-2635] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b62a0ad-ca73-4874-8e28-832d9e84748e] Ack server push request, request = NotifySubscriberRequest, requestId = 78
20:41:30.925 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:30.930 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:31.266 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:31.267 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@14aad172[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:31.267 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752134101221_127.0.0.1_7930
20:41:31.269 [nacos-grpc-client-executor-5229] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752134101221_127.0.0.1_7930]Ignore complete event,isRunning:false,isAbandon=false
20:41:31.278 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23fb215e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5230]
