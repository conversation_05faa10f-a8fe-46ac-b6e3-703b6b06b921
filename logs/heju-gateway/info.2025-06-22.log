10:18:09.138 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:17.182 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:18.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a5240046-1479-4fb2-b770-091431aa50c0_config-0
10:18:18.275 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 91 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:18.322 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:18.343 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:18.355 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:18.369 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:18.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:18.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:18.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001c39e3bb8c8
10:18:18.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001c39e3bbae8
10:18:18.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:18.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:18.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:18:20.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558699690_127.0.0.1_5464
10:18:20.039 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Notify connected event to listeners.
10:18:20.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:20.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5240046-1479-4fb2-b770-091431aa50c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001c39e4f5418
10:18:20.328 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:26.010 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:18:27.892 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:18:28.739 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f9df97b-2901-403d-915d-7b08afeddfa0_config-0
10:18:28.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:28.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001c39e3bb8c8
10:18:28.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001c39e3bbae8
10:18:28.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:28.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:28.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:18:28.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558708755_127.0.0.1_5478
10:18:28.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:28.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001c39e4f5418
10:18:28.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f9df97b-2901-403d-915d-7b08afeddfa0_config-0] Notify connected event to listeners.
10:18:29.093 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8cdedb9c-fcf8-444d-b576-86d5649b3fd1
10:18:29.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] RpcClient init label, labels = {module=naming, source=sdk}
10:18:29.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:18:29.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:18:29.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:18:29.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:18:29.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558709117_127.0.0.1_5479
10:18:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001c39e4f5418
10:18:29.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Notify connected event to listeners.
10:18:29.934 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
10:18:29.989 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 13.781 seconds (JVM running for 15.815)
10:18:30.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:18:30.002 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:18:30.004 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:18:30.257 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 1
10:18:30.258 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 1
10:18:30.535 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:18:30.536 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:19:00.122 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:19:00.123 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 4
10:19:30.127 [nacos-grpc-client-executor-58] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:19:30.128 [nacos-grpc-client-executor-58] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:19:30.139 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 10
10:19:30.140 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:19:30.151 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:19:30.152 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 9
13:47:39.339 [nacos-grpc-client-executor-4181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 13
13:47:39.370 [nacos-grpc-client-executor-4181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 13
13:47:59.187 [nacos-grpc-client-executor-4185] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 14
13:47:59.221 [nacos-grpc-client-executor-4185] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 14
13:53:35.155 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
13:53:35.183 [lettuce-nioEventLoop-5-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
14:18:40.213 [nacos-grpc-client-executor-4795] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 17
14:18:40.262 [nacos-grpc-client-executor-4795] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 17
14:19:03.744 [nacos-grpc-client-executor-4800] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 18
14:19:03.879 [nacos-grpc-client-executor-4800] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 18
14:23:09.734 [nacos-grpc-client-executor-4881] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 20
14:23:09.753 [nacos-grpc-client-executor-4881] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 20
14:23:25.375 [nacos-grpc-client-executor-4884] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 21
14:23:25.402 [nacos-grpc-client-executor-4884] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cdedb9c-fcf8-444d-b576-86d5649b3fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 21
14:31:00.519 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:31:00.522 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:31:00.885 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:31:00.885 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29e82881[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:31:00.885 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750558709117_127.0.0.1_5479
14:31:00.891 [nacos-grpc-client-executor-5036] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750558709117_127.0.0.1_5479]Ignore complete event,isRunning:false,isAbandon=false
14:31:00.892 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@409d46c9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5037]
14:32:34.256 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:32:34.932 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0
14:32:35.019 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
14:32:35.049 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:32:35.061 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:32:35.073 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:32:35.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:32:35.100 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:32:35.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:32:35.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000257d83b9220
14:32:35.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000257d83b9440
14:32:35.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:32:35.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:32:35.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:32:36.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573956385_127.0.0.1_7592
14:32:36.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Notify connected event to listeners.
14:32:36.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:36.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000257d84f0fb0
14:32:36.897 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:00.470 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:33:07.714 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:33:12.850 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0
14:33:12.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:12.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000257d83b9220
14:33:12.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000257d83b9440
14:33:12.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:12.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:12.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:33:13.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573992867_127.0.0.1_7658
14:33:13.011 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Notify connected event to listeners.
14:33:13.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:13.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000257d84f0fb0
14:33:13.557 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bdaa3483-e7f4-477b-ad44-aa398fbaf0ef
14:33:13.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] RpcClient init label, labels = {module=naming, source=sdk}
14:33:13.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:33:13.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:33:13.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:33:13.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:33:13.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573993582_127.0.0.1_7661
14:33:13.794 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Notify connected event to listeners.
14:33:13.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:13.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000257d84f0fb0
14:33:14.432 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 24
14:33:14.432 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 24
14:33:14.524 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 25
14:33:14.525 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:33:14.535 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 26
14:33:14.536 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 26
14:33:14.549 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 27
14:33:14.550 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 27
14:33:14.564 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 28
14:33:14.565 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:33:14.963 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:33:15.059 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 41.302 seconds (JVM running for 43.865)
14:33:15.075 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:33:15.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:33:15.078 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:33:15.472 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 29
14:33:15.473 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 29
14:33:26.987 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:33:27.073 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Ack server push request, request = NotifySubscriberRequest, requestId = 30
17:55:55.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.515 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.735 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.735 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.737 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.605 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.605 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.607 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.320 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.137 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.222 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.226 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.459 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.459 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.732 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:56:04.066 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:56:04.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:56:04.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@18e3e062[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:56:04.401 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdaa3483-e7f4-477b-ad44-aa398fbaf0ef] Client is shutdown, stop reconnect to server
17:56:04.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750573993582_127.0.0.1_7661
17:56:04.402 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4c4e362c[Running, pool size = 28, active threads = 0, queued tasks = 0, completed tasks = 3834]
17:56:04.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:04.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:06.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63775f45-b3f8-44e5-b91e-ed1558b2b4c4_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:06.219 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ac6985e-602d-4292-b943-14c0d8d12bc6_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
