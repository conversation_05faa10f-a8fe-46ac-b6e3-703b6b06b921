09:12:26.162 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:26.816 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0
09:12:26.883 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 39 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:26.910 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:26.921 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:26.931 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:26.945 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:26.958 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:26.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:26.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000018fcd3b42b8
09:12:26.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000018fcd3b44d8
09:12:26.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:26.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:26.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:28.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752541947835_127.0.0.1_5676
09:12:28.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Notify connected event to listeners.
09:12:28.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:28.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d9067b0-27e2-4782-8f8e-88cb7cd41727_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018fcd4ee350
09:12:28.323 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:37.469 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:12:39.690 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:12:40.305 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0
09:12:40.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:40.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000018fcd3b42b8
09:12:40.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000018fcd3b44d8
09:12:40.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:40.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:40.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:40.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752541960318_127.0.0.1_5744
09:12:40.433 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Notify connected event to listeners.
09:12:40.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:40.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [918efd3e-0f7c-436f-84cd-7fbd77e2d865_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018fcd4ee350
09:12:40.597 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2
09:12:40.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] RpcClient init label, labels = {module=naming, source=sdk}
09:12:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:40.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:40.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:40.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:40.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Success to connect to server [localhost:8848] on start up, connectionId = 1752541960620_127.0.0.1_5755
09:12:40.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Notify connected event to listeners.
09:12:40.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:40.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018fcd4ee350
09:12:41.202 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
09:12:41.249 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 15.727 seconds (JVM running for 20.801)
09:12:41.259 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:12:41.261 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:12:41.261 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:12:41.345 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:12:41.345 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:12:41.441 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:12:41.442 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:12:41.676 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:12:41.680 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:13:11.479 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:13:11.480 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:13:11.490 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:13:11.490 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad1d80d2-1a3a-42ab-aecb-fcd2cad863b2] Ack server push request, request = NotifySubscriberRequest, requestId = 8
11:24:20.442 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:24:20.464 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:24:20.790 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:24:20.790 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c0db8c6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:24:20.793 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752541960620_127.0.0.1_5755
11:24:20.797 [nacos-grpc-client-executor-2642] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752541960620_127.0.0.1_5755]Ignore complete event,isRunning:false,isAbandon=false
11:24:20.816 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@30d0cd2[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 2643]
11:24:36.164 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:24:36.869 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 626a69e3-65ee-4825-b4be-97939775a006_config-0
11:24:36.951 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
11:24:36.982 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:24:36.991 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:24:37.003 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:24:37.017 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:24:37.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
11:24:37.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:24:37.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000177a23cc2b8
11:24:37.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000177a23cc4d8
11:24:37.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:24:37.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:24:37.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:38.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752549877889_127.0.0.1_9477
11:24:38.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Notify connected event to listeners.
11:24:38.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:38.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626a69e3-65ee-4825-b4be-97939775a006_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000177a2506848
11:24:38.302 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:24:42.923 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:24:44.216 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:24:44.827 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0
11:24:44.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:24:44.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000177a23cc2b8
11:24:44.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000177a23cc4d8
11:24:44.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:24:44.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:24:44.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:44.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752549884836_127.0.0.1_9523
11:24:44.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:44.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000177a2506848
11:24:44.947 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc130478-c58e-436e-86c6-4ceeec9d98ef_config-0] Notify connected event to listeners.
11:24:45.052 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b4d7b363-136d-4e9c-8147-61fa8d9a9156
11:24:45.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] RpcClient init label, labels = {module=naming, source=sdk}
11:24:45.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:24:45.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:24:45.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:24:45.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:45.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Success to connect to server [localhost:8848] on start up, connectionId = 1752549885066_127.0.0.1_9524
11:24:45.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:45.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Notify connected event to listeners.
11:24:45.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000177a2506848
11:24:45.734 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
11:24:45.774 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.334 seconds (JVM running for 11.526)
11:24:45.777 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 17
11:24:45.785 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
11:24:45.787 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
11:24:45.788 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
11:24:45.821 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 17
11:24:45.871 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:24:45.872 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:24:45.880 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 21
11:24:45.880 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 21
11:24:45.893 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:24:45.893 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 20
11:24:45.905 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:24:45.907 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 19
11:25:18.276 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 23
11:25:18.309 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 23
14:03:15.180 [nacos-grpc-client-executor-3152] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 27
14:03:15.203 [nacos-grpc-client-executor-3152] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 27
14:03:52.693 [nacos-grpc-client-executor-3167] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:03:52.710 [nacos-grpc-client-executor-3167] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:29:28.792 [nacos-grpc-client-executor-3659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 32
14:29:28.812 [nacos-grpc-client-executor-3659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 32
14:29:52.616 [nacos-grpc-client-executor-3670] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:29:52.639 [nacos-grpc-client-executor-3670] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:43:53.229 [nacos-grpc-client-executor-3938] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:43:53.256 [nacos-grpc-client-executor-3938] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:44:26.198 [nacos-grpc-client-executor-3945] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:44:26.219 [nacos-grpc-client-executor-3945] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:47:04.031 [nacos-grpc-client-executor-3999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:47:04.062 [nacos-grpc-client-executor-3999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 42
14:47:29.767 [nacos-grpc-client-executor-4005] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 45
14:47:29.786 [nacos-grpc-client-executor-4005] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 45
14:51:53.793 [nacos-grpc-client-executor-4097] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 47
14:51:53.816 [nacos-grpc-client-executor-4097] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 47
14:52:28.375 [nacos-grpc-client-executor-4105] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:52:28.400 [nacos-grpc-client-executor-4105] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:53:08.266 [nacos-grpc-client-executor-4118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 52
14:53:08.293 [nacos-grpc-client-executor-4118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 52
14:53:33.707 [nacos-grpc-client-executor-4125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 55
14:53:33.728 [nacos-grpc-client-executor-4125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 55
15:14:43.224 [nacos-grpc-client-executor-4537] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 57
15:14:43.244 [nacos-grpc-client-executor-4537] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 57
15:15:07.195 [nacos-grpc-client-executor-4548] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 60
15:15:07.220 [nacos-grpc-client-executor-4548] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 60
15:16:41.299 [nacos-grpc-client-executor-4574] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 62
15:16:41.322 [nacos-grpc-client-executor-4574] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 62
15:17:04.594 [nacos-grpc-client-executor-4585] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Receive server push request, request = NotifySubscriberRequest, requestId = 65
15:17:04.616 [nacos-grpc-client-executor-4585] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d7b363-136d-4e9c-8147-61fa8d9a9156] Ack server push request, request = NotifySubscriberRequest, requestId = 65
15:25:36.221 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:25:36.224 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:25:36.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:25:36.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@128c966[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:25:36.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752549885066_127.0.0.1_9524
15:25:36.542 [nacos-grpc-client-executor-4746] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752549885066_127.0.0.1_9524]Ignore complete event,isRunning:false,isAbandon=false
15:25:36.545 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@676203b1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4747]
15:29:28.998 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:29:30.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0
15:29:30.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 71 ms to scan 1 urls, producing 3 keys and 6 values 
15:29:30.529 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
15:29:30.547 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
15:29:30.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
15:29:30.598 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
15:29:30.624 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
15:29:30.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:30.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001dd01400d60
15:29:30.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001dd01400f80
15:29:30.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:30.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:30.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:33.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752564573089_127.0.0.1_8347
15:29:33.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Notify connected event to listeners.
15:29:33.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:33.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e13a9e4-685d-45eb-93a7-4f7bc75eae07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001dd01538ad8
15:29:33.691 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:29:43.145 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:29:45.960 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:29:47.142 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0
15:29:47.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:47.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001dd01400d60
15:29:47.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001dd01400f80
15:29:47.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:47.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:47.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:47.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752564587186_127.0.0.1_8457
15:29:47.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:47.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Notify connected event to listeners.
15:29:47.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ac1b457-7a92-4281-b41f-47c6ddb94db3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001dd01538ad8
15:29:47.504 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f270be7b-68e0-40cd-99db-aeda49542ede
15:29:47.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] RpcClient init label, labels = {module=naming, source=sdk}
15:29:47.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:29:47.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:29:47.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:29:47.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:47.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Success to connect to server [localhost:8848] on start up, connectionId = 1752564587524_127.0.0.1_8460
15:29:47.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Notify connected event to listeners.
15:29:47.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:47.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001dd01538ad8
15:29:48.287 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Receive server push request, request = NotifySubscriberRequest, requestId = 67
15:29:48.288 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Ack server push request, request = NotifySubscriberRequest, requestId = 67
15:29:48.374 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Receive server push request, request = NotifySubscriberRequest, requestId = 68
15:29:48.375 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Ack server push request, request = NotifySubscriberRequest, requestId = 68
15:29:48.642 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
15:29:48.698 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 21.205 seconds (JVM running for 23.616)
15:29:48.710 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:29:48.711 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:29:48.712 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:29:49.148 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Receive server push request, request = NotifySubscriberRequest, requestId = 69
15:29:49.148 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Ack server push request, request = NotifySubscriberRequest, requestId = 69
15:30:18.541 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Receive server push request, request = NotifySubscriberRequest, requestId = 73
15:30:18.543 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Ack server push request, request = NotifySubscriberRequest, requestId = 73
15:30:18.556 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Receive server push request, request = NotifySubscriberRequest, requestId = 75
15:30:18.556 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Ack server push request, request = NotifySubscriberRequest, requestId = 75
15:30:18.567 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Receive server push request, request = NotifySubscriberRequest, requestId = 74
15:30:18.567 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f270be7b-68e0-40cd-99db-aeda49542ede] Ack server push request, request = NotifySubscriberRequest, requestId = 74
19:18:04.538 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:18:04.545 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:18:04.901 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:18:04.905 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3849c1c0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:18:04.905 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752564587524_127.0.0.1_8460
19:18:04.912 [nacos-grpc-client-executor-4532] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752564587524_127.0.0.1_8460]Ignore complete event,isRunning:false,isAbandon=false
19:18:04.927 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f77bda9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4533]
