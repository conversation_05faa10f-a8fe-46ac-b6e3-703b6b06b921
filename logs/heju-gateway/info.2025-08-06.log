09:27:10.313 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:27:11.068 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0
09:27:11.146 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:27:11.189 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:27:11.204 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:27:11.211 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:27:11.224 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:27:11.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:27:11.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:27:11.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000016cdf3b94f0
09:27:11.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000016cdf3b9710
09:27:11.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:27:11.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:27:11.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:13.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754443632575_127.0.0.1_2898
09:27:13.526 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Notify connected event to listeners.
09:27:13.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:13.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000016cdf4f1450
09:27:16.016 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:27:23.587 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:27:25.454 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:27:26.333 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 264b20ea-be19-4d48-b106-53f0ab0719ff_config-0
09:27:26.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:27:26.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000016cdf3b94f0
09:27:26.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000016cdf3b9710
09:27:26.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:27:26.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:27:26.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:26.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754443646349_127.0.0.1_3104
09:27:26.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:26.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000016cdf4f1450
09:27:26.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Notify connected event to listeners.
09:27:26.697 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad
09:27:26.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] RpcClient init label, labels = {module=naming, source=sdk}
09:27:26.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:27:26.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:27:26.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:27:26.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:26.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Success to connect to server [localhost:8848] on start up, connectionId = 1754443646729_127.0.0.1_3114
09:27:26.940 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Notify connected event to listeners.
09:27:26.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:26.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000016cdf4f1450
09:27:27.527 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:27:27.530 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:27:27.748 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:27:27.749 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:27:27.838 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.59:8081 register finished
09:27:27.893 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 18.338 seconds (JVM running for 25.928)
09:27:27.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:27:27.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:27:27.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:27:28.501 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:27:28.503 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:27:57.950 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:27:57.952 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:27:57.962 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:27:57.963 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:27:57.974 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:27:57.974 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:18:09.800 [nacos-grpc-client-executor-1045] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:18:09.822 [nacos-grpc-client-executor-1045] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:18:22.482 [nacos-grpc-client-executor-1047] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:18:22.504 [nacos-grpc-client-executor-1047] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:25:03.228 [nacos-grpc-client-executor-1181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:25:03.262 [nacos-grpc-client-executor-1181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:25:30.635 [nacos-grpc-client-executor-1191] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:25:30.655 [nacos-grpc-client-executor-1191] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 23
11:20:56.512 [nacos-grpc-client-executor-2287] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 27
11:20:56.531 [nacos-grpc-client-executor-2287] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 27
11:21:27.419 [nacos-grpc-client-executor-2297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 30
11:21:27.436 [nacos-grpc-client-executor-2297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 30
13:17:10.223 [lettuce-nioEventLoop-5-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
13:17:10.357 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
13:17:19.442 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:17:27.745 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:17:27.776 [lettuce-nioEventLoop-5-15] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
14:25:35.374 [nacos-grpc-client-executor-5940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:25:35.396 [nacos-grpc-client-executor-5940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:25:38.201 [nacos-grpc-client-executor-5941] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:25:38.215 [nacos-grpc-client-executor-5941] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:27:09.944 [nacos-grpc-client-executor-5970] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:27:09.965 [nacos-grpc-client-executor-5970] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 42
14:27:37.878 [nacos-grpc-client-executor-5980] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 45
14:27:37.903 [nacos-grpc-client-executor-5980] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 45
14:55:05.041 [nacos-grpc-client-executor-6503] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:55:05.051 [nacos-grpc-client-executor-6503] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:55:24.583 [nacos-grpc-client-executor-6507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 55
14:55:24.596 [nacos-grpc-client-executor-6507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 55
16:14:05.024 [nacos-grpc-client-executor-7949] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 59
16:14:05.043 [nacos-grpc-client-executor-7949] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 59
16:14:07.958 [nacos-grpc-client-executor-7952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Receive server push request, request = NotifySubscriberRequest, requestId = 63
16:14:07.975 [nacos-grpc-client-executor-7952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Ack server push request, request = NotifySubscriberRequest, requestId = 63
20:49:40.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.386 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.396 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.932 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.865 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.869 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.754 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:49:43.077 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:49:43.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:49:43.412 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d0ab67e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:49:43.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1c9d9ff-c6a6-4379-b8ee-fde4b07532ad] Client is shutdown, stop reconnect to server
20:49:43.412 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754443646729_127.0.0.1_3114
20:49:43.413 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@38424e5b[Running, pool size = 21, active threads = 0, queued tasks = 0, completed tasks = 12954]
20:49:44.018 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:44.019 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:44.933 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [264b20ea-be19-4d48-b106-53f0ab0719ff_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:44.948 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcbb4dde-a38e-44d3-adf1-a08b5fc30be0_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
