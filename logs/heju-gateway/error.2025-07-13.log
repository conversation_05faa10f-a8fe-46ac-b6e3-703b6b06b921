10:05:24.664 [reactor-http-nio-2] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/,异常信息:404 NOT_FOUND
10:05:25.359 [reactor-http-nio-2] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND
10:51:47.204 [reactor-http-nio-8] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/customerinfo/1
15:48:31.609 [boundedElastic-266] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/kkfileview/onlinePreview,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for kkfileview"
15:48:34.413 [boundedElastic-266] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/kkfileview/onlinePreview,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for kkfileview"
16:32:01.233 [reactor-http-nio-20] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
16:32:01.233 [reactor-http-nio-19] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
19:22:09.155 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=fd5e01497a941261ab9f1ef34bfa3c31, Client-RequestTS=1752405728766, exConfigInfo=true, notify=false, Timestamp=1752405728769}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
19:22:09.262 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=fd5e01497a941261ab9f1ef34bfa3c31, Client-RequestTS=1752405728766, exConfigInfo=true, notify=false, Timestamp=1752405728769}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
19:22:09.370 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=fd5e01497a941261ab9f1ef34bfa3c31, Client-RequestTS=1752405728766, exConfigInfo=true, notify=false, Timestamp=1752405728769}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
19:22:09.509 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=92e40cde9960c185713484c4e3cacf39, Client-RequestTS=1752405729399, exConfigInfo=true, notify=false, Timestamp=1752405729399}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
19:22:09.617 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=92e40cde9960c185713484c4e3cacf39, Client-RequestTS=1752405729399, exConfigInfo=true, notify=false, Timestamp=1752405729399}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
19:22:09.725 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=92e40cde9960c185713484c4e3cacf39, Client-RequestTS=1752405729399, exConfigInfo=true, notify=false, Timestamp=1752405729399}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
19:22:09.850 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=ddb4d9d79a6240aff5eaf1101e28c0e9, Client-RequestTS=1752405729738, exConfigInfo=true, notify=false, Timestamp=1752405729738}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
19:22:09.960 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=ddb4d9d79a6240aff5eaf1101e28c0e9, Client-RequestTS=1752405729738, exConfigInfo=true, notify=false, Timestamp=1752405729738}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
19:22:10.069 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=ddb4d9d79a6240aff5eaf1101e28c0e9, Client-RequestTS=1752405729738, exConfigInfo=true, notify=false, Timestamp=1752405729738}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
19:22:10.176 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=57968b4ac3b163b30b93e27005d2025b, Client-RequestTS=1752405730072, exConfigInfo=true, notify=false, Timestamp=1752405730072}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
19:22:10.283 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=57968b4ac3b163b30b93e27005d2025b, Client-RequestTS=1752405730072, exConfigInfo=true, notify=false, Timestamp=1752405730072}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
19:22:10.394 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=57968b4ac3b163b30b93e27005d2025b, Client-RequestTS=1752405730072, exConfigInfo=true, notify=false, Timestamp=1752405730072}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
19:22:10.512 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=3025d91593385bfb3aa74827db2b1600, Client-RequestTS=1752405730397, exConfigInfo=true, notify=false, Timestamp=1752405730397}, requestId='null'}, retryTimes = 0, errorMessage = Client not connected, current status:STARTING
19:22:10.618 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=3025d91593385bfb3aa74827db2b1600, Client-RequestTS=1752405730397, exConfigInfo=true, notify=false, Timestamp=1752405730397}, requestId='null'}, retryTimes = 1, errorMessage = Client not connected, current status:STARTING
19:22:10.727 [main] ERROR c.a.n.c.r.client - [printIfErrorEnabled,99] - Send request fail, request = ConfigQueryRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=3025d91593385bfb3aa74827db2b1600, Client-RequestTS=1752405730397, exConfigInfo=true, notify=false, Timestamp=1752405730397}, requestId='null'}, retryTimes = 2, errorMessage = Client not connected, current status:STARTING
