09:01:31.310 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:01:32.475 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93423879-7ffe-4ca9-a251-d055137a9f25_config-0
09:01:32.638 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 74 ms to scan 1 urls, producing 3 keys and 6 values 
09:01:32.708 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 35 ms to scan 1 urls, producing 4 keys and 9 values 
09:01:32.730 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
09:01:32.744 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:01:32.766 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 7 values 
09:01:32.798 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
09:01:32.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:01:32.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000019e813b8d60
09:01:32.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000019e813b8f80
09:01:32.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:01:32.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:01:32.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:01:35.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753837294832_127.0.0.1_9877
09:01:35.272 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Notify connected event to listeners.
09:01:35.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:01:35.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93423879-7ffe-4ca9-a251-d055137a9f25_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000019e814f0668
09:01:35.708 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:01:45.702 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:01:49.178 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:01:50.125 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0
09:01:50.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:01:50.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000019e813b8d60
09:01:50.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000019e813b8f80
09:01:50.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:01:50.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:01:50.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:01:50.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753837310143_127.0.0.1_9908
09:01:50.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:01:50.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000019e814f0668
09:01:50.282 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12b860f7-badd-47e1-ab7c-6d357f2c61b7_config-0] Notify connected event to listeners.
09:01:50.488 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f15e6446-28a6-4bad-b24e-aab4de621e32
09:01:50.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] RpcClient init label, labels = {module=naming, source=sdk}
09:01:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:01:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:01:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:01:50.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:01:50.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Success to connect to server [localhost:8848] on start up, connectionId = 1753837310514_127.0.0.1_9909
09:01:50.855 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Notify connected event to listeners.
09:01:50.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:01:50.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000019e814f0668
09:01:51.411 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:01:51.412 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:01:51.710 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:01:51.713 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:01:51.986 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.43:8081 register finished
09:01:52.063 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 21.577 seconds (JVM running for 31.633)
09:01:52.081 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:01:52.084 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:01:52.087 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:01:52.274 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:01:52.284 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:01:52.588 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:01:52.589 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:02:21.960 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:02:21.961 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:02:51.895 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:02:51.896 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:06:59.880 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:06:59.892 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:07:16.564 [nacos-grpc-client-executor-148] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:07:16.578 [nacos-grpc-client-executor-148] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f15e6446-28a6-4bad-b24e-aab4de621e32] Ack server push request, request = NotifySubscriberRequest, requestId = 20
19:20:20.282 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:20.395 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:20.745 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:20.746 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a3ae70c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:20.747 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753837310514_127.0.0.1_9909
19:20:20.756 [nacos-grpc-client-executor-11903] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753837310514_127.0.0.1_9909]Ignore complete event,isRunning:false,isAbandon=false
19:20:20.788 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5526113d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 11904]
19:23:43.337 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:23:44.979 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0
19:23:45.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 92 ms to scan 1 urls, producing 3 keys and 6 values 
19:23:45.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
19:23:45.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
19:23:45.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
19:23:45.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
19:23:45.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 2 keys and 8 values 
19:23:45.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:23:45.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000002652f3b8fc8
19:23:45.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000002652f3b91e8
19:23:45.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:23:45.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:23:45.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:23:48.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753874627794_127.0.0.1_5709
19:23:48.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Notify connected event to listeners.
19:23:48.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:23:48.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dec51f15-d03a-4fe6-9859-9ca2d828a383_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000002652f4f0fb0
19:23:48.506 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:23:56.375 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
19:24:00.545 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
19:24:02.936 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of db413bf4-5573-4c22-9970-f028c9632269_config-0
19:24:02.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:24:02.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000002652f3b8fc8
19:24:02.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000002652f3b91e8
19:24:02.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:24:02.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:24:02.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:24:03.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753874642962_127.0.0.1_5930
19:24:03.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:24:03.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000002652f4f0fb0
19:24:03.086 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db413bf4-5573-4c22-9970-f028c9632269_config-0] Notify connected event to listeners.
19:24:03.378 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 29cd2c50-bc64-4e5d-96dd-d688a6d7bf98
19:24:03.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] RpcClient init label, labels = {module=naming, source=sdk}
19:24:03.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:24:03.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:24:03.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:24:03.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:24:03.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Success to connect to server [localhost:8848] on start up, connectionId = 1753874643406_127.0.0.1_5934
19:24:03.534 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Notify connected event to listeners.
19:24:03.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:24:03.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000002652f4f0fb0
19:24:04.104 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Receive server push request, request = NotifySubscriberRequest, requestId = 27
19:24:04.107 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Ack server push request, request = NotifySubscriberRequest, requestId = 27
19:24:04.297 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Receive server push request, request = NotifySubscriberRequest, requestId = 28
19:24:04.299 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Ack server push request, request = NotifySubscriberRequest, requestId = 28
19:24:04.752 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.43:8081 register finished
19:24:04.844 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 24.192 seconds (JVM running for 49.667)
19:24:04.859 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
19:24:04.860 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
19:24:04.861 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
19:24:05.278 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Receive server push request, request = NotifySubscriberRequest, requestId = 29
19:24:05.280 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Ack server push request, request = NotifySubscriberRequest, requestId = 29
19:24:34.424 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Receive server push request, request = NotifySubscriberRequest, requestId = 31
19:24:34.425 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Ack server push request, request = NotifySubscriberRequest, requestId = 31
19:25:04.423 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Receive server push request, request = NotifySubscriberRequest, requestId = 34
19:25:04.424 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Ack server push request, request = NotifySubscriberRequest, requestId = 34
19:25:04.438 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Receive server push request, request = NotifySubscriberRequest, requestId = 35
19:25:04.438 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29cd2c50-bc64-4e5d-96dd-d688a6d7bf98] Ack server push request, request = NotifySubscriberRequest, requestId = 35
