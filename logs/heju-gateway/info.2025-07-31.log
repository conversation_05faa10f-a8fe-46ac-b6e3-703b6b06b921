09:43:49.419 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:43:50.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0
09:43:50.209 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 59 ms to scan 1 urls, producing 3 keys and 6 values 
09:43:50.251 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:43:50.264 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:43:50.274 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:43:50.289 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:43:50.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:43:50.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:43:50.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d8a83b8638
09:43:50.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d8a83b8858
09:43:50.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:43:50.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:43:50.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:43:52.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753926232105_127.0.0.1_5978
09:43:52.493 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Notify connected event to listeners.
09:43:52.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:43:52.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e3250dd-b9d4-45da-b1d0-654c1c581a06_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d8a84f0228
09:43:52.769 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:11.332 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:44:15.157 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:44:18.453 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d730ff5b-c514-4219-b351-322fe44cb927_config-0
09:44:18.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:18.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d8a83b8638
09:44:18.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d8a83b8858
09:44:18.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:18.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:18.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:18.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753926258591_127.0.0.1_6450
09:44:18.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:18.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d8a84f0228
09:44:19.113 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d730ff5b-c514-4219-b351-322fe44cb927_config-0] Notify connected event to listeners.
09:44:19.622 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b7cd50b6-374b-4b29-8d64-dc4e83c5ca26
09:44:19.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] RpcClient init label, labels = {module=naming, source=sdk}
09:44:19.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:19.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:19.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:19.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:20.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Success to connect to server [localhost:8848] on start up, connectionId = 1753926259668_127.0.0.1_6456
09:44:20.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:20.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d8a84f0228
09:44:20.383 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Notify connected event to listeners.
09:44:20.992 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:44:20.993 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:44:21.285 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:44:21.291 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:44:21.382 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:44:21.382 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:44:22.115 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.43:8081 register finished
09:44:22.194 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 33.502 seconds (JVM running for 39.631)
09:44:22.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:44:22.211 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:44:22.211 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:44:22.673 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:44:22.677 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:44:48.122 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:44:48.123 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:44:48.133 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:44:48.134 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 10
11:35:32.649 [nacos-grpc-client-executor-2166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 14
11:35:32.667 [nacos-grpc-client-executor-2166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 14
11:35:55.946 [nacos-grpc-client-executor-2174] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:35:55.960 [nacos-grpc-client-executor-2174] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:36:05.666 [nacos-grpc-client-executor-2176] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:36:05.680 [nacos-grpc-client-executor-2176] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:36:43.045 [nacos-grpc-client-executor-2186] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:36:43.066 [nacos-grpc-client-executor-2186] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 20
14:14:04.135 [nacos-grpc-client-executor-5169] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 26
14:14:04.153 [nacos-grpc-client-executor-5169] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 26
14:14:24.978 [nacos-grpc-client-executor-5174] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 29
14:14:24.993 [nacos-grpc-client-executor-5174] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 29
14:36:10.446 [nacos-grpc-client-executor-5588] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:36:10.464 [nacos-grpc-client-executor-5588] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:36:26.828 [nacos-grpc-client-executor-5597] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:36:26.844 [nacos-grpc-client-executor-5597] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b7cd50b6-374b-4b29-8d64-dc4e83c5ca26] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:59:26.594 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:59:26.598 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:59:26.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:59:26.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@33684782[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:59:26.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753926259668_127.0.0.1_6456
14:59:26.933 [nacos-grpc-client-executor-6023] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753926259668_127.0.0.1_6456]Ignore complete event,isRunning:false,isAbandon=false
14:59:26.942 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@50b54ba7[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 6024]
15:07:04.458 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:05.013 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0
15:07:05.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
15:07:05.119 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:07:05.128 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:07:05.136 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:07:05.143 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:07:05.155 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
15:07:05.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:05.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000122bd39c2b8
15:07:05.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000122bd39c4d8
15:07:05.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:05.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:05.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:06.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753945625889_127.0.0.1_9204
15:07:06.099 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Notify connected event to listeners.
15:07:06.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:06.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b813e704-9dd4-41d7-b604-526a4c8e2e7d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000122bd4f6148
15:07:06.213 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:12.072 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:07:14.948 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:07:16.550 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0
15:07:16.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:16.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000122bd39c2b8
15:07:16.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000122bd39c4d8
15:07:16.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:16.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:16.555 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:16.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753945636575_127.0.0.1_9262
15:07:16.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:16.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Notify connected event to listeners.
15:07:16.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c89ddba8-8a80-4fda-a2bf-96dee60125b1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000122bd4f6148
15:07:16.985 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1db16267-0854-4809-aa04-7d6d18cd335c
15:07:16.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] RpcClient init label, labels = {module=naming, source=sdk}
15:07:16.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:16.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:16.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:16.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:17.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Success to connect to server [localhost:8848] on start up, connectionId = 1753945637016_127.0.0.1_9264
15:07:17.139 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Notify connected event to listeners.
15:07:17.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:17.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000122bd4f6148
15:07:17.729 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 38
15:07:17.730 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 38
15:07:18.262 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.43:8081 register finished
15:07:18.345 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 14.421 seconds (JVM running for 15.318)
15:07:18.362 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:07:18.364 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:07:18.367 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:07:18.787 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 39
15:07:18.788 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 39
15:07:35.509 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 42
15:07:35.509 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 42
15:07:35.518 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 43
15:07:35.518 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 43
15:07:47.960 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 45
15:07:47.960 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 45
15:07:49.160 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 47
15:07:49.179 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 47
16:32:34.881 [nacos-grpc-client-executor-1469] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 48
16:32:34.897 [nacos-grpc-client-executor-1469] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 48
16:32:49.535 [nacos-grpc-client-executor-1477] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 50
16:32:49.536 [nacos-grpc-client-executor-1477] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 50
16:33:00.231 [nacos-grpc-client-executor-1481] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 52
16:33:00.253 [nacos-grpc-client-executor-1481] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 52
16:38:44.424 [nacos-grpc-client-executor-1583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 53
16:38:44.440 [nacos-grpc-client-executor-1583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 53
16:38:48.134 [nacos-grpc-client-executor-1584] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:38:48.150 [nacos-grpc-client-executor-1584] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 55
16:50:27.966 [nacos-grpc-client-executor-1796] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 56
16:50:27.980 [nacos-grpc-client-executor-1796] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 56
16:50:56.123 [nacos-grpc-client-executor-1805] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 58
16:50:56.144 [nacos-grpc-client-executor-1805] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 58
16:54:19.075 [nacos-grpc-client-executor-1864] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 59
16:54:19.095 [nacos-grpc-client-executor-1864] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 59
16:54:37.278 [nacos-grpc-client-executor-1872] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 61
16:54:37.290 [nacos-grpc-client-executor-1872] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 61
16:59:57.101 [nacos-grpc-client-executor-1968] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 62
16:59:57.122 [nacos-grpc-client-executor-1968] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 62
17:00:14.461 [nacos-grpc-client-executor-1972] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 63
17:00:14.472 [nacos-grpc-client-executor-1972] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 63
17:19:27.063 [nacos-grpc-client-executor-2313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 65
17:19:27.079 [nacos-grpc-client-executor-2313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 65
17:20:45.894 [nacos-grpc-client-executor-2335] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 67
17:20:45.915 [nacos-grpc-client-executor-2335] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 67
17:41:15.951 [nacos-grpc-client-executor-2725] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 68
17:41:15.967 [nacos-grpc-client-executor-2725] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 68
17:41:32.655 [nacos-grpc-client-executor-2731] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 70
17:41:32.696 [nacos-grpc-client-executor-2731] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 70
20:32:17.587 [nacos-grpc-client-executor-5885] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Receive server push request, request = NotifySubscriberRequest, requestId = 72
20:32:17.587 [nacos-grpc-client-executor-5885] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1db16267-0854-4809-aa04-7d6d18cd335c] Ack server push request, request = NotifySubscriberRequest, requestId = 72
20:46:45.891 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:45.924 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:46.251 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:46.251 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3692aec1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:46.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753945637016_127.0.0.1_9264
20:46:46.253 [nacos-grpc-client-executor-6157] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753945637016_127.0.0.1_9264]Ignore complete event,isRunning:false,isAbandon=false
20:46:46.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@29ff8e5b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6158]
