09:05:50.500 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:51.364 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0951d808-30c7-46e9-ba32-60c862e50219_config-0
09:05:51.486 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 60 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:51.532 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:51.544 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:51.561 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:51.575 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:51.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:51.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:51.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001c70b3b9748
09:05:51.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001c70b3b9968
09:05:51.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:51.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:51.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:53.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664752756_127.0.0.1_11187
09:05:53.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Notify connected event to listeners.
09:05:53.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:53.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0951d808-30c7-46e9-ba32-60c862e50219_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c70b4f0fb0
09:05:53.257 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:04.072 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:06:06.224 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:06:07.683 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a556daa-342c-4c89-99f7-638adf550ee2_config-0
09:06:07.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:07.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001c70b3b9748
09:06:07.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001c70b3b9968
09:06:07.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:07.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:07.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:07.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664767704_127.0.0.1_11278
09:06:07.828 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Notify connected event to listeners.
09:06:07.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:07.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a556daa-342c-4c89-99f7-638adf550ee2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c70b4f0fb0
09:06:08.030 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 41993da1-6b71-49c3-8008-36fa5feb576e
09:06:08.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] RpcClient init label, labels = {module=naming, source=sdk}
09:06:08.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:08.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:08.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:08.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:08.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Success to connect to server [localhost:8848] on start up, connectionId = 1753664768050_127.0.0.1_11279
09:06:08.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:08.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c70b4f0fb0
09:06:08.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Notify connected event to listeners.
09:06:09.156 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:06:09.157 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:06:09.162 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.43:8081 register finished
09:06:09.233 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 19.678 seconds (JVM running for 26.223)
09:06:09.244 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:06:09.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:06:09.249 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:06:09.759 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:06:09.761 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:06:38.971 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:06:38.973 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:06:38.987 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:06:38.988 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:06:38.998 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:06:38.998 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:06:39.010 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:06:39.010 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 7
11:05:01.981 [nacos-grpc-client-executor-2379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 15
11:05:02.003 [nacos-grpc-client-executor-2379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 15
11:05:22.380 [nacos-grpc-client-executor-2385] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 17
11:05:22.399 [nacos-grpc-client-executor-2385] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 17
11:57:32.026 [nacos-grpc-client-executor-3379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:57:32.057 [nacos-grpc-client-executor-3379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 19
11:59:25.709 [nacos-grpc-client-executor-3415] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:59:25.726 [nacos-grpc-client-executor-3415] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 22
15:45:52.364 [nacos-grpc-client-executor-7672] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 24
15:45:52.384 [nacos-grpc-client-executor-7672] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 24
15:46:31.639 [nacos-grpc-client-executor-7681] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 26
15:46:31.655 [nacos-grpc-client-executor-7681] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 26
17:28:46.405 [nacos-grpc-client-executor-9549] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 29
17:28:46.420 [nacos-grpc-client-executor-9549] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 29
17:29:12.223 [nacos-grpc-client-executor-9559] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Receive server push request, request = NotifySubscriberRequest, requestId = 32
17:29:12.247 [nacos-grpc-client-executor-9559] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41993da1-6b71-49c3-8008-36fa5feb576e] Ack server push request, request = NotifySubscriberRequest, requestId = 32
19:28:44.997 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:45.005 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:45.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:45.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5afa93a1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:45.330 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753664768050_127.0.0.1_11279
19:28:45.330 [nacos-grpc-client-executor-11703] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753664768050_127.0.0.1_11279]Ignore complete event,isRunning:false,isAbandon=false
19:28:45.340 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@340cdad1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 11704]
