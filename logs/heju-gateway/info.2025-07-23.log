09:39:05.431 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:39:06.801 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8334f62a-bd2d-4335-9360-9645e9c989cd_config-0
09:39:06.965 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 89 ms to scan 1 urls, producing 3 keys and 6 values 
09:39:07.025 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:39:07.046 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
09:39:07.065 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 5 values 
09:39:07.084 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:39:07.102 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:39:07.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:39:07.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000023e403b8d60
09:39:07.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000023e403b8f80
09:39:07.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:39:07.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:39:07.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:09.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234748880_127.0.0.1_3822
09:39:09.286 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Notify connected event to listeners.
09:39:09.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:09.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8334f62a-bd2d-4335-9360-9645e9c989cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000023e404f0d48
09:39:09.632 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:39:18.266 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:39:20.825 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:39:21.783 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0
09:39:21.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:39:21.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000023e403b8d60
09:39:21.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000023e403b8f80
09:39:21.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:39:21.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:39:21.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:21.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234761799_127.0.0.1_3931
09:39:21.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:21.920 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Notify connected event to listeners.
09:39:21.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69dd7e8d-5717-4615-bba5-eaa1db1a3a3e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000023e404f0d48
09:39:22.107 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c7e8a683-de7b-4b0b-bf41-f938799837c9
09:39:22.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] RpcClient init label, labels = {module=naming, source=sdk}
09:39:22.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:39:22.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:39:22.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:39:22.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:22.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Success to connect to server [localhost:8848] on start up, connectionId = 1753234762124_127.0.0.1_3932
09:39:22.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Notify connected event to listeners.
09:39:22.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:22.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000023e404f0d48
09:39:23.049 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:39:23.050 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7e8a683-de7b-4b0b-bf41-f938799837c9] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:39:23.258 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:39:23.258 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@620260ad[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:39:23.258 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753234762124_127.0.0.1_3932
09:39:23.263 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753234762124_127.0.0.1_3932]Ignore complete event,isRunning:false,isAbandon=false
09:39:23.265 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23f9d0ce[Running, pool size = 13, active threads = 0, queued tasks = 0, completed tasks = 13]
09:40:12.011 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:12.537 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0
09:40:12.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:12.640 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:12.651 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:12.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:12.673 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:12.692 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:12.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:12.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019f813bbda8
09:40:12.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019f813bbfc8
09:40:12.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:12.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:12.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:13.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234813631_127.0.0.1_4571
09:40:13.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Notify connected event to listeners.
09:40:13.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:13.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7213fb9e-5723-4c20-8a6f-74f1953fae6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019f814f5f18
09:40:14.002 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:17.494 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:40:18.807 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:40:19.329 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0
09:40:19.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:19.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019f813bbda8
09:40:19.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019f813bbfc8
09:40:19.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:19.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:19.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:19.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234819340_127.0.0.1_4635
09:40:19.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:19.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019f814f5f18
09:40:19.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6612bf7-ab97-413a-af14-e9cc33f166dc_config-0] Notify connected event to listeners.
09:40:19.634 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46713da6-98d6-4de6-8079-70a9190bec9d
09:40:19.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] RpcClient init label, labels = {module=naming, source=sdk}
09:40:19.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:40:19.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:40:19.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:40:19.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:19.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Success to connect to server [localhost:8848] on start up, connectionId = 1753234819651_127.0.0.1_4638
09:40:19.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Notify connected event to listeners.
09:40:19.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:19.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019f814f5f18
09:40:20.389 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:40:20.390 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:40:20.482 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:40:20.483 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:40:20.492 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:40:20.493 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:40:20.500 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:40:20.501 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46713da6-98d6-4de6-8079-70a9190bec9d] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:40:20.701 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:40:20.703 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d305059[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:40:20.703 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753234819651_127.0.0.1_4638
09:40:20.706 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753234819651_127.0.0.1_4638]Ignore complete event,isRunning:false,isAbandon=false
09:40:20.710 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2e549515[Running, pool size = 25, active threads = 0, queued tasks = 0, completed tasks = 25]
09:41:09.306 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:41:09.866 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0
09:41:09.950 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
09:41:09.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:41:09.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:41:09.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:41:10.009 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:41:10.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:41:10.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:10.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001fe013bc958
09:41:10.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001fe013bcb78
09:41:10.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:10.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:10.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:41:11.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234870966_127.0.0.1_4907
09:41:11.203 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Notify connected event to listeners.
09:41:11.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:11.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [150173b7-b6cd-46c4-aea2-7fb1003aae39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001fe014f69a8
09:41:11.370 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:41:15.358 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:41:16.524 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:41:17.105 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0
09:41:17.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:17.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001fe013bc958
09:41:17.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001fe013bcb78
09:41:17.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:17.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:17.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:41:17.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234877119_127.0.0.1_4938
09:41:17.234 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Notify connected event to listeners.
09:41:17.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:17.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbc36830-6248-4bf7-a3c6-f56062ea3740_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001fe014f69a8
09:41:17.370 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 68800195-ab85-4fe1-a50c-a5cc513c297d
09:41:17.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] RpcClient init label, labels = {module=naming, source=sdk}
09:41:17.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:41:17.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:41:17.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:41:17.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:41:17.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Success to connect to server [localhost:8848] on start up, connectionId = 1753234877386_127.0.0.1_4939
09:41:17.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:17.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001fe014f69a8
09:41:17.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Notify connected event to listeners.
09:41:18.089 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:41:18.091 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:41:18.172 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:41:18.173 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:41:18.181 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:41:18.182 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:41:18.191 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:41:18.192 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68800195-ab85-4fe1-a50c-a5cc513c297d] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:41:18.488 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:41:18.488 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17adbecf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:41:18.489 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753234877386_127.0.0.1_4939
09:41:18.492 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753234877386_127.0.0.1_4939]Ignore complete event,isRunning:false,isAbandon=false
09:41:18.495 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1ef8f7e6[Running, pool size = 24, active threads = 0, queued tasks = 0, completed tasks = 24]
09:47:43.531 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:47:44.110 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0
09:47:44.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
09:47:44.201 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:47:44.211 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:47:44.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:47:44.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:47:44.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:47:44.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:47:44.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001daaa3a42b8
09:47:44.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001daaa3a44d8
09:47:44.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:47:44.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:47:44.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:47:45.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753235265002_127.0.0.1_6797
09:47:45.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Notify connected event to listeners.
09:47:45.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:47:45.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b080857-f935-4ea9-afa1-0ecabe207ca3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001daaa526370
09:47:45.382 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:47:49.131 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:47:50.281 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:47:50.810 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0
09:47:50.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:47:50.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001daaa3a42b8
09:47:50.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001daaa3a44d8
09:47:50.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:47:50.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:47:50.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:47:50.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753235270822_127.0.0.1_6815
09:47:50.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:47:50.934 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Notify connected event to listeners.
09:47:50.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cbd5862-cdf6-4481-ab01-3df63a441bf8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001daaa526370
09:47:51.080 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46d3d0bc-688c-47b6-9014-de6eca140331
09:47:51.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] RpcClient init label, labels = {module=naming, source=sdk}
09:47:51.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:47:51.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:47:51.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:47:51.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:47:51.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Success to connect to server [localhost:8848] on start up, connectionId = 1753235271095_127.0.0.1_6816
09:47:51.224 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Notify connected event to listeners.
09:47:51.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:47:51.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001daaa526370
09:47:51.717 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:47:51.765 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.825 seconds (JVM running for 9.799)
09:47:51.773 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:47:51.774 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:47:51.775 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:47:51.821 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:47:51.821 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:47:51.917 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:47:51.917 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:47:51.927 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:47:51.928 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:47:51.936 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:47:51.937 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:49:22.059 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:49:22.062 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:23:27.533 [nacos-grpc-client-executor-1909] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 25
11:23:27.553 [nacos-grpc-client-executor-1909] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:23:52.632 [nacos-grpc-client-executor-1914] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 28
11:23:52.650 [nacos-grpc-client-executor-1914] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:42:48.385 [nacos-grpc-client-executor-2291] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:42:48.404 [nacos-grpc-client-executor-2291] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 32
11:43:12.005 [nacos-grpc-client-executor-2302] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 35
11:43:12.022 [nacos-grpc-client-executor-2302] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 35
19:29:13.341 [nacos-grpc-client-executor-11349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 39
19:29:13.363 [nacos-grpc-client-executor-11349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 39
19:30:01.419 [nacos-grpc-client-executor-11367] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 42
19:30:01.434 [nacos-grpc-client-executor-11367] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 42
19:36:48.863 [nacos-grpc-client-executor-11493] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 47
19:36:48.885 [nacos-grpc-client-executor-11493] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 47
19:37:12.007 [nacos-grpc-client-executor-11503] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 51
19:37:12.022 [nacos-grpc-client-executor-11503] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 51
19:40:33.244 [nacos-grpc-client-executor-11566] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 54
19:40:33.260 [nacos-grpc-client-executor-11566] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 54
19:40:49.389 [nacos-grpc-client-executor-11569] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Receive server push request, request = NotifySubscriberRequest, requestId = 58
19:40:49.403 [nacos-grpc-client-executor-11569] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46d3d0bc-688c-47b6-9014-de6eca140331] Ack server push request, request = NotifySubscriberRequest, requestId = 58
20:22:57.194 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:22:57.208 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:22:57.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:22:57.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c598df3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:22:57.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753235271095_127.0.0.1_6816
20:22:57.541 [nacos-grpc-client-executor-12375] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753235271095_127.0.0.1_6816]Ignore complete event,isRunning:false,isAbandon=false
20:22:57.542 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6ae8db25[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 12376]
