09:12:31.130 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:32.620 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0
09:12:32.851 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 66 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:33.007 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:33.019 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:33.039 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:33.055 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:33.069 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:33.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:33.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ad8139ed38
09:12:33.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ad8139ef58
09:12:33.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:33.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:33.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:34.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752714754094_127.0.0.1_7183
09:12:34.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Notify connected event to listeners.
09:12:34.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:34.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ac467b9-b6d3-4185-b029-250fb2a27b3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ad81518668
09:12:34.679 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:42.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:12:42.884 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:42.884 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:43.232 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:45.295 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:12:45.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:12:45.303 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:12:55.127 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:58.351 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 20aa5851-07c9-461d-980e-afd0f4741379
09:12:58.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] RpcClient init label, labels = {module=naming, source=sdk}
09:12:58.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:58.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:58.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:58.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:58.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Success to connect to server [localhost:8848] on start up, connectionId = 1752714778367_127.0.0.1_7253
09:12:58.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:58.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ad81518668
09:12:58.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Notify connected event to listeners.
09:12:58.563 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:12:58.607 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:12:58.758 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.628 seconds (JVM running for 30.087)
09:12:58.796 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:12:58.798 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:12:58.798 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:12:58.965 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:12:59.097 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:12:59.122 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20aa5851-07c9-461d-980e-afd0f4741379] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:23:51.173 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:23:51.173 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:32:41.042 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:32:41.045 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:32:41.379 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:32:41.379 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@61ebfd57[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:32:41.379 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752714778367_127.0.0.1_7253
09:32:41.381 [nacos-grpc-client-executor-250] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752714778367_127.0.0.1_7253]Ignore complete event,isRunning:false,isAbandon=false
09:32:41.383 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4fea2c12[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 251]
09:32:41.544 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:32:41.555 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:32:41.564 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:32:41.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:32:41.567 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:32:41.567 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:59:18.968 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:59:20.022 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 756273d5-8776-4884-8f6c-d96984b20cc2_config-0
09:59:20.134 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:59:20.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:59:20.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:59:20.227 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:59:20.238 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:59:20.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:59:20.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:59:20.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023ea039dd70
09:59:20.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023ea039df90
09:59:20.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:59:20.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:59:20.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:59:21.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752717561234_127.0.0.1_14693
09:59:21.494 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Notify connected event to listeners.
09:59:21.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:59:21.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [756273d5-8776-4884-8f6c-d96984b20cc2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023ea0517b88
09:59:21.677 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:59:28.593 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:59:28.594 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:59:28.595 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:59:28.992 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:59:30.291 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:59:30.294 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:59:30.294 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:59:46.495 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:59:52.783 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb102375-77f6-4502-b819-90f4d8494989
09:59:52.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] RpcClient init label, labels = {module=naming, source=sdk}
09:59:52.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:59:52.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:59:52.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:59:52.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:59:52.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Success to connect to server [localhost:8848] on start up, connectionId = 1752717592820_127.0.0.1_14762
09:59:52.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:59:52.959 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Notify connected event to listeners.
09:59:52.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023ea0517b88
09:59:53.050 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:59:53.166 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:59:53.482 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 35.339 seconds (JVM running for 36.624)
09:59:53.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:59:53.517 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:59:53.519 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:59:53.574 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:59:53.597 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb102375-77f6-4502-b819-90f4d8494989] Ack server push request, request = NotifySubscriberRequest, requestId = 22
09:59:54.026 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:01:50.704 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:01:50.704 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:27:05.199 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:27:05.214 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:27:05.549 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:27:05.549 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7b08bbe3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:27:05.550 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752717592820_127.0.0.1_14762
12:27:05.551 [nacos-grpc-client-executor-1775] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752717592820_127.0.0.1_14762]Ignore complete event,isRunning:false,isAbandon=false
12:27:05.555 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1beeccb1[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 1776]
12:27:05.694 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:27:05.700 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:27:05.702 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:27:05.703 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:27:05.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:27:05.706 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:27:31.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:27:32.340 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 80036fb9-dc5c-437f-9b18-02504c2384a4_config-0
12:27:32.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
12:27:32.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 4 keys and 9 values 
12:27:32.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
12:27:32.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
12:27:32.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
12:27:32.465 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
12:27:32.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:27:32.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b5aa3b83f0
12:27:32.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001b5aa3b8610
12:27:32.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:27:32.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:27:32.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:27:33.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752726453229_127.0.0.1_11614
12:27:33.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:27:33.426 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Notify connected event to listeners.
12:27:33.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80036fb9-dc5c-437f-9b18-02504c2384a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b5aa4f0228
12:27:33.596 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:27:36.992 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:27:36.992 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:27:36.992 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:27:37.118 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:27:37.950 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:27:37.951 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:27:37.951 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:27:44.026 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:27:46.819 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 08d69c45-0d80-43c1-96cb-cc69096c6dd3
12:27:46.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] RpcClient init label, labels = {module=naming, source=sdk}
12:27:46.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:27:46.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:27:46.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:27:46.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:27:46.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Success to connect to server [localhost:8848] on start up, connectionId = 1752726466833_127.0.0.1_11642
12:27:46.948 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Notify connected event to listeners.
12:27:46.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:27:46.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b5aa4f0228
12:27:46.992 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:27:47.015 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:27:47.103 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.914 seconds (JVM running for 17.814)
12:27:47.113 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:27:47.113 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:27:47.114 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:27:47.538 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Receive server push request, request = NotifySubscriberRequest, requestId = 29
12:27:47.553 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08d69c45-0d80-43c1-96cb-cc69096c6dd3] Ack server push request, request = NotifySubscriberRequest, requestId = 29
12:29:14.605 [http-nio-9600-exec-5] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:29:15.647 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:29:15.647 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:29:15.647 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:29:15.648 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:29:15.648 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:29:15.648 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:36:49.883 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:36:49.888 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:36:50.212 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:36:50.213 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f89303b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:36:50.213 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752726466833_127.0.0.1_11642
12:36:50.214 [nacos-grpc-client-executor-119] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752726466833_127.0.0.1_11642]Ignore complete event,isRunning:false,isAbandon=false
12:36:50.215 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5eebe88f[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 120]
12:36:50.344 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:36:50.344 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:36:50.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:36:50.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:36:50.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:36:50.345 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:36:54.657 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:36:55.165 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 12ef8b03-970c-4b07-b105-b8fe3033852a_config-0
12:36:55.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
12:36:55.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
12:36:55.249 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:36:55.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
12:36:55.261 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
12:36:55.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
12:36:55.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:36:55.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019f533ce8d8
12:36:55.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000019f533ceaf8
12:36:55.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:36:55.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:36:55.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:36:55.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752727015778_127.0.0.1_13193
12:36:55.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Notify connected event to listeners.
12:36:55.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:36:55.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12ef8b03-970c-4b07-b105-b8fe3033852a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019f53508d48
12:36:56.039 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:36:58.335 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:36:58.335 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:36:58.335 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:36:58.441 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:36:59.101 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:36:59.102 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:36:59.102 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:37:03.933 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:37:05.965 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e4279362-d575-4802-845a-9c095a78981f
12:37:05.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] RpcClient init label, labels = {module=naming, source=sdk}
12:37:05.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:37:05.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:37:05.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:37:05.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:37:06.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Success to connect to server [localhost:8848] on start up, connectionId = 1752727025978_127.0.0.1_13208
12:37:06.109 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Notify connected event to listeners.
12:37:06.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:37:06.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019f53508d48
12:37:06.144 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:37:06.164 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:37:06.248 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.079 seconds (JVM running for 13.069)
12:37:06.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:37:06.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:37:06.261 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:37:06.640 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Receive server push request, request = NotifySubscriberRequest, requestId = 34
12:37:06.653 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e4279362-d575-4802-845a-9c095a78981f] Ack server push request, request = NotifySubscriberRequest, requestId = 34
12:37:06.777 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:39:22.271 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:39:22.271 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:39:22.274 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:39:22.276 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:39:22.281 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:39:22.281 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:31:33.498 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:31:33.498 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:31:33.860 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:31:33.860 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d170eea[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:31:33.860 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752727025978_127.0.0.1_13208
14:31:33.860 [nacos-grpc-client-executor-1386] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752727025978_127.0.0.1_13208]Ignore complete event,isRunning:false,isAbandon=false
14:31:33.871 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2aa0a6ae[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1387]
14:31:34.079 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:31:34.081 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:31:34.085 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:31:34.085 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:31:34.087 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:31:34.089 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:31:39.627 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:40.422 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0
14:31:40.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:40.551 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:40.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:40.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:40.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:40.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:40.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:40.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001994039ed38
14:31:40.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001994039ef58
14:31:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:40.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:40.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:41.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752733901363_127.0.0.1_2070
14:31:41.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Notify connected event to listeners.
14:31:41.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:41.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b20a51-1eda-4625-9e6a-c2433b4dee61_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019940518668
14:31:41.771 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:45.859 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:31:45.860 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:45.860 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:31:46.077 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:46.831 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:31:46.833 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:31:46.834 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:32:00.486 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:32:03.636 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4b5dda1d-16be-446a-90f6-92a4159c22d8
14:32:03.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] RpcClient init label, labels = {module=naming, source=sdk}
14:32:03.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:32:03.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:32:03.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:32:03.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:32:03.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Success to connect to server [localhost:8848] on start up, connectionId = 1752733923647_127.0.0.1_2133
14:32:03.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:03.825 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Notify connected event to listeners.
14:32:03.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019940518668
14:32:03.902 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:32:03.938 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:32:04.108 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.089 seconds (JVM running for 26.102)
14:32:04.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:32:04.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:32:04.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:32:04.447 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:32:04.467 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b5dda1d-16be-446a-90f6-92a4159c22d8] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:32:04.670 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:40:06.850 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:40:06.850 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:40:06.850 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:40:06.858 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:40:06.865 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:40:06.867 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:20:53.840 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:20:53.845 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:20:54.188 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:20:54.189 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@65491c26[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:20:54.190 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752733923647_127.0.0.1_2133
15:20:54.194 [nacos-grpc-client-executor-597] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752733923647_127.0.0.1_2133]Ignore complete event,isRunning:false,isAbandon=false
15:20:54.198 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3facab24[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 598]
15:20:54.380 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:20:54.381 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:20:54.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:20:54.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:20:54.385 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:20:54.386 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:21:00.083 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:00.964 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0
15:21:01.028 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:01.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:01.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:01.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:01.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:01.107 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:01.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:01.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001888139eaf8
15:21:01.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001888139ed18
15:21:01.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:01.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:01.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:02.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752736862043_127.0.0.1_10768
15:21:02.269 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Notify connected event to listeners.
15:21:02.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:02.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09cd9083-63d7-4218-82cf-c5e8c1622b79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018881518fb0
15:21:02.449 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:06.854 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:21:06.856 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:06.857 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:07.087 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:08.051 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:21:08.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:21:08.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:21:15.823 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:21:18.856 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5cfa45f6-b19e-43fb-b341-006fd54446af
15:21:18.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] RpcClient init label, labels = {module=naming, source=sdk}
15:21:18.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:21:18.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:21:18.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:21:18.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:19.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Success to connect to server [localhost:8848] on start up, connectionId = 1752736878884_127.0.0.1_10806
15:21:19.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:19.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018881518fb0
15:21:19.007 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Notify connected event to listeners.
15:21:19.081 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:21:19.121 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:21:19.260 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.794 seconds (JVM running for 20.879)
15:21:19.299 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:21:19.299 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:21:19.299 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:21:19.518 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:21:19.582 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Receive server push request, request = NotifySubscriberRequest, requestId = 44
15:21:19.674 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cfa45f6-b19e-43fb-b341-006fd54446af] Ack server push request, request = NotifySubscriberRequest, requestId = 44
15:21:21.738 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:21:21.738 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:27:02.584 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:27:02.588 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:27:02.920 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:27:02.920 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@352651c5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:27:02.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752736878884_127.0.0.1_10806
15:27:02.925 [nacos-grpc-client-executor-80] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752736878884_127.0.0.1_10806]Ignore complete event,isRunning:false,isAbandon=false
15:27:02.929 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1ae59de9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 81]
15:27:03.095 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:27:03.102 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:27:03.115 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:27:03.116 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:27:03.118 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:27:03.118 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:27:08.095 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:27:08.884 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 04f46481-83ee-4ada-9f48-753955837b86_config-0
15:27:08.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
15:27:09.032 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:27:09.041 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
15:27:09.054 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:27:09.066 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:27:09.082 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
15:27:09.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:27:09.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001832839ed38
15:27:09.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001832839ef58
15:27:09.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:27:09.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:27:09.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:27:10.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752737229952_127.0.0.1_11560
15:27:10.181 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Notify connected event to listeners.
15:27:10.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:27:10.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04f46481-83ee-4ada-9f48-753955837b86_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018328518ad8
15:27:10.363 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:27:14.212 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:27:14.212 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:27:14.214 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:27:14.404 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:27:15.147 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:27:15.149 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:27:15.150 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:27:23.006 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:27:26.214 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3afea727-615a-40ba-9bcb-24e453dde9d3
15:27:26.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] RpcClient init label, labels = {module=naming, source=sdk}
15:27:26.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:27:26.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:27:26.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:27:26.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:27:26.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Success to connect to server [localhost:8848] on start up, connectionId = 1752737246228_127.0.0.1_11588
15:27:26.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:27:26.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018328518ad8
15:27:26.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Notify connected event to listeners.
15:27:26.437 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:27:26.484 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:27:26.658 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.138 seconds (JVM running for 20.106)
15:27:26.679 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:27:26.680 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:27:26.681 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:27:26.953 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Receive server push request, request = NotifySubscriberRequest, requestId = 49
15:27:26.971 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3afea727-615a-40ba-9bcb-24e453dde9d3] Ack server push request, request = NotifySubscriberRequest, requestId = 49
15:27:27.268 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:27:35.674 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:27:35.680 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:30:56.904 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:30:56.910 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:30:57.254 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:30:57.257 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c00bf4a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:30:57.257 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752737246228_127.0.0.1_11588
15:30:57.261 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752737246228_127.0.0.1_11588]Ignore complete event,isRunning:false,isAbandon=false
15:30:57.266 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@306f4e83[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 54]
15:30:57.446 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:30:57.451 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:30:57.464 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:30:57.465 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:30:57.466 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:30:57.466 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:31:02.537 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:31:03.452 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0
15:31:03.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
15:31:03.590 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:31:03.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:31:03.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:31:03.619 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:31:03.635 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
15:31:03.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:31:03.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f2e239eaf8
15:31:03.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f2e239ed18
15:31:03.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:31:03.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:31:03.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:31:04.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752737464718_127.0.0.1_12083
15:31:04.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Notify connected event to listeners.
15:31:04.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:31:04.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f98d6caa-bf49-47b4-93fb-076bbcbea583_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f2e2518ad8
15:31:05.086 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:31:09.366 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:31:09.367 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:31:09.368 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:31:09.668 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:31:10.463 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:31:10.465 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:31:10.466 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:31:19.265 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:31:22.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 506fa372-a41f-4e8c-a816-7f78f2049581
15:31:22.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] RpcClient init label, labels = {module=naming, source=sdk}
15:31:22.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:31:22.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:31:22.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:31:22.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:31:22.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Success to connect to server [localhost:8848] on start up, connectionId = 1752737482674_127.0.0.1_12124
15:31:22.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:31:22.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f2e2518ad8
15:31:22.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Notify connected event to listeners.
15:31:22.868 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:31:22.908 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:31:23.074 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.139 seconds (JVM running for 22.203)
15:31:23.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:31:23.092 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:31:23.092 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:31:23.379 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:31:23.389 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Receive server push request, request = NotifySubscriberRequest, requestId = 55
15:31:23.411 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [506fa372-a41f-4e8c-a816-7f78f2049581] Ack server push request, request = NotifySubscriberRequest, requestId = 55
15:31:29.850 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:31:29.850 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:34:22.776 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:34:22.782 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:34:23.124 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:34:23.125 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2565b4b6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:34:23.125 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752737482674_127.0.0.1_12124
15:34:23.131 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ea2368a[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 46]
15:34:23.132 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752737482674_127.0.0.1_12124]Ignore complete event,isRunning:false,isAbandon=false
15:34:23.310 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:34:23.315 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:34:23.326 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:34:23.326 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:34:23.330 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:34:23.330 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:34:28.787 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:34:29.607 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0
15:34:29.678 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
15:34:29.723 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
15:34:29.741 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
15:34:29.754 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:34:29.767 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:34:29.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
15:34:29.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:34:29.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002940139e8d8
15:34:29.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002940139eaf8
15:34:29.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:34:29.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:34:29.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:34:30.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752737670579_127.0.0.1_12506
15:34:30.834 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Notify connected event to listeners.
15:34:30.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:34:30.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7cbbfe4-692d-4ef1-9003-76e10a273792_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029401518fd0
15:34:31.051 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:34:35.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:34:35.088 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:34:35.088 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:34:35.269 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:34:36.167 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:34:36.170 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:34:36.170 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:34:44.803 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:34:48.032 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d0a8e276-8669-4ddf-8c48-1e6e60817fcf
15:34:48.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] RpcClient init label, labels = {module=naming, source=sdk}
15:34:48.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:34:48.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:34:48.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:34:48.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:34:48.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Success to connect to server [localhost:8848] on start up, connectionId = 1752737688055_127.0.0.1_12562
15:34:48.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:34:48.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Notify connected event to listeners.
15:34:48.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029401518fd0
15:34:48.254 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:34:48.288 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:34:48.435 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.304 seconds (JVM running for 21.657)
15:34:48.464 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:34:48.465 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:34:48.466 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:34:48.597 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:34:48.806 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Receive server push request, request = NotifySubscriberRequest, requestId = 59
15:34:48.827 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0a8e276-8669-4ddf-8c48-1e6e60817fcf] Ack server push request, request = NotifySubscriberRequest, requestId = 59
15:35:07.665 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:35:07.665 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:57:17.852 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:57:17.855 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:57:18.193 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:57:18.193 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@64d12f4f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:57:18.194 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752737688055_127.0.0.1_12562
15:57:18.196 [nacos-grpc-client-executor-284] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752737688055_127.0.0.1_12562]Ignore complete event,isRunning:false,isAbandon=false
15:57:18.200 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@a27413e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 285]
15:57:18.375 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:57:18.379 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:57:18.391 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:57:18.391 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:57:18.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:57:18.393 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:57:24.043 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:57:24.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8c961af-698f-495b-95ee-fbfed644b642_config-0
15:57:24.857 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
15:57:24.901 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
15:57:24.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:57:24.919 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:57:24.927 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:57:24.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:57:24.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:57:24.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020dac39eaf8
15:57:24.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020dac39ed18
15:57:24.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:57:24.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:57:24.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:57:25.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752739045746_127.0.0.1_2415
15:57:25.969 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Notify connected event to listeners.
15:57:25.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:57:25.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8c961af-698f-495b-95ee-fbfed644b642_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020dac518ad8
15:57:26.181 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:57:29.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:57:29.835 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:57:29.835 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:57:30.004 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:57:30.773 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:57:30.775 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:57:30.775 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:57:38.709 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:57:41.664 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 73f33c03-79d0-4ef9-a317-9817ad741616
15:57:41.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] RpcClient init label, labels = {module=naming, source=sdk}
15:57:41.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:57:41.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:57:41.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:57:41.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:57:41.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Success to connect to server [localhost:8848] on start up, connectionId = 1752739061676_127.0.0.1_2456
15:57:41.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:57:41.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020dac518ad8
15:57:41.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Notify connected event to listeners.
15:57:41.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:57:41.927 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:57:42.098 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.644 seconds (JVM running for 19.733)
15:57:42.134 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:57:42.135 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:57:42.135 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:57:42.448 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Receive server push request, request = NotifySubscriberRequest, requestId = 64
15:57:42.482 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73f33c03-79d0-4ef9-a317-9817ad741616] Ack server push request, request = NotifySubscriberRequest, requestId = 64
15:57:42.593 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:58:00.710 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:58:00.710 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:13:30.574 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:13:30.581 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:13:30.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:13:30.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@657b0dec[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:13:30.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752739061676_127.0.0.1_2456
18:13:30.913 [nacos-grpc-client-executor-1642] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752739061676_127.0.0.1_2456]Ignore complete event,isRunning:false,isAbandon=false
18:13:30.927 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1570361a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1643]
18:13:31.118 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:13:31.131 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:13:31.142 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:13:31.143 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:13:31.144 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:13:31.144 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:13:41.314 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:13:42.753 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0
18:13:42.925 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 70 ms to scan 1 urls, producing 3 keys and 6 values 
18:13:42.997 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
18:13:43.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:13:43.028 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
18:13:43.043 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
18:13:43.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
18:13:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:13:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001962c3b7660
18:13:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001962c3b7880
18:13:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:13:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:13:43.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:13:45.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752747224873_127.0.0.1_13817
18:13:45.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Notify connected event to listeners.
18:13:45.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:13:45.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ae66e7c-d52b-44fc-8d50-b21479f9cecb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001962c4f0fb0
18:13:45.386 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:13:49.590 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:13:49.591 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:13:49.591 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:13:49.732 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:13:50.331 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:13:50.332 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:13:50.333 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:13:56.396 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:13:59.225 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f70c86e0-55f2-4df3-a32a-b76815eb5b42
18:13:59.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] RpcClient init label, labels = {module=naming, source=sdk}
18:13:59.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:13:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:13:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:13:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:13:59.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Success to connect to server [localhost:8848] on start up, connectionId = 1752747239238_127.0.0.1_13868
18:13:59.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:13:59.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001962c4f0fb0
18:13:59.357 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Notify connected event to listeners.
18:13:59.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:13:59.439 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:13:59.559 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.64 seconds (JVM running for 22.408)
18:13:59.590 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:13:59.591 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:13:59.591 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:13:59.922 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Receive server push request, request = NotifySubscriberRequest, requestId = 71
18:13:59.937 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Ack server push request, request = NotifySubscriberRequest, requestId = 71
18:14:15.922 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:14:17.821 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:14:17.821 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:21:18.769 [nacos-grpc-client-executor-829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Receive server push request, request = NotifySubscriberRequest, requestId = 72
19:21:18.769 [nacos-grpc-client-executor-829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f70c86e0-55f2-4df3-a32a-b76815eb5b42] Ack server push request, request = NotifySubscriberRequest, requestId = 72
20:26:58.018 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:26:58.026 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:26:58.371 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:26:58.371 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1896d985[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:26:58.371 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752747239238_127.0.0.1_13868
20:26:58.375 [nacos-grpc-client-executor-1721] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752747239238_127.0.0.1_13868]Ignore complete event,isRunning:false,isAbandon=false
20:26:58.381 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5d35cad[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1722]
20:26:58.597 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:26:58.609 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:26:58.625 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:26:58.625 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:26:58.629 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:26:58.632 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:27:04.842 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:27:05.684 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0
20:27:05.756 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
20:27:05.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
20:27:05.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:27:05.818 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
20:27:05.827 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
20:27:05.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
20:27:05.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:27:05.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000026d2939ed38
20:27:05.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000026d2939ef58
20:27:05.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:27:05.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:27:05.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:27:06.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752755226728_127.0.0.1_5522
20:27:06.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Notify connected event to listeners.
20:27:06.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:27:06.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d44d4a26-12d7-4de2-b0c4-7abdb32bc2db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026d29518fb0
20:27:07.152 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:27:11.279 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:27:11.280 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:27:11.280 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:27:11.475 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:27:12.202 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:27:12.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:27:12.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:27:20.825 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:27:24.216 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fcbc4dd-42bb-481d-a639-89f2a1fcd305
20:27:24.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] RpcClient init label, labels = {module=naming, source=sdk}
20:27:24.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:27:24.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:27:24.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:27:24.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:27:24.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Success to connect to server [localhost:8848] on start up, connectionId = 1752755244233_127.0.0.1_5555
20:27:24.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:27:24.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Notify connected event to listeners.
20:27:24.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026d29518fb0
20:27:24.439 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:27:24.494 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:27:24.646 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.404 seconds (JVM running for 21.448)
20:27:24.683 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:27:24.684 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:27:24.685 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:27:25.002 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:27:25.006 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Receive server push request, request = NotifySubscriberRequest, requestId = 78
20:27:25.024 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fcbc4dd-42bb-481d-a639-89f2a1fcd305] Ack server push request, request = NotifySubscriberRequest, requestId = 78
20:27:45.262 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:27:45.262 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:28:29.290 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:28:29.295 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:28:29.632 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:28:29.632 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@20b63004[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:28:29.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752755244233_127.0.0.1_5555
20:28:29.638 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752755244233_127.0.0.1_5555]Ignore complete event,isRunning:false,isAbandon=false
20:28:29.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3eea0e12[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 26]
20:28:29.813 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:28:29.816 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:28:29.823 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:28:29.823 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:28:29.825 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:28:29.825 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:28:35.335 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:28:36.389 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d63d267b-5fef-4377-9358-9019664aeae6_config-0
20:28:36.469 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
20:28:36.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
20:28:36.516 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
20:28:36.526 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
20:28:36.537 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
20:28:36.549 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
20:28:36.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:28:36.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d12139eaf8
20:28:36.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d12139ed18
20:28:36.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:28:36.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:28:36.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:28:37.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752755317313_127.0.0.1_5752
20:28:37.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Notify connected event to listeners.
20:28:37.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:28:37.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63d267b-5fef-4377-9358-9019664aeae6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d121518ad8
20:28:37.739 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:28:42.153 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:28:42.153 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:28:42.153 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:28:42.337 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:28:43.127 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:28:43.127 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:28:43.127 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:28:51.665 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:28:55.144 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0d88d600-d3c8-4f5d-bc94-662b59ef6391
20:28:55.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] RpcClient init label, labels = {module=naming, source=sdk}
20:28:55.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:28:55.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:28:55.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:28:55.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:28:55.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Success to connect to server [localhost:8848] on start up, connectionId = 1752755335157_127.0.0.1_5833
20:28:55.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:28:55.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d121518ad8
20:28:55.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Notify connected event to listeners.
20:28:55.362 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:28:55.405 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:28:55.587 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.924 seconds (JVM running for 22.025)
20:28:55.608 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:28:55.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:28:55.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:28:55.912 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Receive server push request, request = NotifySubscriberRequest, requestId = 84
20:28:55.936 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d88d600-d3c8-4f5d-bc94-662b59ef6391] Ack server push request, request = NotifySubscriberRequest, requestId = 84
20:28:56.021 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:29:02.017 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:29:02.018 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:41:40.254 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:40.261 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:40.584 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:40.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@752d6291[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:40.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752755335157_127.0.0.1_5833
20:41:40.587 [nacos-grpc-client-executor-162] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752755335157_127.0.0.1_5833]Ignore complete event,isRunning:false,isAbandon=false
20:41:40.590 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@18b90ee8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 163]
20:41:40.744 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:41:40.746 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:41:40.753 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:41:40.753 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:41:40.753 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:41:40.753 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
