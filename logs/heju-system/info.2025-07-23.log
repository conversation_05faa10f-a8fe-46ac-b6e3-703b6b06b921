09:40:01.598 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:02.441 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0
09:40:02.524 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:02.569 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:02.580 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:02.591 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:02.601 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:02.613 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:02.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:02.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000271a739eaf8
09:40:02.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000271a739ed18
09:40:02.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:02.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:02.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:03.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234803474_127.0.0.1_4409
09:40:03.691 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Notify connected event to listeners.
09:40:03.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:03.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c413d59f-560c-4750-b9f4-0f111ea78ef9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000271a7518ad8
09:40:03.940 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:08.296 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:40:08.297 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:40:08.297 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:40:08.492 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:40:09.335 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:40:09.337 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:40:09.337 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:40:16.360 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:40:16.364 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:40:16.375 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:40:16.375 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:40:16.376 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:42:32.419 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:42:33.214 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0
09:42:33.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
09:42:33.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:42:33.334 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:42:33.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:42:33.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:42:33.364 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:42:33.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:42:33.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001994539dd70
09:42:33.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001994539df90
09:42:33.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:42:33.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:42:33.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:42:34.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234954212_127.0.0.1_5348
09:42:34.439 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Notify connected event to listeners.
09:42:34.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:42:34.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17d0e759-0a95-4fea-9577-f2b176bbfb1c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019945517d88
09:42:34.634 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:42:38.877 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:42:38.878 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:42:38.878 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:42:39.070 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:42:39.802 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:42:39.804 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:42:39.804 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:42:53.325 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:42:53.331 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:42:53.349 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:42:53.349 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:42:53.352 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:48:58.211 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:48:59.003 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0
09:48:59.084 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
09:48:59.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:48:59.131 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:48:59.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:48:59.150 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:48:59.162 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:48:59.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:48:59.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023a3939e690
09:48:59.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023a3939e8b0
09:48:59.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:48:59.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:48:59.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:00.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753235339950_127.0.0.1_6964
09:49:00.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Notify connected event to listeners.
09:49:00.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:00.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5efdff-1526-48ce-b567-6c5352ab8a40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023a395186d8
09:49:00.359 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:49:04.013 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:49:04.014 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:49:04.015 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:49:04.211 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:49:04.988 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:49:04.990 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:49:04.991 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:49:12.791 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:49:15.793 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7
09:49:15.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] RpcClient init label, labels = {module=naming, source=sdk}
09:49:15.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:49:15.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:49:15.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:49:15.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:15.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Success to connect to server [localhost:8848] on start up, connectionId = 1753235355810_127.0.0.1_6992
09:49:15.933 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Notify connected event to listeners.
09:49:15.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:15.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023a395186d8
09:49:16.014 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:49:16.064 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:49:16.213 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.622 seconds (JVM running for 19.807)
09:49:16.230 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:49:16.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:49:16.233 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:49:16.538 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:49:16.553 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:49:16.579 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:52:13.826 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:52:13.827 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d2d4bd-f8f5-4515-b48b-5e9cf0635bc7] Ack server push request, request = NotifySubscriberRequest, requestId = 21
09:52:14.421 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:52:14.422 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:52:14.611 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:52:14.612 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:23:26.978 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:23:26.978 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:23:27.321 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:23:27.321 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1a4e9d34[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:23:27.322 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753235355810_127.0.0.1_6992
11:23:27.324 [nacos-grpc-client-executor-1180] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753235355810_127.0.0.1_6992]Ignore complete event,isRunning:false,isAbandon=false
11:23:27.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@339376ff[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1181]
11:23:27.494 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:23:27.499 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:23:27.509 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:23:27.509 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:23:27.509 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:23:27.509 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:23:27.509 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:23:27.509 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:23:33.323 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:23:34.112 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0
11:23:34.193 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
11:23:34.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:23:34.254 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:23:34.265 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:23:34.276 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
11:23:34.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
11:23:34.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:23:34.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001dfad39e480
11:23:34.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001dfad39e6a0
11:23:34.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:23:34.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:23:34.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:23:35.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753241015111_127.0.0.1_10802
11:23:35.355 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Notify connected event to listeners.
11:23:35.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:23:35.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f7f11c6-8b76-4525-a34e-b52ad004fde7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dfad518228
11:23:35.512 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:23:39.488 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:23:39.489 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:23:39.489 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:23:39.703 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:23:40.605 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:23:40.608 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:23:40.609 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:23:48.721 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:23:51.881 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3b5f8f9f-107b-44d3-ac07-896dabe6c46c
11:23:51.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] RpcClient init label, labels = {module=naming, source=sdk}
11:23:51.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:23:51.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:23:51.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:23:51.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:23:52.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Success to connect to server [localhost:8848] on start up, connectionId = 1753241031892_127.0.0.1_10887
11:23:52.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Notify connected event to listeners.
11:23:52.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:23:52.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dfad518228
11:23:52.084 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:23:52.134 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:23:52.308 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.654 seconds (JVM running for 20.791)
11:23:52.328 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:23:52.329 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:23:52.329 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:23:52.663 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:23:52.684 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5f8f9f-107b-44d3-ac07-896dabe6c46c] Ack server push request, request = NotifySubscriberRequest, requestId = 29
11:23:52.706 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:24:04.189 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:24:04.189 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:42:47.835 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:42:47.840 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:42:48.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:42:48.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@********[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:42:48.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753241031892_127.0.0.1_10887
11:42:48.179 [nacos-grpc-client-executor-235] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753241031892_127.0.0.1_10887]Ignore complete event,isRunning:false,isAbandon=false
11:42:48.180 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@11f6f906[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 236]
11:42:48.347 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:42:48.351 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:42:48.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:42:48.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:42:48.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:42:48.367 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:42:54.069 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:42:54.791 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0
11:42:54.859 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
11:42:54.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:42:54.903 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:42:54.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:42:54.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:42:54.931 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:42:54.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:42:54.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002525639f1c0
11:42:54.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002525639f3e0
11:42:54.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:42:54.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:42:54.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:42:55.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753242175657_127.0.0.1_2837
11:42:55.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:42:55.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Notify connected event to listeners.
11:42:55.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e74dee44-7a44-4f9f-9ee0-f7db516db2a3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025256518fb0
11:42:56.014 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:42:59.711 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:42:59.712 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:42:59.712 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:42:59.894 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:43:00.689 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:43:00.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:43:00.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:43:08.277 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:43:11.259 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7db3c05b-e525-439c-b703-b4c16291bcf0
11:43:11.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] RpcClient init label, labels = {module=naming, source=sdk}
11:43:11.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:43:11.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:43:11.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:43:11.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:11.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Success to connect to server [localhost:8848] on start up, connectionId = 1753242191277_127.0.0.1_2977
11:43:11.398 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Notify connected event to listeners.
11:43:11.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:11.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025256518fb0
11:43:11.490 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:43:11.534 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:43:11.680 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.315 seconds (JVM running for 19.426)
11:43:11.716 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:43:11.717 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:43:11.718 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:43:12.026 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:43:12.030 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:43:12.045 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7db3c05b-e525-439c-b703-b4c16291bcf0] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:49:03.934 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:49:03.935 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:29:12.778 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:29:12.784 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:29:13.120 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:29:13.120 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@19b9b473[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:29:13.120 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753242191277_127.0.0.1_2977
19:29:13.122 [nacos-grpc-client-executor-5751] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753242191277_127.0.0.1_2977]Ignore complete event,isRunning:false,isAbandon=false
19:29:13.125 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@136be61a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5752]
19:29:13.271 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:29:13.275 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:29:13.277 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:29:13.277 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:29:13.278 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:29:13.278 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:29:42.186 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:29:42.764 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 49ecb788-6653-4eba-a29b-dba2601ac231_config-0
19:29:42.823 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
19:29:42.859 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
19:29:42.866 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
19:29:42.873 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
19:29:42.882 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
19:29:42.891 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
19:29:42.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:29:42.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001da663b8fc8
19:29:42.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001da663b91e8
19:29:42.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:29:42.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:29:42.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:29:43.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753270183476_127.0.0.1_13114
19:29:43.672 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Notify connected event to listeners.
19:29:43.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:29:43.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ecb788-6653-4eba-a29b-dba2601ac231_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001da664f1210
19:29:43.795 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:29:46.392 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:29:46.392 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:29:46.392 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:29:46.513 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:29:47.001 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:29:47.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:29:47.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:29:57.380 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:30:00.784 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b57b298a-e52e-410a-9cd6-107155b7a90d
19:30:00.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] RpcClient init label, labels = {module=naming, source=sdk}
19:30:00.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:30:00.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:30:00.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:30:00.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:30:00.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Success to connect to server [localhost:8848] on start up, connectionId = 1753270200794_127.0.0.1_13156
19:30:00.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:00.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001da664f1210
19:30:00.910 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Notify connected event to listeners.
19:30:00.949 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:30:00.971 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:30:01.055 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.322 seconds (JVM running for 21.238)
19:30:01.066 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:30:01.066 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:30:01.066 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:30:01.431 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Receive server push request, request = NotifySubscriberRequest, requestId = 43
19:30:01.444 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Ack server push request, request = NotifySubscriberRequest, requestId = 43
19:30:46.645 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:30:48.922 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Receive server push request, request = NotifySubscriberRequest, requestId = 44
19:30:48.923 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b57b298a-e52e-410a-9cd6-107155b7a90d] Ack server push request, request = NotifySubscriberRequest, requestId = 44
19:30:49.816 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:30:49.818 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:30:49.824 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:30:49.824 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:30:49.841 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:30:49.842 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:36:48.343 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:36:48.346 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:36:48.680 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:36:48.681 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@45f12e85[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:36:48.681 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753270200794_127.0.0.1_13156
19:36:48.682 [nacos-grpc-client-executor-93] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753270200794_127.0.0.1_13156]Ignore complete event,isRunning:false,isAbandon=false
19:36:48.684 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@288fc334[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 94]
19:36:48.826 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:36:48.826 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:36:48.827 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:36:48.827 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:36:48.828 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:36:48.828 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:36:53.140 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:36:53.671 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7531f350-5339-4d5e-ac81-aed344af10ec_config-0
19:36:53.722 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
19:36:53.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 4 keys and 9 values 
19:36:53.756 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
19:36:53.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
19:36:53.768 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
19:36:53.773 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
19:36:53.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:36:53.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000219a93cdb10
19:36:53.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000219a93cdd30
19:36:53.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:36:53.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:36:53.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:36:54.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753270614267_127.0.0.1_14014
19:36:54.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Notify connected event to listeners.
19:36:54.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:36:54.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7531f350-5339-4d5e-ac81-aed344af10ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000219a9508228
19:36:54.519 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:36:56.932 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:36:56.933 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:36:56.933 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:36:57.043 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:36:57.540 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:36:57.541 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:36:57.541 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:37:06.835 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:37:11.249 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8a95dcc6-d43a-42da-a9e9-dd6288dd9a78
19:37:11.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] RpcClient init label, labels = {module=naming, source=sdk}
19:37:11.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:37:11.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:37:11.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:37:11.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:37:11.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Success to connect to server [localhost:8848] on start up, connectionId = 1753270631257_127.0.0.1_14048
19:37:11.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Notify connected event to listeners.
19:37:11.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:37:11.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000219a9508228
19:37:11.454 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:37:11.489 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:37:11.685 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.043 seconds (JVM running for 19.845)
19:37:11.702 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:37:11.702 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:37:11.702 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:37:11.790 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:37:12.023 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Receive server push request, request = NotifySubscriberRequest, requestId = 48
19:37:12.029 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a95dcc6-d43a-42da-a9e9-dd6288dd9a78] Ack server push request, request = NotifySubscriberRequest, requestId = 48
19:37:24.824 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:37:24.825 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:40:32.707 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:40:32.710 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:40:33.043 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:40:33.043 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c2c60e8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:40:33.043 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753270631257_127.0.0.1_14048
19:40:33.044 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753270631257_127.0.0.1_14048]Ignore complete event,isRunning:false,isAbandon=false
19:40:33.046 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4c1a5133[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 50]
19:40:33.176 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:40:33.178 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:40:33.182 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:40:33.183 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:40:33.183 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:40:33.183 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:40:37.153 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:40:37.686 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0
19:40:37.731 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
19:40:37.757 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
19:40:37.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
19:40:37.769 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
19:40:37.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
19:40:37.783 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
19:40:37.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:40:37.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b4013cdd70
19:40:37.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002b4013cdf90
19:40:37.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:40:37.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:40:37.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:40:38.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753270838277_127.0.0.1_14445
19:40:38.455 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Notify connected event to listeners.
19:40:38.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:40:38.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a6f82de-6c2d-4332-a9cd-57e4a5b8bc40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b401508228
19:40:38.526 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:40:40.843 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:40:40.844 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:40:40.844 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:40:40.951 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:40:41.442 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:40:41.443 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:40:41.443 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:40:46.622 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:40:48.734 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c6bc9200-d0a3-4894-98fa-9e6591e655a7
19:40:48.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] RpcClient init label, labels = {module=naming, source=sdk}
19:40:48.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:40:48.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:40:48.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:40:48.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:40:48.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Success to connect to server [localhost:8848] on start up, connectionId = 1753270848745_127.0.0.1_14496
19:40:48.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:40:48.856 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Notify connected event to listeners.
19:40:48.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b401508228
19:40:48.897 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:40:48.919 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:40:49.007 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.321 seconds (JVM running for 13.162)
19:40:49.020 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:40:49.020 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:40:49.021 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:40:49.325 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:40:49.407 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Receive server push request, request = NotifySubscriberRequest, requestId = 57
19:40:49.421 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6bc9200-d0a3-4894-98fa-9e6591e655a7] Ack server push request, request = NotifySubscriberRequest, requestId = 57
19:40:58.534 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:40:58.534 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:22:57.234 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:22:57.237 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:22:57.569 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:22:57.570 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3da71b96[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:22:57.570 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753270848745_127.0.0.1_14496
20:22:57.571 [nacos-grpc-client-executor-517] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753270848745_127.0.0.1_14496]Ignore complete event,isRunning:false,isAbandon=false
20:22:57.572 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@61b75b0e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 518]
20:22:57.713 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:22:57.715 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:22:57.721 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:22:57.721 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:22:57.722 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:22:57.722 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
