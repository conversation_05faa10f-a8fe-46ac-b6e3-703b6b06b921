09:20:58.261 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:00.093 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 37db9970-c805-47b8-aed5-89fcea349640_config-0
09:21:00.269 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 58 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:00.371 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:00.393 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:00.408 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:00.427 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:00.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:00.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:00.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020e153bdd70
09:21:00.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020e153bdf90
09:21:00.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:00.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:00.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:02.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751851261836_127.0.0.1_10537
09:21:02.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Notify connected event to listeners.
09:21:02.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:02.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37db9970-c805-47b8-aed5-89fcea349640_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020e154f7b88
09:21:02.300 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:07.870 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:21:07.871 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:07.872 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:21:08.221 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:09.457 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:21:09.460 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:21:09.461 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:21:21.293 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:25.705 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 09187ecc-2847-4f38-b1c8-a89397c754dd
09:21:25.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] RpcClient init label, labels = {module=naming, source=sdk}
09:21:25.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:25.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:25.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:25.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:25.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Success to connect to server [localhost:8848] on start up, connectionId = 1751851285717_127.0.0.1_10790
09:21:25.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:25.832 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Notify connected event to listeners.
09:21:25.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020e154f7b88
09:21:25.885 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:21:25.930 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:21:26.105 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.341 seconds (JVM running for 31.359)
09:21:26.120 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:21:26.120 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:21:26.123 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:21:26.415 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:21:26.434 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09187ecc-2847-4f38-b1c8-a89397c754dd] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:21:26.434 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:18:11.852 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:18:11.852 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:20:37.277 [http-nio-9600-exec-19] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:20:37.277 [http-nio-9600-exec-19] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:30:04.084 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:30:04.087 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:30:04.434 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:30:04.434 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3885dbbf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:30:04.434 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751851285717_127.0.0.1_10790
13:30:04.434 [nacos-grpc-client-executor-2960] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751851285717_127.0.0.1_10790]Ignore complete event,isRunning:false,isAbandon=false
13:30:04.444 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5eabda35[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2961]
13:30:04.670 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:30:04.674 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:30:04.683 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:30:04.685 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:30:04.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:30:04.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:30:04.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:30:04.695 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:21:11.890 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:15.431 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d1a31127-d25c-4346-a9d7-822aee05ead0_config-0
15:21:15.841 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 202 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:16.064 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 62 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:16.104 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:16.150 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:16.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:16.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:16.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:16.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021b113b8fc8
15:21:16.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000021b113b91e8
15:21:16.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:16.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:16.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:19.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751872878759_127.0.0.1_11784
15:21:19.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Notify connected event to listeners.
15:21:19.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:19.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1a31127-d25c-4346-a9d7-822aee05ead0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000021b114f0fb0
15:21:19.884 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:40.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:21:40.857 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:40.858 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:41.587 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:43.735 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:21:43.741 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:21:43.742 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:22:08.317 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:22:17.688 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 96f99cfc-b938-4934-8b94-dbc597332184
15:22:17.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] RpcClient init label, labels = {module=naming, source=sdk}
15:22:17.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:22:17.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:22:17.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:22:17.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:22:17.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Success to connect to server [localhost:8848] on start up, connectionId = 1751872937697_127.0.0.1_12147
15:22:17.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:22:17.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Notify connected event to listeners.
15:22:17.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000021b114f0fb0
15:22:17.921 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:22:17.981 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:22:18.374 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 68.954 seconds (JVM running for 72.624)
15:22:18.385 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Receive server push request, request = NotifySubscriberRequest, requestId = 20
15:22:18.400 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96f99cfc-b938-4934-8b94-dbc597332184] Ack server push request, request = NotifySubscriberRequest, requestId = 20
15:22:18.400 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:22:18.400 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:22:18.414 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:24:00.631 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:24:03.031 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:24:03.031 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
15:24:13.581 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:24:13.581 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:36:43.035 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:36:43.048 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:36:43.382 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:36:43.382 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1539dd86[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:36:43.382 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751872937697_127.0.0.1_12147
15:36:43.383 [nacos-grpc-client-executor-183] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751872937697_127.0.0.1_12147]Ignore complete event,isRunning:false,isAbandon=false
15:36:43.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7f0ead5b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 184]
15:36:43.518 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:36:43.520 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:36:43.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:36:43.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:36:43.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:36:43.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:36:43.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:36:43.530 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:37:02.979 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:37:03.794 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1323d52-cf5b-415b-9861-3725cf3c5195_config-0
15:37:03.846 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
15:37:03.880 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
15:37:03.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
15:37:03.903 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:37:03.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:37:03.914 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 2 keys and 8 values 
15:37:03.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:37:03.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000130563bdd70
15:37:03.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000130563bdf90
15:37:03.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:37:03.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:37:03.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:04.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751873824476_127.0.0.1_14783
15:37:04.678 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Notify connected event to listeners.
15:37:04.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:04.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1323d52-cf5b-415b-9861-3725cf3c5195_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000130564f7b88
15:37:04.761 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:37:07.418 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:37:07.418 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:37:07.418 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:37:07.552 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:37:08.052 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:37:08.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:37:08.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:37:13.933 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:37:16.446 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 90ffb56d-a6d8-44d5-b329-ee43946decc5
15:37:16.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] RpcClient init label, labels = {module=naming, source=sdk}
15:37:16.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:37:16.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:37:16.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:37:16.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:16.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Success to connect to server [localhost:8848] on start up, connectionId = 1751873836459_127.0.0.1_14867
15:37:16.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:16.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Notify connected event to listeners.
15:37:16.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000130564f7b88
15:37:16.624 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:37:16.660 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:37:16.792 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.626 seconds (JVM running for 16.13)
15:37:16.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:37:16.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:37:16.822 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:37:17.104 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Receive server push request, request = NotifySubscriberRequest, requestId = 24
15:37:17.119 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90ffb56d-a6d8-44d5-b329-ee43946decc5] Ack server push request, request = NotifySubscriberRequest, requestId = 24
15:37:17.244 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:37:25.660 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:37:25.661 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:37:25.663 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:37:25.665 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
15:37:25.665 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:37:25.673 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:37:25.673 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:37:25.673 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:37:25.679 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:37:25.679 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:41:27.677 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:41:27.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:41:28.014 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:41:28.014 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69916f5a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:41:28.014 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751873836459_127.0.0.1_14867
15:41:28.016 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751873836459_127.0.0.1_14867]Ignore complete event,isRunning:false,isAbandon=false
15:41:28.020 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 61]
15:41:28.147 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:41:28.147 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
15:41:28.147 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
15:41:28.147 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:41:28.147 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:41:28.147 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:41:46.179 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:41:47.017 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0
15:41:47.083 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
15:41:47.112 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:41:47.118 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:41:47.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:41:47.132 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:41:47.142 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:41:47.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:41:47.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000244813bed38
15:41:47.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000244813bef58
15:41:47.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:41:47.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:41:47.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:41:47.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874107716_127.0.0.1_1754
15:41:47.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Notify connected event to listeners.
15:41:47.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:41:47.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4106cfde-6239-4192-9e6d-3f74c59ae7ab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000244814f8d48
15:41:48.005 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:41:50.907 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:41:50.907 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:41:50.907 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:41:51.054 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:41:51.609 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:41:51.610 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:41:51.610 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:41:57.504 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:42:00.123 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1
15:42:00.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] RpcClient init label, labels = {module=naming, source=sdk}
15:42:00.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:42:00.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:42:00.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:42:00.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:42:00.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Success to connect to server [localhost:8848] on start up, connectionId = 1751874120133_127.0.0.1_1802
15:42:00.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:42:00.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Notify connected event to listeners.
15:42:00.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000244814f8d48
15:42:00.287 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:42:00.315 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:42:00.445 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.021 seconds (JVM running for 16.503)
15:42:00.462 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:42:00.462 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:42:00.463 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:42:00.743 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:42:00.819 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Receive server push request, request = NotifySubscriberRequest, requestId = 28
15:42:00.836 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf4f2c45-3c5f-4e02-8ddc-5898f830b8e1] Ack server push request, request = NotifySubscriberRequest, requestId = 28
15:43:23.449 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:43:23.449 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:43:25.174 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:43:25.174 [http-nio-9600-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
15:43:55.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:43:55.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:43:55.995 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:43:55.996 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7afc7e4f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:43:55.996 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874120133_127.0.0.1_1802
15:43:55.999 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751874120133_127.0.0.1_1802]Ignore complete event,isRunning:false,isAbandon=false
15:43:56.000 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5743fdee[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 37]
15:43:56.135 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:43:56.137 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:43:56.142 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:43:56.143 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:43:56.146 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:43:56.146 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:43:56.147 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:43:56.147 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:47:10.489 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:47:38.629 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-3a18-459d-8445-3142c194ea7d_config-0
15:47:41.731 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 874 ms to scan 1 urls, producing 3 keys and 6 values 
15:47:42.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 168 ms to scan 1 urls, producing 4 keys and 9 values 
15:47:42.778 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 142 ms to scan 1 urls, producing 3 keys and 10 values 
15:47:42.931 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 99 ms to scan 1 urls, producing 1 keys and 5 values 
15:47:43.072 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 118 ms to scan 1 urls, producing 1 keys and 7 values 
15:47:43.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 193 ms to scan 1 urls, producing 2 keys and 8 values 
15:47:43.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:47:43.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c4573e2f18
15:47:43.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c4573e3138
15:47:43.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:47:43.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:47:43.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:00.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:03.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:05.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874485066_127.0.0.1_4512
15:48:05.649 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Notify connected event to listeners.
15:48:05.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:05.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3a18-459d-8445-3142c194ea7d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$474/0x000001c45752edf8
15:48:06.289 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:48:16.922 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:48:16.924 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:48:16.924 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:48:17.226 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:48:18.681 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:48:18.685 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:48:18.717 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:48:34.602 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:48:38.925 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 09c0d7f7-f372-4952-b90b-838e07a9778b
15:48:38.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] RpcClient init label, labels = {module=naming, source=sdk}
15:48:38.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:48:38.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:48:38.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:48:38.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:39.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Success to connect to server [localhost:8848] on start up, connectionId = 1751874518939_127.0.0.1_4851
15:48:39.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:39.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Notify connected event to listeners.
15:48:39.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$474/0x000001c45752edf8
15:48:39.153 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:48:39.215 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:48:39.546 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 106.385 seconds (JVM running for 131.156)
15:48:39.580 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:48:39.582 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:48:39.582 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:48:39.693 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Receive server push request, request = NotifySubscriberRequest, requestId = 38
15:48:39.714 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09c0d7f7-f372-4952-b90b-838e07a9778b] Ack server push request, request = NotifySubscriberRequest, requestId = 38
15:48:45.424 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:48:50.258 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:48:50.259 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:28:49.801 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:28:49.803 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:28:50.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:28:50.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4c19b815[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:28:50.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874518939_127.0.0.1_4851
16:28:50.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5d7531fc[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 491]
16:28:50.269 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:28:50.271 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:28:50.276 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:28:50.276 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:28:50.276 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:28:50.276 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:28:54.959 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:28:55.570 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8bf1b924-17df-4e2b-af50-36393255f5c5_config-0
16:28:55.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
16:28:55.684 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
16:28:55.697 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
16:28:55.706 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:28:55.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:28:55.728 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
16:28:55.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:28:55.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001918d39e480
16:28:55.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001918d39e6a0
16:28:55.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:28:55.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:28:55.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:28:56.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751876936348_127.0.0.1_12713
16:28:56.536 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Notify connected event to listeners.
16:28:56.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:28:56.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf1b924-17df-4e2b-af50-36393255f5c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001918d518228
16:28:56.638 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:28:59.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:28:59.160 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:28:59.160 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:28:59.282 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:28:59.748 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:28:59.749 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:28:59.750 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:29:05.624 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:29:07.887 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 71ccbb3d-c723-4e85-9a15-884cc9faa0ca
16:29:07.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] RpcClient init label, labels = {module=naming, source=sdk}
16:29:07.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:29:07.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:29:07.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:29:07.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:29:08.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Success to connect to server [localhost:8848] on start up, connectionId = 1751876947897_127.0.0.1_12776
16:29:08.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:29:08.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001918d518228
16:29:08.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Notify connected event to listeners.
16:29:08.042 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:29:08.065 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:29:08.159 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.749 seconds (JVM running for 14.792)
16:29:08.171 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:29:08.172 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:29:08.172 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:29:08.442 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:29:08.529 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Receive server push request, request = NotifySubscriberRequest, requestId = 42
16:29:08.544 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71ccbb3d-c723-4e85-9a15-884cc9faa0ca] Ack server push request, request = NotifySubscriberRequest, requestId = 42
16:29:18.359 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:29:18.359 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:39:05.243 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:39:05.246 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:39:05.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:05.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17afb3f7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:05.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751876947897_127.0.0.1_12776
16:39:05.562 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751876947897_127.0.0.1_12776]Ignore complete event,isRunning:false,isAbandon=false
16:39:05.564 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4802083b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 134]
16:39:05.692 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:39:05.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:39:05.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:39:05.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:39:05.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:39:05.692 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:39:10.945 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:39:11.541 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 033cc906-b85e-4f74-9573-a5f6141bc913_config-0
16:39:11.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
16:39:11.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:39:11.635 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
16:39:11.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:39:11.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:39:11.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:39:11.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:39:11.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001121739e480
16:39:11.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001121739e6a0
16:39:11.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:39:11.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:39:11.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:39:12.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751877552244_127.0.0.1_1422
16:39:12.429 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Notify connected event to listeners.
16:39:12.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:12.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000112175186d8
16:39:12.514 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:39:15.102 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:39:15.103 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:39:15.103 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:39:15.225 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:39:15.711 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:39:15.712 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:39:15.712 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:39:21.196 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:39:23.735 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f
16:39:23.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] RpcClient init label, labels = {module=naming, source=sdk}
16:39:23.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:23.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:23.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:23.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:39:23.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Success to connect to server [localhost:8848] on start up, connectionId = 1751877563749_127.0.0.1_1465
16:39:23.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:23.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000112175186d8
16:39:23.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Notify connected event to listeners.
16:39:23.913 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:39:23.936 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:39:24.062 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.72 seconds (JVM running for 14.816)
16:39:24.077 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:39:24.078 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:39:24.078 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:39:24.334 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:39:24.420 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Receive server push request, request = NotifySubscriberRequest, requestId = 49
16:39:24.461 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Ack server push request, request = NotifySubscriberRequest, requestId = 49
16:40:23.346 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:40:23.346 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:18:31.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Server healthy check fail, currentConnection = 1751877552244_127.0.0.1_1422
17:18:31.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Server healthy check fail, currentConnection = 1751877563749_127.0.0.1_1465
17:18:31.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:18:31.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Success to connect a server [localhost:8848], connectionId = 1751879985401_127.0.0.1_9094
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Success to connect a server [localhost:8848], connectionId = 1751879985396_127.0.0.1_9095
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Abandon prev connection, server is localhost:8848, connectionId is 1751877563749_127.0.0.1_1465
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751877552244_127.0.0.1_1422
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751877563749_127.0.0.1_1465
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751877552244_127.0.0.1_1422
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Notify disconnected event to listeners
17:19:49.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Notify disconnected event to listeners
17:19:49.136 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [033cc906-b85e-4f74-9573-a5f6141bc913_config-0] Notify connected event to listeners.
17:19:49.138 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Server check success, currentServer is localhost:8848 
17:19:54.519 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Notify connected event to listeners.
17:20:00.912 [nacos-grpc-client-executor-367] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Receive server push request, request = NotifySubscriberRequest, requestId = 54
17:20:00.912 [nacos-grpc-client-executor-367] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02bf99a0-a5f5-4154-8e6f-149b3e4f7f7f] Ack server push request, request = NotifySubscriberRequest, requestId = 54
19:02:19.610 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:19.610 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:19.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:19.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3670ceb0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:19.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751879985401_127.0.0.1_9094
19:02:19.964 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4294641f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1665]
19:02:20.225 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:02:20.235 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:02:20.257 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:02:20.257 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:02:20.260 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:02:20.260 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
