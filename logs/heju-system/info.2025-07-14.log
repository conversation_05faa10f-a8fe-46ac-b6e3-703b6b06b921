09:36:57.727 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:59.022 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0
09:36:59.142 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:36:59.205 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:36:59.217 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:36:59.229 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:36:59.243 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:36:59.257 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:36:59.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:59.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002024e3cf1c0
09:36:59.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002024e3cf3e0
09:36:59.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:59.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:59.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:37:01.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457020591_127.0.0.1_3222
09:37:01.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Notify connected event to listeners.
09:37:01.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:01.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27c15b76-5ebd-4ec1-abf2-c2cad13d33ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002024e508ad8
09:37:01.634 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:37:14.205 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:37:14.206 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:37:14.206 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:37:14.449 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:37:15.341 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:37:15.343 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:37:15.344 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:37:25.124 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:37:28.949 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c7696373-04b1-40a4-bd44-b3f813c7c5ce
09:37:28.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] RpcClient init label, labels = {module=naming, source=sdk}
09:37:28.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:37:28.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:37:28.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:37:28.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:37:29.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Success to connect to server [localhost:8848] on start up, connectionId = 1752457048960_127.0.0.1_3332
09:37:29.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Notify connected event to listeners.
09:37:29.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:29.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002024e508ad8
09:37:29.149 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:37:29.196 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:37:29.382 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.568 seconds (JVM running for 34.168)
09:37:29.401 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:37:29.402 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:37:29.402 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:37:29.639 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:37:29.664 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7696373-04b1-40a4-bd44-b3f813c7c5ce] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:37:29.886 [RMI TCP Connection(19)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:42:21.170 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:42:21.171 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:42:41.689 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:42:41.690 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:44:14.766 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:44:14.769 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:44:15.093 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:44:15.094 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7400a0e9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:44:15.094 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752457048960_127.0.0.1_3332
09:44:15.096 [nacos-grpc-client-executor-96] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752457048960_127.0.0.1_3332]Ignore complete event,isRunning:false,isAbandon=false
09:44:15.117 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@14ed5515[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 97]
09:44:15.273 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:44:15.275 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:44:15.285 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:44:15.286 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:44:15.287 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:44:15.288 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:44:15.290 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:44:15.290 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:50:16.698 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:50:17.664 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0
09:50:17.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:50:17.786 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:50:17.795 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:50:17.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:50:17.817 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:50:17.832 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:50:17.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:50:17.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b4b739e480
09:50:17.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b4b739e6a0
09:50:17.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:50:17.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:50:17.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:19.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457818858_127.0.0.1_5666
09:50:19.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Notify connected event to listeners.
09:50:19.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:19.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45bef0a7-f6dc-43d8-bd90-005a2a5648f5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b4b7518228
09:50:19.418 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:50:25.946 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:50:25.947 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:50:25.948 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:50:26.225 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:50:27.454 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:50:27.456 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:50:27.457 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:50:44.409 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:50:48.107 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a7100bc1-2d39-4050-8fce-b2a372de5051
09:50:48.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] RpcClient init label, labels = {module=naming, source=sdk}
09:50:48.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:50:48.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:50:48.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:50:48.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:48.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Success to connect to server [localhost:8848] on start up, connectionId = 1752457848118_127.0.0.1_5844
09:50:48.231 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Notify connected event to listeners.
09:50:48.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:48.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b4b7518228
09:50:48.315 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:50:48.368 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:50:48.542 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.655 seconds (JVM running for 34.491)
09:50:48.562 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:50:48.564 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:50:48.564 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:50:48.873 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:50:48.897 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7100bc1-2d39-4050-8fce-b2a372de5051] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:50:49.086 [RMI TCP Connection(27)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:50:59.681 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:50:59.683 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:53:20.137 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:53:20.138 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:43:56.014 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:43:56.028 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:43:56.356 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:43:56.357 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@56aa97e2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:43:56.357 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752457848118_127.0.0.1_5844
10:43:56.360 [nacos-grpc-client-executor-687] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752457848118_127.0.0.1_5844]Ignore complete event,isRunning:false,isAbandon=false
10:43:56.363 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@64a8f090[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 688]
10:43:56.519 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:43:56.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:43:56.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:43:56.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:43:56.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:43:56.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:43:56.545 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:43:56.545 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:44:02.899 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:44:03.678 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fdb4779a-b864-475b-bc50-187cf9c905af_config-0
10:44:03.765 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
10:44:03.812 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
10:44:03.821 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:44:03.834 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
10:44:03.844 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:44:03.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
10:44:03.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:44:03.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018bb639f1c0
10:44:03.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000018bb639f3e0
10:44:03.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:44:03.867 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:44:03.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:44:04.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752461044737_127.0.0.1_11794
10:44:04.971 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Notify connected event to listeners.
10:44:04.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:44:04.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fdb4779a-b864-475b-bc50-187cf9c905af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018bb6518fb0
10:44:05.140 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:44:09.223 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:44:09.224 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:44:09.225 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:44:09.477 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:44:10.279 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:44:10.282 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:44:10.282 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:44:18.575 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:44:21.626 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc12bf66-7344-4e91-83a8-8085062d8bde
10:44:21.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] RpcClient init label, labels = {module=naming, source=sdk}
10:44:21.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:44:21.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:44:21.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:44:21.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:44:21.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Success to connect to server [localhost:8848] on start up, connectionId = 1752461061638_127.0.0.1_11821
10:44:21.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Notify connected event to listeners.
10:44:21.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:44:21.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018bb6518fb0
10:44:21.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:44:21.884 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
10:44:22.040 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.748 seconds (JVM running for 20.787)
10:44:22.059 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:44:22.062 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:44:22.062 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:44:22.288 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Receive server push request, request = NotifySubscriberRequest, requestId = 27
10:44:22.309 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc12bf66-7344-4e91-83a8-8085062d8bde] Ack server push request, request = NotifySubscriberRequest, requestId = 27
10:44:22.467 [RMI TCP Connection(2)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:44:47.027 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:44:47.027 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:59:45.344 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:59:45.360 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:59:45.713 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:59:45.713 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2bae4b8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:59:45.718 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752461061638_127.0.0.1_11821
10:59:45.718 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@19d4a92c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 196]
10:59:45.896 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:59:45.912 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:59:45.914 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:59:45.925 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:59:45.927 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:59:45.927 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:59:52.467 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:59:53.247 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0
10:59:53.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
10:59:53.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:59:53.390 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:59:53.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
10:59:53.411 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:59:53.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
10:59:53.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:59:53.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002949139dd70
10:59:53.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002949139df90
10:59:53.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:59:53.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:59:53.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:59:54.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752461994273_127.0.0.1_13095
10:59:54.499 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Notify connected event to listeners.
10:59:54.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:59:54.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd795ebf-f5e9-43c6-ad68-c5d14f9157bb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029491518228
10:59:54.685 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:59:58.519 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:59:58.520 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:59:58.520 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:59:58.667 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:59:59.903 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:59:59.906 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:59:59.906 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:00:08.076 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:00:11.218 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0c5637c9-50f3-4880-b29b-62634366c1a3
11:00:11.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] RpcClient init label, labels = {module=naming, source=sdk}
11:00:11.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:00:11.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:00:11.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:00:11.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:00:11.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Success to connect to server [localhost:8848] on start up, connectionId = 1752462011231_127.0.0.1_13109
11:00:11.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Notify connected event to listeners.
11:00:11.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:00:11.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029491518228
11:00:11.448 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:00:11.498 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
11:00:11.645 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.825 seconds (JVM running for 21.023)
11:00:11.673 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:00:11.675 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:00:11.676 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:00:12.008 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Receive server push request, request = NotifySubscriberRequest, requestId = 34
11:00:12.031 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c5637c9-50f3-4880-b29b-62634366c1a3] Ack server push request, request = NotifySubscriberRequest, requestId = 34
11:00:12.112 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:00:29.178 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:00:29.179 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:10:39.110 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:10:39.113 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:10:39.459 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:10:39.459 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48760a3d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:10:39.459 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752462011231_127.0.0.1_13109
11:10:39.459 [nacos-grpc-client-executor-137] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752462011231_127.0.0.1_13109]Ignore complete event,isRunning:false,isAbandon=false
11:10:39.472 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 138]
11:10:39.639 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:10:39.655 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:10:39.672 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:10:39.672 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:10:39.672 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:10:39.672 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:10:45.119 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:10:45.904 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0
11:10:45.986 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
11:10:46.030 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:10:46.041 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:10:46.049 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:10:46.059 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
11:10:46.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:10:46.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:10:46.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002145339e8d8
11:10:46.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002145339eaf8
11:10:46.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:10:46.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:10:46.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:10:47.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752462646926_127.0.0.1_14362
11:10:47.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Notify connected event to listeners.
11:10:47.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:10:47.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3d03ccd-ec12-4a9a-ada5-cd674560959e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021453518668
11:10:47.314 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:10:51.263 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:10:51.264 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:10:51.264 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:10:51.452 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:10:52.226 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:10:52.229 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:10:52.229 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:11:00.092 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:11:03.293 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6e6d3014-38b8-4bfa-9c42-cdd86c712502
11:11:03.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] RpcClient init label, labels = {module=naming, source=sdk}
11:11:03.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:11:03.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:11:03.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:11:03.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:11:03.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Success to connect to server [localhost:8848] on start up, connectionId = 1752462663316_127.0.0.1_14385
11:11:03.440 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Notify connected event to listeners.
11:11:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:11:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021453518668
11:11:03.540 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:11:03.578 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
11:11:03.702 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.222 seconds (JVM running for 20.222)
11:11:03.715 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:11:03.715 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:11:03.716 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:11:03.993 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:11:04.011 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e6d3014-38b8-4bfa-9c42-cdd86c712502] Ack server push request, request = NotifySubscriberRequest, requestId = 37
11:11:04.346 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:11:06.887 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:11:06.888 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:50:07.374 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:50:07.378 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:50:07.711 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:50:07.711 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a965e4e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:50:07.711 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752462663316_127.0.0.1_14385
13:50:07.711 [nacos-grpc-client-executor-1526] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752462663316_127.0.0.1_14385]Ignore complete event,isRunning:false,isAbandon=false
13:50:07.711 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@62b1adb[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1527]
13:50:07.883 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:50:07.883 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:50:07.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:50:07.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:50:07.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:50:07.892 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:50:33.084 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:50:33.658 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a937aeb6-d2eb-4373-9580-20c1f259921c_config-0
13:50:33.706 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
13:50:33.742 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
13:50:33.750 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
13:50:33.756 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
13:50:33.761 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 7 values 
13:50:33.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:50:33.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:50:33.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000185e03b6af8
13:50:33.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000185e03b6d18
13:50:33.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:50:33.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:50:33.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:50:34.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752472234306_127.0.0.1_12293
13:50:34.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Notify connected event to listeners.
13:50:34.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:50:34.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000185e04f0ad8
13:50:34.609 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:50:37.139 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:50:37.139 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:50:37.139 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:50:37.262 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:50:37.760 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:50:37.769 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:50:37.769 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:50:42.475 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:50:44.460 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bd62154f-ba39-42c4-8842-cbedce72020f
13:50:44.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] RpcClient init label, labels = {module=naming, source=sdk}
13:50:44.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:50:44.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:50:44.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:50:44.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:50:44.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Success to connect to server [localhost:8848] on start up, connectionId = 1752472244468_127.0.0.1_12301
13:50:44.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:50:44.593 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Notify connected event to listeners.
13:50:44.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000185e04f0ad8
13:50:44.628 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:50:44.650 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:50:44.736 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.092 seconds (JVM running for 13.869)
13:50:44.747 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:50:44.747 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:50:44.747 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:50:45.123 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 43
13:50:45.128 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 43
13:50:47.276 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:50:48.111 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:50:48.111 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:11:30.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Server healthy check fail, currentConnection = 1752472234306_127.0.0.1_12293
14:11:30.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Server healthy check fail, currentConnection = 1752472244468_127.0.0.1_12301
14:11:30.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:11:30.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:11:31.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Success to connect a server [localhost:8848], connectionId = 1752473490922_127.0.0.1_13966
14:11:31.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Success to connect a server [localhost:8848], connectionId = 1752473490922_127.0.0.1_13967
14:11:31.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Abandon prev connection, server is localhost:8848, connectionId is 1752472244468_127.0.0.1_12301
14:11:31.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752472234306_127.0.0.1_12293
14:11:31.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752472244468_127.0.0.1_12301
14:11:31.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752472234306_127.0.0.1_12293
14:11:31.046 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752472234306_127.0.0.1_12293]Ignore complete event,isRunning:false,isAbandon=true
14:11:31.046 [nacos-grpc-client-executor-254] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752472244468_127.0.0.1_12301]Ignore complete event,isRunning:false,isAbandon=true
14:11:31.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Notify disconnected event to listeners
14:11:31.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Notify disconnected event to listeners
14:11:31.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a937aeb6-d2eb-4373-9580-20c1f259921c_config-0] Notify connected event to listeners.
14:11:31.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Notify connected event to listeners.
14:11:43.586 [nacos-grpc-client-executor-258] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:11:43.586 [nacos-grpc-client-executor-258] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:11:55.094 [nacos-grpc-client-executor-259] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 51
14:11:55.094 [nacos-grpc-client-executor-259] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 51
14:15:25.810 [nacos-grpc-client-executor-262] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 52
14:15:25.830 [nacos-grpc-client-executor-262] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 52
14:15:25.839 [nacos-grpc-client-executor-263] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:15:25.846 [nacos-grpc-client-executor-263] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 53
14:15:25.855 [nacos-grpc-client-executor-264] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 54
14:15:25.858 [nacos-grpc-client-executor-264] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 54
14:15:25.858 [nacos-grpc-client-executor-265] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 55
14:15:25.858 [nacos-grpc-client-executor-265] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 55
14:15:25.880 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 56
14:15:25.880 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 56
14:15:25.880 [nacos-grpc-client-executor-267] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 57
14:15:25.880 [nacos-grpc-client-executor-267] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 57
14:15:25.896 [nacos-grpc-client-executor-268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 58
14:15:25.896 [nacos-grpc-client-executor-268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 58
14:15:25.896 [nacos-grpc-client-executor-269] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 59
14:15:25.896 [nacos-grpc-client-executor-269] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 59
14:15:25.896 [nacos-grpc-client-executor-270] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 60
14:15:25.896 [nacos-grpc-client-executor-270] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 60
14:15:25.917 [nacos-grpc-client-executor-271] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 61
14:15:25.917 [nacos-grpc-client-executor-271] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 61
14:15:25.929 [nacos-grpc-client-executor-272] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 62
14:15:25.929 [nacos-grpc-client-executor-272] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 62
14:15:25.937 [nacos-grpc-client-executor-273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 63
14:15:25.937 [nacos-grpc-client-executor-273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 63
14:15:25.948 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 64
14:15:25.949 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 64
14:15:25.952 [nacos-grpc-client-executor-275] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 65
14:15:25.952 [nacos-grpc-client-executor-275] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 65
14:15:25.971 [nacos-grpc-client-executor-276] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 66
14:15:25.971 [nacos-grpc-client-executor-276] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 66
14:15:25.979 [nacos-grpc-client-executor-277] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 67
14:15:25.979 [nacos-grpc-client-executor-277] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 67
14:15:25.985 [nacos-grpc-client-executor-278] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 68
14:15:25.985 [nacos-grpc-client-executor-278] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 68
14:15:25.993 [nacos-grpc-client-executor-279] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 69
14:15:25.994 [nacos-grpc-client-executor-279] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 69
14:15:25.995 [nacos-grpc-client-executor-280] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 70
14:15:25.995 [nacos-grpc-client-executor-280] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 70
14:15:26.008 [nacos-grpc-client-executor-281] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 71
14:15:26.009 [nacos-grpc-client-executor-281] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 71
14:15:26.016 [nacos-grpc-client-executor-282] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 72
14:15:26.016 [nacos-grpc-client-executor-282] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 72
14:15:26.026 [nacos-grpc-client-executor-283] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 73
14:15:26.026 [nacos-grpc-client-executor-283] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 73
14:15:26.030 [nacos-grpc-client-executor-284] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 74
14:15:26.030 [nacos-grpc-client-executor-284] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 74
14:15:26.032 [nacos-grpc-client-executor-285] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 75
14:15:26.032 [nacos-grpc-client-executor-285] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 75
14:15:26.032 [nacos-grpc-client-executor-286] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 76
14:15:26.032 [nacos-grpc-client-executor-286] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 76
14:15:26.037 [nacos-grpc-client-executor-287] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 77
14:15:26.037 [nacos-grpc-client-executor-287] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 77
14:15:26.037 [nacos-grpc-client-executor-288] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 78
14:15:26.037 [nacos-grpc-client-executor-288] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 78
14:15:26.037 [nacos-grpc-client-executor-289] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 79
14:15:26.037 [nacos-grpc-client-executor-289] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 79
14:15:26.037 [nacos-grpc-client-executor-290] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 80
14:15:26.037 [nacos-grpc-client-executor-290] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 80
14:15:26.045 [nacos-grpc-client-executor-291] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 81
14:15:26.045 [nacos-grpc-client-executor-291] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 81
14:15:26.046 [nacos-grpc-client-executor-292] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 82
14:15:26.046 [nacos-grpc-client-executor-292] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 82
14:15:26.048 [nacos-grpc-client-executor-293] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 83
14:15:26.048 [nacos-grpc-client-executor-293] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 83
14:15:26.048 [nacos-grpc-client-executor-294] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 84
14:15:26.048 [nacos-grpc-client-executor-294] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 84
14:15:26.048 [nacos-grpc-client-executor-295] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 85
14:15:26.048 [nacos-grpc-client-executor-295] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 85
14:15:26.053 [nacos-grpc-client-executor-296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 86
14:15:26.053 [nacos-grpc-client-executor-296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 86
14:15:26.441 [nacos-grpc-client-executor-297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Receive server push request, request = NotifySubscriberRequest, requestId = 87
14:15:26.441 [nacos-grpc-client-executor-297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd62154f-ba39-42c4-8842-cbedce72020f] Ack server push request, request = NotifySubscriberRequest, requestId = 87
14:55:28.473 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:55:28.477 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:55:28.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:55:28.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@73e9f415[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:55:28.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752473490922_127.0.0.1_13967
14:55:28.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3deef5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 779]
14:55:28.985 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:55:28.987 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:55:28.993 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:55:28.995 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:55:28.996 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:55:28.996 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:59:38.700 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:59:39.215 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 848217e1-a221-4991-91f4-049098e8ab07_config-0
14:59:39.267 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
14:59:39.293 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:59:39.298 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:59:39.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:59:39.310 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:59:39.315 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:59:39.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:59:39.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000182d23cda58
14:59:39.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000182d23cdc78
14:59:39.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:59:39.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:59:39.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:59:39.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752476379794_127.0.0.1_4465
14:59:39.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Notify connected event to listeners.
14:59:39.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:59:39.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848217e1-a221-4991-91f4-049098e8ab07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000182d2507cb0
14:59:40.057 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:59:42.361 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:59:42.361 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:59:42.362 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:59:42.465 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:59:42.983 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:59:42.984 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:59:42.984 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:59:48.249 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:59:50.474 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4222bff1-5a7f-490c-8e1e-8372aea17bc0
14:59:50.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] RpcClient init label, labels = {module=naming, source=sdk}
14:59:50.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:59:50.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:59:50.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:59:50.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:59:50.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Success to connect to server [localhost:8848] on start up, connectionId = 1752476390486_127.0.0.1_4484
14:59:50.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:59:50.607 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Notify connected event to listeners.
14:59:50.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000182d2507cb0
14:59:50.649 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:59:50.671 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:59:50.796 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.556 seconds (JVM running for 13.494)
14:59:50.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:59:50.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:59:50.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:59:51.139 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Receive server push request, request = NotifySubscriberRequest, requestId = 90
14:59:51.154 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4222bff1-5a7f-490c-8e1e-8372aea17bc0] Ack server push request, request = NotifySubscriberRequest, requestId = 90
14:59:51.410 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:02:02.809 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:02:02.809 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:09:27.550 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:09:27.556 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:09:27.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:09:27.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@677faf72[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:09:27.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752476390486_127.0.0.1_4484
18:09:27.875 [nacos-grpc-client-executor-2405] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752476390486_127.0.0.1_4484]Ignore complete event,isRunning:false,isAbandon=false
18:09:27.881 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@21c83278[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2406]
18:09:28.070 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:09:28.075 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:09:28.094 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:09:28.094 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:09:28.096 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:09:28.098 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
