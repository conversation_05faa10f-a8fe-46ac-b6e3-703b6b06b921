09:18:51.601 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:52.537 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0
09:18:52.650 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 40 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:52.721 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:52.735 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:52.746 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:52.758 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:52.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:52.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:52.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023de53e8b08
09:18:52.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023de53e8d28
09:18:52.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:52.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:52.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:53.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753147133664_127.0.0.1_13514
09:18:53.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Notify connected event to listeners.
09:18:53.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:53.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023de5560ad8
09:18:54.121 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:59.115 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:18:59.115 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:59.116 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:59.370 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:00.391 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:19:00.393 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:19:00.394 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:19:10.680 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:13.565 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4af75df6-aff8-4d89-823d-72e0621ed24a
09:19:13.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] RpcClient init label, labels = {module=naming, source=sdk}
09:19:13.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:13.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:13.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:13.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:13.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Success to connect to server [localhost:8848] on start up, connectionId = 1753147153580_127.0.0.1_9220
09:19:13.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Notify connected event to listeners.
09:19:13.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:13.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023de5560ad8
09:19:13.789 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:19:13.832 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:19:13.971 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.443 seconds (JVM running for 25.001)
09:19:13.985 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:19:13.986 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:19:13.986 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:19:14.281 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:19:14.303 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:19:14.543 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:23:50.587 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:23:50.587 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:23:51.202 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:23:51.203 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:23:51.398 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:23:51.401 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
20:39:57.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:39:57.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:39:57.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.707 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.708 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.023 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.048 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.455 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.966 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.970 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.581 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.607 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:00.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:00.355 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:01.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:01.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:02.047 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:02.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:03.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:03.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:04.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:04.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:05.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:05.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:06.719 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:06.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:08.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:08.181 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb16a369-39e9-4557-a9dc-fe7d5aac06f4_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.940 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:40:10.258 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:40:10.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:40:10.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@59d4f5e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:40:10.587 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4af75df6-aff8-4d89-823d-72e0621ed24a] Client is shutdown, stop reconnect to server
20:40:10.587 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753147153580_127.0.0.1_9220
20:40:10.587 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40939fd4[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 8209]
20:40:10.753 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:40:10.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:40:10.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:40:10.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:40:10.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:40:10.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:40:10.763 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:40:10.764 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
