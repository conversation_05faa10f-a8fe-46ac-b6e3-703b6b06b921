09:12:16.610 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:17.942 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2053f320-ee31-4237-a613-7a773d669c4a_config-0
09:12:18.061 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:18.125 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:18.138 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:18.152 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:18.164 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:18.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:18.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:18.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002208a3bef80
09:12:18.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002208a3bf1a0
09:12:18.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:18.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:18.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:19.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752541939396_127.0.0.1_5618
09:12:19.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Notify connected event to listeners.
09:12:19.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:19.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2053f320-ee31-4237-a613-7a773d669c4a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002208a4f8fb0
09:12:19.999 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:31.110 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:12:31.112 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:31.112 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:31.803 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:33.543 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:12:33.546 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:12:33.547 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:12:46.274 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:49.528 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aadd58e7-4004-48de-aa9c-ccf6fcee28df
09:12:49.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] RpcClient init label, labels = {module=naming, source=sdk}
09:12:49.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:49.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:49.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:49.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:49.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Success to connect to server [localhost:8848] on start up, connectionId = 1752541969542_127.0.0.1_5812
09:12:49.663 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Notify connected event to listeners.
09:12:49.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:49.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002208a4f8fb0
09:12:49.818 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:12:49.863 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:12:50.009 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.432 seconds (JVM running for 37.832)
09:12:50.030 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:12:50.032 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:12:50.033 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:12:50.306 [RMI TCP Connection(23)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:12:50.311 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:12:50.335 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:13:53.095 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:13:53.096 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aadd58e7-4004-48de-aa9c-ccf6fcee28df] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:13:53.334 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:13:53.335 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:13:53.512 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:13:53.513 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:24:20.520 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:24:20.529 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:24:20.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:24:20.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d88635e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:24:20.868 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752541969542_127.0.0.1_5812
11:24:20.870 [nacos-grpc-client-executor-1661] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752541969542_127.0.0.1_5812]Ignore complete event,isRunning:false,isAbandon=false
11:24:20.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@68942f16[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1662]
11:24:21.059 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:24:21.067 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:24:21.071 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:24:21.071 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:24:21.077 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:24:21.077 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:24:21.081 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:24:21.081 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:24:44.243 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:24:45.147 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3240a8b0-ff61-4b19-88e4-4539d2005810_config-0
11:24:45.251 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
11:24:45.344 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
11:24:45.358 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
11:24:45.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:24:45.390 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:24:45.406 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
11:24:45.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:24:45.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002721339dd00
11:24:45.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002721339df20
11:24:45.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:24:45.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:24:45.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:46.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752549886261_127.0.0.1_9530
11:24:46.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Notify connected event to listeners.
11:24:46.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:46.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3240a8b0-ff61-4b19-88e4-4539d2005810_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027213517b88
11:24:46.621 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:24:51.285 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:24:51.287 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:24:51.287 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:24:51.665 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:24:53.080 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:24:53.084 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:24:53.084 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:25:10.559 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:25:17.505 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 360c5a31-5c63-4c03-9e19-87c1b790c5d4
11:25:17.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] RpcClient init label, labels = {module=naming, source=sdk}
11:25:17.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:25:17.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:25:17.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:25:17.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:25:17.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Success to connect to server [localhost:8848] on start up, connectionId = 1752549917532_127.0.0.1_9659
11:25:17.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:25:17.657 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Notify connected event to listeners.
11:25:17.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027213517b88
11:25:17.762 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:25:17.824 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
11:25:18.163 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.799 seconds (JVM running for 36.106)
11:25:18.198 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:25:18.198 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:25:18.201 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:25:18.307 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:25:18.337 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [360c5a31-5c63-4c03-9e19-87c1b790c5d4] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:25:18.391 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:26:09.982 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:26:09.982 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:03:14.598 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:03:14.605 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:03:14.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:03:14.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5ac5e85b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:03:14.964 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752549917532_127.0.0.1_9659
14:03:14.964 [nacos-grpc-client-executor-2029] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752549917532_127.0.0.1_9659]Ignore complete event,isRunning:false,isAbandon=false
14:03:14.968 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@66b3a1ca[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2030]
14:03:15.114 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:03:15.114 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:03:15.129 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:03:15.129 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:03:15.130 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:03:15.130 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:03:38.196 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:03:38.761 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0
14:03:38.817 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
14:03:38.847 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
14:03:38.854 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:03:38.861 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:03:38.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 7 values 
14:03:38.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
14:03:38.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:03:38.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001abc739bda8
14:03:38.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001abc739bfc8
14:03:38.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:03:38.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:03:38.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:03:39.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752559419389_127.0.0.1_11659
14:03:39.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Notify connected event to listeners.
14:03:39.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:03:39.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ea1c1e2-e3ca-4109-9469-4cd2db010a9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001abc7513ff8
14:03:39.675 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:03:42.263 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:03:42.263 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:03:42.263 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:03:42.384 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:03:43.502 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:03:43.502 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:03:43.502 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:03:48.889 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:03:51.976 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 776e7d86-c952-468d-8456-35bafe61f249
14:03:51.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] RpcClient init label, labels = {module=naming, source=sdk}
14:03:51.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:03:51.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:03:51.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:03:51.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:03:52.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Success to connect to server [localhost:8848] on start up, connectionId = 1752559431982_127.0.0.1_11696
14:03:52.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Notify connected event to listeners.
14:03:52.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:03:52.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001abc7513ff8
14:03:52.162 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:03:52.189 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:03:52.303 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.556 seconds (JVM running for 35.459)
14:03:52.313 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:03:52.313 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:03:52.313 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:03:52.717 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Receive server push request, request = NotifySubscriberRequest, requestId = 28
14:03:52.742 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [776e7d86-c952-468d-8456-35bafe61f249] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:06:14.626 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:06:16.568 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:06:16.569 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:29:28.218 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:29:28.220 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:29:28.542 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:29:28.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5b400581[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:29:28.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752559431982_127.0.0.1_11696
14:29:28.547 [nacos-grpc-client-executor-316] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752559431982_127.0.0.1_11696]Ignore complete event,isRunning:false,isAbandon=false
14:29:28.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6033e2ae[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 317]
14:29:28.740 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:29:28.746 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:29:28.766 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:29:28.766 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:29:28.766 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:29:28.766 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:29:32.209 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:29:33.044 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5beb2860-2d9c-460d-895e-b91077707954_config-0
14:29:33.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
14:29:33.163 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:29:33.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:29:33.183 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:29:33.193 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:29:33.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:29:33.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:29:33.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c73b39ed38
14:29:33.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c73b39ef58
14:29:33.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:29:33.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:29:33.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:29:34.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752560973986_127.0.0.1_14937
14:29:34.216 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Notify connected event to listeners.
14:29:34.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:29:34.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5beb2860-2d9c-460d-895e-b91077707954_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c73b518668
14:29:34.381 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:29:38.678 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:29:38.679 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:29:38.679 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:29:38.872 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:29:39.758 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:29:39.760 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:29:39.761 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:29:48.691 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:29:51.910 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93b4492f-21f4-46ef-8088-16a0c44dd202
14:29:51.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] RpcClient init label, labels = {module=naming, source=sdk}
14:29:51.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:29:51.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:29:51.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:29:51.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:29:52.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Success to connect to server [localhost:8848] on start up, connectionId = 1752560991923_127.0.0.1_14968
14:29:52.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:29:52.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Notify connected event to listeners.
14:29:52.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c73b518668
14:29:52.122 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:29:52.164 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:29:52.336 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.748 seconds (JVM running for 21.881)
14:29:52.355 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:29:52.356 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:29:52.356 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:29:52.639 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:29:52.663 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93b4492f-21f4-46ef-8088-16a0c44dd202] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:29:52.917 [RMI TCP Connection(2)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:30:13.629 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:30:13.629 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:43:52.670 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:43:52.675 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:43:52.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:43:52.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67ad09f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:43:52.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752560991923_127.0.0.1_14968
14:43:52.997 [nacos-grpc-client-executor-174] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752560991923_127.0.0.1_14968]Ignore complete event,isRunning:false,isAbandon=false
14:43:53.003 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40619b48[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 175]
14:43:53.160 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:43:53.162 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:43:53.166 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:43:53.166 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:43:53.166 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:43:53.166 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:43:58.585 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:43:59.421 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0
14:43:59.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
14:43:59.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:43:59.560 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:43:59.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:43:59.579 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:43:59.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:43:59.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:43:59.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001396839dd00
14:43:59.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001396839df20
14:43:59.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:43:59.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:43:59.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:44:00.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752561840442_127.0.0.1_3337
14:44:00.658 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Notify connected event to listeners.
14:44:00.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:44:00.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02509cf5-67bf-44f4-8e34-bb0be6383dce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013968517b88
14:44:00.879 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:44:04.760 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:44:04.761 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:44:04.761 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:44:04.956 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:44:05.831 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:44:05.833 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:44:05.834 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:44:18.631 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:44:25.447 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ed81656-b451-493a-9872-fb83965ae485
14:44:25.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] RpcClient init label, labels = {module=naming, source=sdk}
14:44:25.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:44:25.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:44:25.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:44:25.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:44:25.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Success to connect to server [localhost:8848] on start up, connectionId = 1752561865468_127.0.0.1_3426
14:44:25.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:44:25.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013968517b88
14:44:25.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Notify connected event to listeners.
14:44:25.697 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:44:25.763 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:44:26.112 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.122 seconds (JVM running for 29.129)
14:44:26.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:44:26.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:44:26.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:44:26.223 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:44:26.244 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ed81656-b451-493a-9872-fb83965ae485] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:44:26.483 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:44:36.710 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:44:36.712 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:47:03.445 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:47:03.449 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:47:03.753 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:47:03.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d37f515[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:47:03.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752561865468_127.0.0.1_3426
14:47:03.760 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752561865468_127.0.0.1_3426]Ignore complete event,isRunning:false,isAbandon=false
14:47:03.760 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@41bbaef2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 41]
14:47:03.918 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:47:03.923 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:47:03.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:47:03.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:47:03.939 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:47:03.939 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:47:11.037 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:47:11.884 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c2cec138-ea23-49f7-9f60-20836b197b43_config-0
14:47:11.973 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
14:47:12.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:47:12.033 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:47:12.043 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:47:12.054 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:47:12.073 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
14:47:12.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:47:12.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bfba39f410
14:47:12.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001bfba39f630
14:47:12.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:47:12.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:47:12.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:47:13.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752562032950_127.0.0.1_3845
14:47:13.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Notify connected event to listeners.
14:47:13.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:47:13.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2cec138-ea23-49f7-9f60-20836b197b43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bfba5194a8
14:47:13.402 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:47:17.343 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:47:17.343 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:47:17.343 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:47:17.583 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:47:18.339 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:47:18.343 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:47:18.343 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:47:25.925 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:47:28.980 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2eff2d23-387d-4987-b7b5-50c156930bcd
14:47:28.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] RpcClient init label, labels = {module=naming, source=sdk}
14:47:28.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:47:28.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:47:28.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:47:28.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:47:29.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Success to connect to server [localhost:8848] on start up, connectionId = 1752562048996_127.0.0.1_3892
14:47:29.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:47:29.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Notify connected event to listeners.
14:47:29.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bfba5194a8
14:47:29.234 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:47:29.274 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:47:29.445 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.98 seconds (JVM running for 20.041)
14:47:29.460 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:47:29.461 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:47:29.461 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:47:29.791 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:47:29.811 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2eff2d23-387d-4987-b7b5-50c156930bcd] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:47:30.019 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:47:36.993 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:47:36.994 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:51:53.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:51:53.235 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:51:53.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:51:53.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5fbc63db[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:51:53.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752562048996_127.0.0.1_3892
14:51:53.582 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752562048996_127.0.0.1_3892]Ignore complete event,isRunning:false,isAbandon=false
14:51:53.582 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@33b63bbe[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 65]
14:51:53.769 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:51:53.776 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:51:53.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:51:53.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:51:53.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:51:53.786 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:51:59.068 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:51:59.905 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0
14:51:59.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
14:52:00.028 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:52:00.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:52:00.048 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:52:00.059 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:52:00.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:52:00.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:52:00.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018be939ed38
14:52:00.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000018be939ef58
14:52:00.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:52:00.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:52:00.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:52:01.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752562320887_127.0.0.1_4688
14:52:01.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Notify connected event to listeners.
14:52:01.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:52:01.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f614e1b5-0155-4b6a-8f56-a6abf49bbb70_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018be9518ad8
14:52:01.264 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:52:05.336 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:52:05.337 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:52:05.337 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:52:05.546 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:52:06.303 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:52:06.306 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:52:06.306 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:52:20.325 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:52:27.683 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1b3df9e-ef33-49c8-9cbf-ea336467be9e
14:52:27.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] RpcClient init label, labels = {module=naming, source=sdk}
14:52:27.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:52:27.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:52:27.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:52:27.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:52:27.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Success to connect to server [localhost:8848] on start up, connectionId = 1752562347698_127.0.0.1_4756
14:52:27.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:52:27.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018be9518ad8
14:52:27.817 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Notify connected event to listeners.
14:52:27.915 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:52:27.978 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:52:28.304 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.894 seconds (JVM running for 30.912)
14:52:28.338 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:52:28.340 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:52:28.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:52:28.400 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Receive server push request, request = NotifySubscriberRequest, requestId = 48
14:52:28.427 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1b3df9e-ef33-49c8-9cbf-ea336467be9e] Ack server push request, request = NotifySubscriberRequest, requestId = 48
14:52:28.765 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:52:34.498 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:52:34.498 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:53:07.747 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:53:07.753 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:53:08.078 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:53:08.078 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ecce4e7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:53:08.078 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752562347698_127.0.0.1_4756
14:53:08.081 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752562347698_127.0.0.1_4756]Ignore complete event,isRunning:false,isAbandon=false
14:53:08.083 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@705bdd86[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 19]
14:53:08.243 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:53:08.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:53:08.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:53:08.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:53:08.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:53:08.243 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:53:13.233 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:53:14.269 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 274f0ef6-ace2-495e-862b-e65da2cb7818_config-0
14:53:14.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
14:53:14.375 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
14:53:14.384 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:53:14.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:53:14.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:53:14.414 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:53:14.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:53:14.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d2a839dd70
14:53:14.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d2a839df90
14:53:14.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:53:14.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:53:14.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:53:15.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752562395153_127.0.0.1_4836
14:53:15.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Notify connected event to listeners.
14:53:15.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:53:15.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [274f0ef6-ace2-495e-862b-e65da2cb7818_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d2a8517cb0
14:53:15.550 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:53:19.607 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:53:19.608 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:53:19.609 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:53:19.817 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:53:20.594 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:53:20.596 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:53:20.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:53:29.593 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:53:32.963 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5cdf68dc-3034-4fa3-9983-7c3bbb345189
14:53:32.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] RpcClient init label, labels = {module=naming, source=sdk}
14:53:32.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:53:32.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:53:32.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:53:32.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:53:33.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Success to connect to server [localhost:8848] on start up, connectionId = 1752562412974_127.0.0.1_4950
14:53:33.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:53:33.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Notify connected event to listeners.
14:53:33.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d2a8517cb0
14:53:33.162 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:53:33.208 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:53:33.389 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.8 seconds (JVM running for 21.842)
14:53:33.407 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:53:33.408 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:53:33.409 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:53:33.724 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:53:33.748 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cdf68dc-3034-4fa3-9983-7c3bbb345189] Ack server push request, request = NotifySubscriberRequest, requestId = 53
14:53:33.839 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:53:48.218 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:53:48.220 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:14:42.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:14:42.673 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:14:43.007 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:14:43.007 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a6dcb3b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:14:43.007 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752562412974_127.0.0.1_4950
15:14:43.012 [nacos-grpc-client-executor-193] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752562412974_127.0.0.1_4950]Ignore complete event,isRunning:false,isAbandon=false
15:14:43.018 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2592c8f7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 194]
15:14:43.198 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:14:43.199 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:14:43.208 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:14:43.208 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:14:43.212 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:14:43.212 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:14:48.448 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:14:49.224 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0021ebc6-b91d-403f-a13b-e280bb233514_config-0
15:14:49.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
15:14:49.334 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
15:14:49.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:14:49.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
15:14:49.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
15:14:49.384 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:14:49.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:14:49.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002186439dd70
15:14:49.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002186439df90
15:14:49.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:14:49.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:14:49.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:14:50.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752563690106_127.0.0.1_6815
15:14:50.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Notify connected event to listeners.
15:14:50.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:14:50.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0021ebc6-b91d-403f-a13b-e280bb233514_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021864517cb0
15:14:50.509 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:14:54.543 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:14:54.543 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:14:54.545 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:14:54.769 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:14:55.565 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:14:55.567 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:14:55.568 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:15:03.544 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:15:06.476 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3fce8d37-590f-4df0-b583-228f7e93b480
15:15:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] RpcClient init label, labels = {module=naming, source=sdk}
15:15:06.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:15:06.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:15:06.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:15:06.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:15:06.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Success to connect to server [localhost:8848] on start up, connectionId = 1752563706491_127.0.0.1_6832
15:15:06.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:15:06.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Notify connected event to listeners.
15:15:06.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021864517cb0
15:15:06.710 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:15:06.753 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:15:06.932 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.155 seconds (JVM running for 20.218)
15:15:06.952 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:15:06.953 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:15:06.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:15:07.217 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Receive server push request, request = NotifySubscriberRequest, requestId = 58
15:15:07.239 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fce8d37-590f-4df0-b583-228f7e93b480] Ack server push request, request = NotifySubscriberRequest, requestId = 58
15:15:07.551 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:15:12.124 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:15:12.124 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:16:40.685 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:16:40.695 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:16:41.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:16:41.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66c4d23f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:16:41.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752563706491_127.0.0.1_6832
15:16:41.027 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752563706491_127.0.0.1_6832]Ignore complete event,isRunning:false,isAbandon=false
15:16:41.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6263d6ab[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 29]
15:16:41.239 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:16:41.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:16:41.253 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:16:41.253 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:16:41.255 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:16:41.255 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:16:46.538 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:16:47.308 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0
15:16:47.398 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
15:16:47.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:16:47.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:16:47.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:16:47.483 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:16:47.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
15:16:47.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:16:47.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000026c5439ed38
15:16:47.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000026c5439ef58
15:16:47.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:16:47.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:16:47.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:16:48.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752563808357_127.0.0.1_7015
15:16:48.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Notify connected event to listeners.
15:16:48.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:16:48.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [950ffb05-1b04-460b-9fd4-1504cb3f377e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026c54518ad8
15:16:48.795 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:16:52.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:16:52.690 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:16:52.690 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:16:52.858 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:16:53.584 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:16:53.587 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:16:53.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:17:00.948 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:17:03.906 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e7b1f88-7b40-485c-888a-40b205cb8166
15:17:03.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] RpcClient init label, labels = {module=naming, source=sdk}
15:17:03.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:17:03.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:17:03.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:17:03.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:17:04.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Success to connect to server [localhost:8848] on start up, connectionId = 1752563823919_127.0.0.1_7037
15:17:04.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Notify connected event to listeners.
15:17:04.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:17:04.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026c54518ad8
15:17:04.122 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:17:04.165 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:17:04.342 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.425 seconds (JVM running for 19.472)
15:17:04.360 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:17:04.361 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:17:04.362 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:17:04.571 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:17:04.635 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Receive server push request, request = NotifySubscriberRequest, requestId = 63
15:17:04.664 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7b1f88-7b40-485c-888a-40b205cb8166] Ack server push request, request = NotifySubscriberRequest, requestId = 63
15:17:09.802 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:17:09.803 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:25:36.279 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:25:36.286 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:25:36.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:25:36.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@45b9d596[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:25:36.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752563823919_127.0.0.1_7037
15:25:36.620 [nacos-grpc-client-executor-114] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752563823919_127.0.0.1_7037]Ignore complete event,isRunning:false,isAbandon=false
15:25:36.622 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@24bb49f7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 115]
15:25:36.780 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:25:36.782 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:25:36.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:25:36.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:25:36.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:25:36.789 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:29:20.741 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:29:23.002 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c62072a8-3985-4763-aef1-af00d8d63350_config-0
15:29:23.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 82 ms to scan 1 urls, producing 3 keys and 6 values 
15:29:23.361 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
15:29:23.388 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 10 values 
15:29:23.419 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
15:29:23.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
15:29:23.465 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
15:29:23.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:23.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001fb5a3e3da8
15:29:23.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001fb5a3e3fc8
15:29:23.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:23.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:23.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:25.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752564565184_127.0.0.1_8296
15:29:25.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:25.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Notify connected event to listeners.
15:29:25.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c62072a8-3985-4763-aef1-af00d8d63350_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001fb5a523ff8
15:29:25.848 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:29:36.484 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:29:36.486 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:29:36.487 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:29:37.039 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:29:39.026 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:29:39.029 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:29:39.029 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:29:54.955 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:29:58.687 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d108c82-c7e8-4db4-9733-8337836d844c
15:29:58.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] RpcClient init label, labels = {module=naming, source=sdk}
15:29:58.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:29:58.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:29:58.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:29:58.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:58.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Success to connect to server [localhost:8848] on start up, connectionId = 1752564598695_127.0.0.1_8513
15:29:58.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:58.809 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Notify connected event to listeners.
15:29:58.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001fb5a523ff8
15:29:58.876 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:29:58.922 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:29:59.089 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 40.19 seconds (JVM running for 43.32)
15:29:59.127 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:29:59.128 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:29:59.129 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:29:59.409 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Receive server push request, request = NotifySubscriberRequest, requestId = 71
15:29:59.431 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d108c82-c7e8-4db4-9733-8337836d844c] Ack server push request, request = NotifySubscriberRequest, requestId = 71
15:30:38.389 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:30:40.224 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:30:40.224 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:18:04.601 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:18:04.607 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:18:04.945 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:18:04.945 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7956b199[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:18:04.945 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752564598695_127.0.0.1_8513
19:18:04.952 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d1e933a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2743]
19:18:05.215 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:18:05.218 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:18:05.229 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:18:05.229 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:18:05.230 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:18:05.230 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
