09:16:07.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:16:09.362 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55914887-f497-4526-926a-667bf2413b89_config-0
09:16:09.502 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 54 ms to scan 1 urls, producing 3 keys and 6 values 
09:16:09.581 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:16:09.599 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:16:09.613 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:16:09.630 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:16:09.648 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:16:09.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:16:09.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019c183b9e68
09:16:09.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000019c183ba088
09:16:09.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:16:09.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:16:09.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:11.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:11.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:11.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:11.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:11.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019c184c8000
09:16:11.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:11.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:12.018 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:12.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:12.976 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:13.503 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:16:13.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.175 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:18.370 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:18.958 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:16:18.958 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:16:18.958 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:16:19.151 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:16:19.671 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:21.060 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:22.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:24.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:25.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:27.692 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:29.591 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:31.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:33.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:35.889 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:38.164 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:40.561 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55914887-f497-4526-926a-667bf2413b89_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:40.789 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:16:40.865 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:18:33.370 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:34.392 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a73f7527-7c70-4278-b425-befaffabd894_config-0
09:18:34.518 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:34.579 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:34.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:34.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:34.619 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:34.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:34.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:34.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002351239dd70
09:18:34.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002351239df90
09:18:34.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:34.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:34.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:36.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752110315783_127.0.0.1_4224
09:18:36.079 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Notify connected event to listeners.
09:18:36.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:36.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a73f7527-7c70-4278-b425-befaffabd894_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023512517b88
09:18:36.365 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:42.235 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:18:42.236 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:42.236 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:42.517 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:43.976 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:18:43.978 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:18:43.979 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:58.791 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:02.044 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 08fa4897-6238-479a-b682-95003f0cbb3e
09:19:02.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] RpcClient init label, labels = {module=naming, source=sdk}
09:19:02.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:02.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:02.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:02.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:02.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Success to connect to server [localhost:8848] on start up, connectionId = 1752110342052_127.0.0.1_4400
09:19:02.174 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Notify connected event to listeners.
09:19:02.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:02.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023512517b88
09:19:02.236 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:19:02.274 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:19:02.418 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.819 seconds (JVM running for 31.096)
09:19:02.432 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:19:02.436 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:19:02.436 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:19:02.778 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:19:02.795 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:19:02.937 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:35:49.602 [nacos-grpc-client-executor-212] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:35:49.602 [nacos-grpc-client-executor-212] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08fa4897-6238-479a-b682-95003f0cbb3e] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:35:51.256 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:35:51.256 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:35:51.512 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:35:51.519 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:35:51.538 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:35:51.538 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:48:02.416 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
11:48:02.418 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:38:02.147 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:02.152 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:02.475 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:02.475 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4e19b19d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:02.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752110342052_127.0.0.1_4400
13:38:02.478 [nacos-grpc-client-executor-3114] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752110342052_127.0.0.1_4400]Ignore complete event,isRunning:false,isAbandon=false
13:38:02.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@21e7beb7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3115]
13:38:02.653 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:38:02.667 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:38:02.667 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:38:02.667 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
13:38:02.667 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
13:38:02.667 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:38:02.685 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:38:02.685 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:45:18.687 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:45:21.152 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0
13:45:21.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 99 ms to scan 1 urls, producing 3 keys and 6 values 
13:45:21.524 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 4 keys and 9 values 
13:45:21.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
13:45:21.576 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
13:45:21.597 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
13:45:21.622 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
13:45:21.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:45:21.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fa3539ed38
13:45:21.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fa3539ef58
13:45:21.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:45:21.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:45:21.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:23.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752126323563_127.0.0.1_10447
13:45:23.946 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Notify connected event to listeners.
13:45:23.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:23.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [daf6bb81-f1a6-4efe-8fe9-bcb561954cad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fa35518668
13:45:24.198 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:45:32.605 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:45:32.606 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:45:32.606 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:45:32.905 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:45:33.943 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:45:33.945 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:45:33.945 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:45:45.673 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:45:48.464 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc
13:45:48.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] RpcClient init label, labels = {module=naming, source=sdk}
13:45:48.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:45:48.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:45:48.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:45:48.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:48.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Success to connect to server [localhost:8848] on start up, connectionId = 1752126348472_127.0.0.1_10542
13:45:48.584 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Notify connected event to listeners.
13:45:48.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:48.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fa35518668
13:45:48.631 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:45:48.659 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:45:48.765 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 31.935 seconds (JVM running for 35.1)
13:45:48.796 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:45:48.797 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:45:48.798 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:45:49.027 [RMI TCP Connection(9)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:45:49.114 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Receive server push request, request = NotifySubscriberRequest, requestId = 34
13:45:49.127 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f81bae-eb5f-4f3f-aa2d-5409fd4ed7dc] Ack server push request, request = NotifySubscriberRequest, requestId = 34
13:46:01.769 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:46:01.769 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:20:37.937 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:37.939 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63a05911[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752126348472_127.0.0.1_10542
14:20:38.265 [nacos-grpc-client-executor-428] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752126348472_127.0.0.1_10542]Ignore complete event,isRunning:false,isAbandon=false
14:20:38.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@202da121[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 429]
14:20:38.400 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:20:38.400 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:20:38.408 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:20:38.408 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:20:38.408 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:20:38.408 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:31:38.570 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:39.694 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0
14:31:39.816 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:39.874 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:39.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:39.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:39.905 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:39.918 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:39.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:39.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d30339e8d8
14:31:39.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d30339eaf8
14:31:39.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:39.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:39.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:41.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752129101419_127.0.0.1_1626
14:31:41.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Notify connected event to listeners.
14:31:41.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:41.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fd1251-21ca-44bd-b43b-65948cdddbf3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d3035188e0
14:31:41.851 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:47.031 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:31:47.031 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:47.031 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:31:47.231 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:48.655 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:31:48.655 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:31:48.661 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:31:57.538 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:32:01.334 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 70edddae-56f2-4151-84f4-bc08e2cadf84
14:32:01.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] RpcClient init label, labels = {module=naming, source=sdk}
14:32:01.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:32:01.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:32:01.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:32:01.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:32:01.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Success to connect to server [localhost:8848] on start up, connectionId = 1752129121356_127.0.0.1_1753
14:32:01.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:01.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d3035188e0
14:32:01.665 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Notify connected event to listeners.
14:32:01.774 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:32:01.866 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:32:02.194 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.559 seconds (JVM running for 25.705)
14:32:02.298 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:32:02.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:32:02.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:32:02.314 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:32:02.347 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70edddae-56f2-4151-84f4-bc08e2cadf84] Ack server push request, request = NotifySubscriberRequest, requestId = 42
14:32:02.840 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:35:53.274 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:35:53.276 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:58:53.211 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:58:53.216 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:58:53.557 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:58:53.557 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@115e3542[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:58:53.557 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752129121356_127.0.0.1_1753
14:58:53.559 [nacos-grpc-client-executor-333] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752129121356_127.0.0.1_1753]Ignore complete event,isRunning:false,isAbandon=false
14:58:53.563 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@22c890ed[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 334]
14:58:53.720 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:58:53.723 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:58:53.731 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:58:53.731 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:58:53.732 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:58:53.732 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:29:27.691 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:29:28.618 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0
15:29:28.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
15:29:28.718 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
15:29:28.724 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:29:28.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:29:28.740 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:29:28.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:29:28.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:28.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000288de3be480
15:29:28.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000288de3be6a0
15:29:28.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:28.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:28.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:29.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752132569351_127.0.0.1_5853
15:29:29.549 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Notify connected event to listeners.
15:29:29.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:29.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7404ddc0-7cf3-4680-861a-2f7a833d8aae_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000288de4f8228
15:29:29.640 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:29:32.381 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:29:32.381 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:29:32.381 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:29:32.520 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:29:33.455 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:29:33.456 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:29:33.456 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:29:41.652 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:29:45.677 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b03727c5-590a-4bc6-92d6-f721b15bc192
15:29:45.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] RpcClient init label, labels = {module=naming, source=sdk}
15:29:45.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:29:45.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:29:45.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:29:45.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:45.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Success to connect to server [localhost:8848] on start up, connectionId = 1752132585712_127.0.0.1_5983
15:29:45.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:45.832 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Notify connected event to listeners.
15:29:45.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000288de4f8228
15:29:45.895 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:29:45.939 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:29:46.133 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.129 seconds (JVM running for 20.831)
15:29:46.156 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:29:46.157 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:29:46.157 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:29:46.549 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Receive server push request, request = NotifySubscriberRequest, requestId = 48
15:29:46.569 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b03727c5-590a-4bc6-92d6-f721b15bc192] Ack server push request, request = NotifySubscriberRequest, requestId = 48
15:29:46.751 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:56:35.528 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:56:35.532 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:56:35.862 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:56:35.862 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@b4b13a9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:56:35.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752132585712_127.0.0.1_5983
15:56:35.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@667bd05e[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 332]
15:56:35.867 [nacos-grpc-client-executor-332] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752132585712_127.0.0.1_5983]Ignore complete event,isRunning:false,isAbandon=false
15:56:36.002 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:56:36.002 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:56:36.002 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:56:36.002 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:56:48.713 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:56:49.242 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f90b76c8-748e-46ae-80cb-2e28faef9683_config-0
15:56:49.303 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
15:56:49.328 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
15:56:49.336 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:56:49.346 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
15:56:49.346 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
15:56:49.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
15:56:49.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:56:49.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024da039e480
15:56:49.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000024da039e6a0
15:56:49.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:56:49.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:56:49.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:56:50.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752134209864_127.0.0.1_8045
15:56:50.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Notify connected event to listeners.
15:56:50.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:56:50.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f90b76c8-748e-46ae-80cb-2e28faef9683_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024da0518228
15:56:50.144 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:56:52.620 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:56:52.620 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:56:52.620 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:56:52.738 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:56:53.332 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:56:53.332 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:56:53.332 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:56:58.158 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:57:00.151 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9f14636a-fbe6-49ec-a105-e9225487f635
15:57:00.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] RpcClient init label, labels = {module=naming, source=sdk}
15:57:00.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:57:00.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:57:00.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:57:00.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:57:00.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Success to connect to server [localhost:8848] on start up, connectionId = 1752134220160_127.0.0.1_8055
15:57:00.276 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Notify connected event to listeners.
15:57:00.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:57:00.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024da0518228
15:57:00.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:57:00.334 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:57:00.423 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.149 seconds (JVM running for 13.259)
15:57:00.434 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:57:00.434 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:57:00.435 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:57:00.615 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:57:00.899 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Receive server push request, request = NotifySubscriberRequest, requestId = 53
15:57:00.912 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f14636a-fbe6-49ec-a105-e9225487f635] Ack server push request, request = NotifySubscriberRequest, requestId = 53
16:43:59.744 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:43:59.751 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:44:00.100 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:44:00.101 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b117ba6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:44:00.101 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752134220160_127.0.0.1_8055
16:44:00.103 [nacos-grpc-client-executor-565] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752134220160_127.0.0.1_8055]Ignore complete event,isRunning:false,isAbandon=false
16:44:00.109 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@36fdd036[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 566]
16:44:00.288 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:44:00.293 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:44:00.301 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:44:00.302 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:44:06.282 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:44:07.026 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0
16:44:07.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
16:44:07.129 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:44:07.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
16:44:07.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:44:07.160 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
16:44:07.171 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:44:07.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:44:07.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002565d39dd00
16:44:07.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002565d39df20
16:44:07.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:44:07.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:44:07.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:44:08.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752137047980_127.0.0.1_12085
16:44:08.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Notify connected event to listeners.
16:44:08.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:44:08.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a73313a-84d7-4a08-a5a6-bfa7c352b476_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002565d517b88
16:44:08.383 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:44:12.454 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:44:12.455 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:44:12.455 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:44:12.662 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:44:13.577 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:44:13.579 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:44:13.580 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:44:21.873 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:44:25.085 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 12a8b645-c693-435f-b29f-64463f62c978
16:44:25.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] RpcClient init label, labels = {module=naming, source=sdk}
16:44:25.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:44:25.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:44:25.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:44:25.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:44:25.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Success to connect to server [localhost:8848] on start up, connectionId = 1752137065101_127.0.0.1_12107
16:44:25.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Notify connected event to listeners.
16:44:25.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:44:25.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002565d517b88
16:44:25.317 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:44:25.355 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:44:25.506 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.881 seconds (JVM running for 20.969)
16:44:25.525 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:44:25.526 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:44:25.526 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:44:25.806 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:44:25.834 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Receive server push request, request = NotifySubscriberRequest, requestId = 61
16:44:25.855 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12a8b645-c693-435f-b29f-64463f62c978] Ack server push request, request = NotifySubscriberRequest, requestId = 61
16:49:42.603 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:49:42.604 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:54:56.923 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:54:56.941 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:54:57.290 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:54:57.292 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3fcb719f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:54:57.292 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752137065101_127.0.0.1_12107
16:54:57.301 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@860e512[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 136]
16:54:57.471 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:54:57.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:54:57.489 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:54:57.489 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:54:57.489 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:54:57.489 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:55:02.706 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:55:03.578 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0
16:55:03.657 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
16:55:03.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
16:55:03.708 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
16:55:03.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:55:03.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
16:55:03.744 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:55:03.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:55:03.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ad9639dd70
16:55:03.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ad9639df90
16:55:03.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:55:03.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:55:03.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:55:04.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752137704588_127.0.0.1_13133
16:55:04.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Notify connected event to listeners.
16:55:04.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:55:04.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7317b642-828a-4fc7-a0a9-9041ce0d2af0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ad96517b88
16:55:04.955 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:55:08.825 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:55:08.825 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:55:08.826 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:55:09.017 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:55:09.781 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:55:09.784 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:55:09.784 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:55:17.615 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:55:20.682 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 65e12c95-fc70-49da-ba2d-41d96c9c5422
16:55:20.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] RpcClient init label, labels = {module=naming, source=sdk}
16:55:20.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:55:20.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:55:20.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:55:20.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:55:20.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Success to connect to server [localhost:8848] on start up, connectionId = 1752137720693_127.0.0.1_13158
16:55:20.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:55:20.817 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Notify connected event to listeners.
16:55:20.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ad96517b88
16:55:20.909 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:55:20.958 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:55:21.089 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.133 seconds (JVM running for 20.181)
16:55:21.104 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:55:21.105 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:55:21.105 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:55:21.454 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Receive server push request, request = NotifySubscriberRequest, requestId = 65
16:55:21.472 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65e12c95-fc70-49da-ba2d-41d96c9c5422] Ack server push request, request = NotifySubscriberRequest, requestId = 65
16:55:21.747 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:55:29.350 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:55:29.353 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:19:32.301 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:19:32.305 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:19:32.636 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:19:32.637 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d0375b8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:19:32.637 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752137720693_127.0.0.1_13158
17:19:32.642 [nacos-grpc-client-executor-303] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752137720693_127.0.0.1_13158]Ignore complete event,isRunning:false,isAbandon=false
17:19:32.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@8823e6b[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 304]
17:19:32.818 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:19:32.822 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:19:32.831 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:19:32.831 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:19:32.834 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:19:32.834 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:20:02.274 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:20:03.129 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0
17:20:03.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
17:20:03.269 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
17:20:03.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
17:20:03.295 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:20:03.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
17:20:03.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
17:20:03.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:20:03.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000012c913cdd70
17:20:03.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000012c913cdf90
17:20:03.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:20:03.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:20:03.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:20:04.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752139204188_127.0.0.1_1677
17:20:04.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Notify connected event to listeners.
17:20:04.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:20:04.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b7022b5-d10f-4c80-9b13-7765ea9383f4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000012c915088c8
17:20:04.619 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:20:09.649 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:20:09.649 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:20:09.651 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:20:10.104 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:20:11.479 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:20:11.486 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:20:11.486 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:20:26.079 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:20:29.237 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa70adc4-e9bd-46f4-b842-b2615a097ee9
17:20:29.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] RpcClient init label, labels = {module=naming, source=sdk}
17:20:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:20:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:20:29.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:20:29.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:20:29.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Success to connect to server [localhost:8848] on start up, connectionId = 1752139229256_127.0.0.1_1744
17:20:29.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:20:29.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Notify connected event to listeners.
17:20:29.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000012c915088c8
17:20:29.451 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:20:29.485 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:20:29.667 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.082 seconds (JVM running for 28.978)
17:20:29.689 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:20:29.690 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:20:29.693 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:20:29.986 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Receive server push request, request = NotifySubscriberRequest, requestId = 67
17:20:30.001 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa70adc4-e9bd-46f4-b842-b2615a097ee9] Ack server push request, request = NotifySubscriberRequest, requestId = 67
17:20:30.024 [RMI TCP Connection(11)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:23:56.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:23:56.924 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:23:57.273 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:23:57.275 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@64dbda9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:23:57.275 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752139229256_127.0.0.1_1744
17:23:57.280 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752139229256_127.0.0.1_1744]Ignore complete event,isRunning:false,isAbandon=false
17:23:57.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@560ae668[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 51]
17:23:57.449 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:23:57.454 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:23:57.466 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:23:57.466 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:24:17.665 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:24:18.590 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f50b6240-7479-4577-bdbe-31d5eaccc204_config-0
17:24:18.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
17:24:18.691 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
17:24:18.700 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:24:18.715 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
17:24:18.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
17:24:18.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
17:24:18.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:24:18.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b9e439dd70
17:24:18.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b9e439df90
17:24:18.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:24:18.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:24:18.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:24:19.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752139459532_127.0.0.1_2144
17:24:19.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Notify connected event to listeners.
17:24:19.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:24:19.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50b6240-7479-4577-bdbe-31d5eaccc204_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b9e4517b78
17:24:19.965 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:24:24.246 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:24:24.247 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:24:24.248 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:24:24.483 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:24:25.357 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:24:25.360 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:24:25.360 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:24:33.902 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:24:37.469 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b28701ac-05c0-4b03-8e3d-0eeef786b316
17:24:37.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] RpcClient init label, labels = {module=naming, source=sdk}
17:24:37.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:24:37.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:24:37.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:24:37.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:24:37.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Success to connect to server [localhost:8848] on start up, connectionId = 1752139477481_127.0.0.1_2183
17:24:37.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:24:37.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b9e4517b78
17:24:37.606 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Notify connected event to listeners.
17:24:37.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:24:37.728 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:24:37.904 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.042 seconds (JVM running for 22.223)
17:24:37.919 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:24:37.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:24:37.922 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:24:38.091 [RMI TCP Connection(9)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:24:38.167 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Receive server push request, request = NotifySubscriberRequest, requestId = 70
17:24:38.182 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b28701ac-05c0-4b03-8e3d-0eeef786b316] Ack server push request, request = NotifySubscriberRequest, requestId = 70
17:24:49.431 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:24:49.438 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:59:49.705 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:59:49.714 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:59:50.060 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:59:50.062 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@10e8f875[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:59:50.062 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752139477481_127.0.0.1_2183
17:59:50.070 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@328664ea[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 435]
17:59:50.070 [nacos-grpc-client-executor-435] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752139477481_127.0.0.1_2183]Ignore complete event,isRunning:false,isAbandon=false
17:59:50.277 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:59:50.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:59:50.289 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:59:50.289 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:59:50.294 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:59:50.294 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:00:03.153 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:00:04.400 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-990b-4595-82a8-f94637aba156_config-0
18:00:04.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
18:00:04.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
18:00:04.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
18:00:04.559 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
18:00:04.573 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
18:00:04.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
18:00:04.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:00:04.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002365039e8d8
18:00:04.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002365039eaf8
18:00:04.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:00:04.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:00:04.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:00:05.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752141605384_127.0.0.1_6482
18:00:05.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Notify connected event to listeners.
18:00:05.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:00:05.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-990b-4595-82a8-f94637aba156_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023650518ad8
18:00:05.724 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:00:10.447 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:00:10.460 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:00:10.460 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:00:10.858 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:00:12.405 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:00:12.409 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:00:12.409 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:00:27.760 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:00:31.042 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 553cd04f-0f35-473e-beb6-43d949e3d8c2
18:00:31.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] RpcClient init label, labels = {module=naming, source=sdk}
18:00:31.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:00:31.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:00:31.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:00:31.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:00:31.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Success to connect to server [localhost:8848] on start up, connectionId = 1752141631072_127.0.0.1_6549
18:00:31.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:00:31.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Notify connected event to listeners.
18:00:31.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023650518ad8
18:00:31.260 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:00:31.303 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
18:00:31.459 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.182 seconds (JVM running for 34.357)
18:00:31.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:00:31.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:00:31.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:00:31.795 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Receive server push request, request = NotifySubscriberRequest, requestId = 73
18:00:31.817 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Ack server push request, request = NotifySubscriberRequest, requestId = 73
18:00:31.862 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:29:37.910 [nacos-grpc-client-executor-1082] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Receive server push request, request = NotifySubscriberRequest, requestId = 79
19:29:37.912 [nacos-grpc-client-executor-1082] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [553cd04f-0f35-473e-beb6-43d949e3d8c2] Ack server push request, request = NotifySubscriberRequest, requestId = 79
19:29:39.440 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:29:39.440 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:29:39.440 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:29:39.446 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:29:39.458 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:29:39.459 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:29:39.745 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:29:39.744 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:29:39.745 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:29:39.746 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:29:39.746 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:29:39.746 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:29:39.748 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:29:39.748 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:41:30.983 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:30.987 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:31.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:31.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d2338ec[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:31.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752141631072_127.0.0.1_6549
20:41:31.330 [nacos-grpc-client-executor-1958] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752141631072_127.0.0.1_6549]Ignore complete event,isRunning:false,isAbandon=false
20:41:31.336 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d024f8e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1959]
20:41:31.530 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:41:31.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
20:41:31.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
20:41:31.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:41:31.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:41:31.530 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
