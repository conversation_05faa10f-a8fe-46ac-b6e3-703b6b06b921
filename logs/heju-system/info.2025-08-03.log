10:22:16.723 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:22:18.497 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0
10:22:18.727 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 96 ms to scan 1 urls, producing 3 keys and 6 values 
10:22:18.851 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 4 keys and 9 values 
10:22:18.873 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 3 keys and 10 values 
10:22:18.887 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:22:18.917 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 7 values 
10:22:18.944 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
10:22:18.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:22:18.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000252e33b7b00
10:22:18.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000252e33b7d20
10:22:18.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:22:18.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:22:18.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:22:20.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754187740584_127.0.0.1_1375
10:22:20.935 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Notify connected event to listeners.
10:22:20.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:22:20.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcd0305a-ea5f-4897-ade4-bc71584158a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000252e34f1450
10:22:21.191 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:22:29.511 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:22:29.511 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:22:29.511 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:22:29.869 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:22:31.237 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:22:31.240 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:22:31.242 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:22:43.392 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:22:47.566 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4d1d319-d105-4f2a-908c-499adc130503
10:22:47.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] RpcClient init label, labels = {module=naming, source=sdk}
10:22:47.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:22:47.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:22:47.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:22:47.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:22:47.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Success to connect to server [localhost:8848] on start up, connectionId = 1754187767588_127.0.0.1_1817
10:22:47.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:22:47.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Notify connected event to listeners.
10:22:47.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000252e34f1450
10:22:47.786 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:22:47.839 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:22:48.034 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.868 seconds (JVM running for 37.539)
10:22:48.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:22:48.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:22:48.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:22:48.308 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:22:48.325 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Ack server push request, request = NotifySubscriberRequest, requestId = 9
10:23:57.973 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:24:00.490 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Receive server push request, request = NotifySubscriberRequest, requestId = 13
10:24:00.495 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4d1d319-d105-4f2a-908c-499adc130503] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:24:01.324 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:24:01.324 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:24:01.532 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:24:01.532 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:53:46.513 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:53:46.513 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:53:46.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:53:46.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d63aa3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:53:46.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754187767588_127.0.0.1_1817
10:53:46.872 [nacos-grpc-client-executor-314] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754187767588_127.0.0.1_1817]Ignore complete event,isRunning:false,isAbandon=false
10:53:46.878 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b0df543[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 315]
10:53:47.007 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:53:47.007 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:53:47.022 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:53:47.022 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:53:47.022 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:53:47.022 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:53:47.022 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:53:47.022 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:53:51.431 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:53:52.058 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7edaf834-c357-4449-81cf-f5799715e87d_config-0
10:53:52.104 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
10:53:52.131 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:53:52.138 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
10:53:52.145 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:53:52.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:53:52.158 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
10:53:52.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:53:52.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000015f623be8d8
10:53:52.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000015f623beaf8
10:53:52.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:53:52.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:53:52.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:53:52.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754189632640_127.0.0.1_8228
10:53:52.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Notify connected event to listeners.
10:53:52.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:53:52.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7edaf834-c357-4449-81cf-f5799715e87d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015f624f8668
10:53:52.918 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:53:55.474 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:53:55.474 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:53:55.474 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:53:55.587 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:53:56.128 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:53:56.130 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:53:56.130 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:54:01.426 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:54:04.097 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 748301f1-34e4-4a81-902c-6b7dbacca654
10:54:04.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] RpcClient init label, labels = {module=naming, source=sdk}
10:54:04.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:54:04.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:54:04.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:54:04.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:54:04.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Success to connect to server [localhost:8848] on start up, connectionId = 1754189644109_127.0.0.1_8285
10:54:04.218 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Notify connected event to listeners.
10:54:04.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:54:04.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015f624f8668
10:54:04.258 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:54:04.280 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:54:04.368 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.424 seconds (JVM running for 14.272)
10:54:04.393 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:54:04.393 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:54:04.393 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:54:04.748 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:54:04.754 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:54:04.771 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [748301f1-34e4-4a81-902c-6b7dbacca654] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:54:09.244 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:54:09.244 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:55:22.375 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:55:22.379 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:55:22.723 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:55:22.723 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@15dffa46[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:55:22.723 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754189644109_127.0.0.1_8285
11:55:22.723 [nacos-grpc-client-executor-747] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754189644109_127.0.0.1_8285]Ignore complete event,isRunning:false,isAbandon=false
11:55:22.723 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@563abf0b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 748]
11:55:22.853 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:55:22.853 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:55:22.863 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:55:22.863 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:55:22.863 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:55:22.863 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:55:30.097 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:55:30.705 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0
11:55:30.758 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
11:55:30.783 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:55:30.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:55:30.795 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:55:30.802 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:55:30.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:55:30.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:55:30.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000204da3be8d8
11:55:30.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000204da3beaf8
11:55:30.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:55:30.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:55:30.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:55:31.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754193331317_127.0.0.1_13340
11:55:31.499 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Notify connected event to listeners.
11:55:31.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:55:31.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e357f5e-6107-4dbf-aa2f-e5b2da2c165a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000204da4f8668
11:55:31.593 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:55:34.172 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:55:34.172 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:55:34.172 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:55:34.312 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:55:35.075 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:55:35.076 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:55:35.076 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:55:44.141 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:55:48.493 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-7872-4d81-ad55-eb8089fb0f15
11:55:48.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] RpcClient init label, labels = {module=naming, source=sdk}
11:55:48.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:55:48.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:55:48.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:55:48.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:55:48.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Success to connect to server [localhost:8848] on start up, connectionId = 1754193348507_127.0.0.1_13433
11:55:48.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:55:48.623 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Notify connected event to listeners.
11:55:48.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000204da4f8668
11:55:48.680 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:55:48.725 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:55:48.931 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.399 seconds (JVM running for 20.415)
11:55:48.973 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:55:48.974 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:55:48.975 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:55:49.238 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Receive server push request, request = NotifySubscriberRequest, requestId = 27
11:55:49.254 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-7872-4d81-ad55-eb8089fb0f15] Ack server push request, request = NotifySubscriberRequest, requestId = 27
11:55:49.343 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:58:32.559 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:58:32.559 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:58:32.731 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:58:32.731 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:58:32.746 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:58:32.746 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:43:56.801 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:43:56.801 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:43:57.134 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:43:57.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@491a9267[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:43:57.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754193348507_127.0.0.1_13433
12:43:57.136 [nacos-grpc-client-executor-583] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754193348507_127.0.0.1_13433]Ignore complete event,isRunning:false,isAbandon=false
12:43:57.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@374a0abe[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 584]
12:43:57.285 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:43:57.285 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:43:57.285 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:43:57.285 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:43:57.285 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:43:57.285 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:44:01.816 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:44:02.352 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0
12:44:02.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
12:44:02.431 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
12:44:02.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:44:02.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
12:44:02.451 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
12:44:02.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
12:44:02.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:44:02.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000256c33c2328
12:44:02.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000256c33c2548
12:44:02.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:44:02.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:44:02.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:44:03.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754196242993_127.0.0.1_9122
12:44:03.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Notify connected event to listeners.
12:44:03.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:44:03.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e67c8d2-a611-4843-ac3f-0a6b9b47cf8e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000256c34fbff8
12:44:03.266 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:44:05.699 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:44:05.700 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:44:05.700 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:44:05.813 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:44:06.308 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:44:06.309 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:44:06.309 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:44:12.865 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:44:16.912 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e8433eac-8677-4ddc-a107-889f0c9b001b
12:44:16.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] RpcClient init label, labels = {module=naming, source=sdk}
12:44:16.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:44:16.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:44:16.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:44:16.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:44:17.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Success to connect to server [localhost:8848] on start up, connectionId = 1754196256918_127.0.0.1_9213
12:44:17.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:44:17.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000256c34fbff8
12:44:17.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Notify connected event to listeners.
12:44:17.094 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:44:17.144 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:44:17.352 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.015 seconds (JVM running for 16.838)
12:44:17.375 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:44:17.384 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:44:17.384 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:44:17.485 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:44:17.638 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Receive server push request, request = NotifySubscriberRequest, requestId = 32
12:44:17.662 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8433eac-8677-4ddc-a107-889f0c9b001b] Ack server push request, request = NotifySubscriberRequest, requestId = 32
12:44:32.201 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:44:32.201 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:11:46.074 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:11:46.078 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:11:46.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:11:46.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5bccc8fd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:11:46.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754196256918_127.0.0.1_9213
14:11:46.418 [nacos-grpc-client-executor-1020] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754196256918_127.0.0.1_9213]Ignore complete event,isRunning:false,isAbandon=false
14:11:46.420 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ab4871a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1021]
14:11:46.604 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:11:46.606 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:11:46.612 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:11:46.612 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:11:46.613 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:11:46.613 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:11:53.450 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:11:54.589 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0
14:11:54.695 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 
14:11:54.748 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:11:54.760 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:11:54.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:11:54.785 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:11:54.799 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:11:54.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:11:54.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000135013b71c0
14:11:54.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000135013b73e0
14:11:54.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:11:54.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:11:54.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:11:55.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754201515718_127.0.0.1_1112
14:11:55.964 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Notify connected event to listeners.
14:11:55.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:11:55.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac02dfd1-3156-462e-bd31-2f2f0a96da82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000135014f0d48
14:11:56.085 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:11:59.583 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:11:59.584 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:11:59.584 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:11:59.798 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:12:00.776 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:12:00.778 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:12:00.778 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:12:10.496 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:12:14.583 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ced04bd6-e89b-4138-9a7a-72106c67f369
14:12:14.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] RpcClient init label, labels = {module=naming, source=sdk}
14:12:14.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:12:14.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:12:14.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:12:14.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:12:14.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Success to connect to server [localhost:8848] on start up, connectionId = 1754201534598_127.0.0.1_1151
14:12:14.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:12:14.715 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Notify connected event to listeners.
14:12:14.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000135014f0d48
14:12:14.768 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:12:14.803 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:12:14.954 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.477 seconds (JVM running for 24.177)
14:12:14.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:12:14.972 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:12:14.972 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:12:15.294 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:12:15.313 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ced04bd6-e89b-4138-9a7a-72106c67f369] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:12:18.588 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:12:19.863 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:12:19.863 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:30:47.151 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:47.154 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:47.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:47.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@e216b60[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:47.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754201534598_127.0.0.1_1151
14:30:47.503 [nacos-grpc-client-executor-237] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754201534598_127.0.0.1_1151]Ignore complete event,isRunning:false,isAbandon=false
14:30:47.503 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d4acf0b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 238]
14:30:47.648 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:30:47.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:30:47.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:30:47.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:30:47.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:30:47.653 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:30:52.848 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:30:53.413 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0
14:30:53.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
14:30:53.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:30:53.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:30:53.515 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:30:53.526 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:30:53.537 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:30:53.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:30:53.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001373139f410
14:30:53.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001373139f630
14:30:53.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:30:53.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:30:53.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:30:54.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754202654109_127.0.0.1_5824
14:30:54.293 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Notify connected event to listeners.
14:30:54.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:30:54.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e0417f9-beac-4907-b0a7-fcf7fa1269a8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013731518fb0
14:30:54.389 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:30:56.921 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:30:56.922 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:30:56.922 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:30:57.044 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:30:57.591 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:30:57.591 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:30:57.591 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:31:04.817 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:31:07.519 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cca5ae43-3be2-49c3-b20a-49f39904c55e
14:31:07.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] RpcClient init label, labels = {module=naming, source=sdk}
14:31:07.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:07.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:07.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:07.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:07.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Success to connect to server [localhost:8848] on start up, connectionId = 1754202667529_127.0.0.1_5850
14:31:07.649 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Notify connected event to listeners.
14:31:07.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:07.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013731518fb0
14:31:07.690 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:31:07.713 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:31:07.820 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.489 seconds (JVM running for 16.481)
14:31:07.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:31:07.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:31:07.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:31:08.021 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:31:08.274 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Receive server push request, request = NotifySubscriberRequest, requestId = 49
14:31:08.291 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cca5ae43-3be2-49c3-b20a-49f39904c55e] Ack server push request, request = NotifySubscriberRequest, requestId = 49
14:32:14.629 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:32:14.629 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:47:47.946 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:47:47.957 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:47:48.284 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:47:48.284 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@43b01bf4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:47:48.284 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754202667529_127.0.0.1_5850
14:47:48.284 [nacos-grpc-client-executor-213] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754202667529_127.0.0.1_5850]Ignore complete event,isRunning:false,isAbandon=false
14:47:48.291 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3fefcfe2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 214]
14:47:48.439 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:47:48.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:47:48.446 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:47:48.446 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:47:48.446 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:47:48.446 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:47:57.354 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:47:58.488 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6d8b593c-e1af-4a44-8262-dc48561687b8_config-0
14:47:58.584 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
14:47:58.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
14:47:58.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
14:47:58.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:47:58.674 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:47:58.688 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
14:47:58.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:47:58.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000020a8b3b6af8
14:47:58.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000020a8b3b6d18
14:47:58.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:47:58.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:47:58.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:47:59.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754203679464_127.0.0.1_10874
14:47:59.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Notify connected event to listeners.
14:47:59.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:47:59.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8b593c-e1af-4a44-8262-dc48561687b8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020a8b4f0668
14:47:59.799 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:48:02.685 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:48:02.685 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:48:02.685 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:48:02.814 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:48:03.326 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:48:03.326 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:48:03.326 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:48:09.200 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:48:11.626 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 35307f1f-5dc4-4891-a3ab-8167f3a5be10
14:48:11.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] RpcClient init label, labels = {module=naming, source=sdk}
14:48:11.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:48:11.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:48:11.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:48:11.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:48:11.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Success to connect to server [localhost:8848] on start up, connectionId = 1754203691639_127.0.0.1_10885
14:48:11.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:48:11.762 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Notify connected event to listeners.
14:48:11.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020a8b4f0668
14:48:11.814 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:48:11.840 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:48:11.946 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.576 seconds (JVM running for 17.454)
14:48:11.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:48:11.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:48:11.959 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:48:12.325 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Receive server push request, request = NotifySubscriberRequest, requestId = 54
14:48:12.340 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35307f1f-5dc4-4891-a3ab-8167f3a5be10] Ack server push request, request = NotifySubscriberRequest, requestId = 54
15:05:04.769 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:05:05.761 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:05:05.761 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:12:28.100 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:12:28.100 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:12:28.455 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:12:28.455 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6b7295d2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:12:28.455 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754203691639_127.0.0.1_10885
15:12:28.461 [nacos-grpc-client-executor-300] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754203691639_127.0.0.1_10885]Ignore complete event,isRunning:false,isAbandon=false
15:12:28.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@26889f38[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 301]
15:12:28.598 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:12:28.601 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:12:28.604 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:12:28.605 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:12:28.605 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:12:28.605 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:12:32.989 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:12:33.540 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a57699a8-8570-4572-be87-71e1927cbf39_config-0
15:12:33.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
15:12:33.620 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
15:12:33.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:12:33.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 5 values 
15:12:33.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
15:12:33.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
15:12:33.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:12:33.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f7913ce8d8
15:12:33.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f7913ceaf8
15:12:33.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:12:33.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:12:33.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:12:34.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754205154157_127.0.0.1_13092
15:12:34.336 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Notify connected event to listeners.
15:12:34.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:12:34.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a57699a8-8570-4572-be87-71e1927cbf39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f791508ad8
15:12:34.426 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:12:36.940 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:12:36.940 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:12:36.940 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:12:37.049 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:12:37.582 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:12:37.583 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:12:37.584 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:12:42.784 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:12:45.399 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ccc459d-e17f-47ce-af9a-bad364b2d5ef
15:12:45.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] RpcClient init label, labels = {module=naming, source=sdk}
15:12:45.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:12:45.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:12:45.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:12:45.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:12:45.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Success to connect to server [localhost:8848] on start up, connectionId = 1754205165415_127.0.0.1_13105
15:12:45.529 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Notify connected event to listeners.
15:12:45.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:12:45.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f791508ad8
15:12:45.571 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:12:45.599 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:12:45.703 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.22 seconds (JVM running for 14.167)
15:12:45.716 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:12:45.716 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:12:45.719 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:12:45.779 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:12:46.049 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Receive server push request, request = NotifySubscriberRequest, requestId = 62
15:12:46.070 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccc459d-e17f-47ce-af9a-bad364b2d5ef] Ack server push request, request = NotifySubscriberRequest, requestId = 62
15:13:11.469 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:13:11.469 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:13:11.673 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:13:11.678 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:13:11.685 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:13:11.685 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:13:11.685 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [recycle,2099] - connection recyle failed.
15:21:48.307 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:21:48.309 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:21:48.641 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:21:48.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@********[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:21:48.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754205165415_127.0.0.1_13105
15:21:48.644 [nacos-grpc-client-executor-121] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754205165415_127.0.0.1_13105]Ignore complete event,isRunning:false,isAbandon=false
15:21:48.645 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@119a4e0f[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 122]
15:21:48.769 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:21:48.769 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:21:48.769 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:21:48.775 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:21:48.775 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:21:48.775 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:21:53.682 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:54.589 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0
15:21:54.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:54.663 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:54.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:54.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:54.690 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:54.698 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:54.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:54.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cc023ceaf8
15:21:54.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001cc023ced18
15:21:54.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:54.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:54.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:55.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754205715234_127.0.0.1_13847
15:21:55.432 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Notify connected event to listeners.
15:21:55.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:55.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ece6b8a1-3195-419e-b70e-27ca6840d2c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cc02508ad8
15:21:55.559 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:58.756 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:21:58.757 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:58.757 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:58.900 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:59.508 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:21:59.509 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:21:59.509 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:22:06.268 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:22:08.863 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bffdea53-6170-4e3d-9437-3bfe7c5aeec8
15:22:08.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] RpcClient init label, labels = {module=naming, source=sdk}
15:22:08.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:22:08.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:22:08.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:22:08.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:22:08.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Success to connect to server [localhost:8848] on start up, connectionId = 1754205728873_127.0.0.1_13884
15:22:08.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Notify connected event to listeners.
15:22:08.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:22:08.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cc02508ad8
15:22:09.033 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:22:09.055 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:22:09.155 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.972 seconds (JVM running for 16.95)
15:22:09.167 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:22:09.168 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:22:09.168 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:22:09.257 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:22:09.567 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Receive server push request, request = NotifySubscriberRequest, requestId = 69
15:22:09.592 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bffdea53-6170-4e3d-9437-3bfe7c5aeec8] Ack server push request, request = NotifySubscriberRequest, requestId = 69
15:23:34.839 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:23:34.839 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:23:35.043 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:23:35.049 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:23:35.049 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:23:35.049 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:29:22.086 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:29:22.086 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:29:22.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:29:22.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@30fcf255[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:29:22.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754205728873_127.0.0.1_13884
15:29:22.417 [nacos-grpc-client-executor-100] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754205728873_127.0.0.1_13884]Ignore complete event,isRunning:false,isAbandon=false
15:29:22.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7f7522db[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 101]
15:29:22.555 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:29:22.557 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:29:22.557 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:29:22.557 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:29:22.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:29:22.559 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:29:29.864 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:29:32.431 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b5120841-7239-4272-8f9a-17a656ee4bda_config-0
15:29:32.596 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 67 ms to scan 1 urls, producing 3 keys and 6 values 
15:29:32.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
15:29:32.695 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
15:29:32.714 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
15:29:32.731 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
15:29:32.753 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
15:29:32.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:32.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001be0439ed38
15:29:32.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001be0439ef58
15:29:32.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:32.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:32.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:35.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754206175110_127.0.0.1_14439
15:29:35.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Notify connected event to listeners.
15:29:35.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:35.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5120841-7239-4272-8f9a-17a656ee4bda_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001be04518668
15:29:35.748 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:29:40.121 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:29:40.123 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:29:40.123 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:29:40.269 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:29:40.885 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:29:40.886 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:29:40.887 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:29:47.151 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:29:49.922 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b267094a-7046-42cd-be19-f31b2afb4e15
15:29:49.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] RpcClient init label, labels = {module=naming, source=sdk}
15:29:49.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:29:49.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:29:49.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:29:49.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:50.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Success to connect to server [localhost:8848] on start up, connectionId = 1754206189933_127.0.0.1_14461
15:29:50.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:50.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Notify connected event to listeners.
15:29:50.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001be04518668
15:29:50.092 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:29:50.116 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:29:50.223 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.537 seconds (JVM running for 23.746)
15:29:50.238 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:29:50.238 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:29:50.238 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:29:50.597 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Receive server push request, request = NotifySubscriberRequest, requestId = 77
15:29:50.609 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b267094a-7046-42cd-be19-f31b2afb4e15] Ack server push request, request = NotifySubscriberRequest, requestId = 77
15:29:50.790 [RMI TCP Connection(15)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:35:11.688 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:35:11.691 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:35:12.033 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:35:12.033 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@78f3849b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:35:12.033 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754206189933_127.0.0.1_14461
15:35:12.036 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754206189933_127.0.0.1_14461]Ignore complete event,isRunning:false,isAbandon=false
15:35:12.037 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@e4c6d4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 76]
15:35:12.180 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:35:12.180 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:35:12.185 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:35:12.185 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:35:16.887 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:35:17.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0
15:35:17.469 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
15:35:17.494 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:35:17.501 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:35:17.506 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 5 values 
15:35:17.512 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:35:17.518 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:35:17.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:35:17.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023e3c3ce8d8
15:35:17.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023e3c3ceaf8
15:35:17.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:35:17.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:35:17.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:35:18.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754206518020_127.0.0.1_14823
15:35:18.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Notify connected event to listeners.
15:35:18.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:35:18.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eac2497b-7f5d-403e-8c4b-dfc635248d36_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023e3c508440
15:35:18.280 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:35:20.676 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:35:20.676 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:35:20.676 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:35:20.787 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:35:21.209 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:35:21.210 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:35:21.210 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:35:26.375 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:35:28.555 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3a1a9d78-3cc5-434b-917e-3608e8b04a7c
15:35:28.555 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] RpcClient init label, labels = {module=naming, source=sdk}
15:35:28.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:35:28.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:35:28.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:35:28.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:35:28.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Success to connect to server [localhost:8848] on start up, connectionId = 1754206528566_127.0.0.1_14839
15:35:28.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:35:28.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023e3c508440
15:35:28.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Notify connected event to listeners.
15:35:28.724 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:35:28.747 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:35:28.836 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.434 seconds (JVM running for 13.274)
15:35:28.848 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:35:28.849 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:35:28.849 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:35:29.053 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:35:29.254 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Receive server push request, request = NotifySubscriberRequest, requestId = 82
15:35:29.270 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a1a9d78-3cc5-434b-917e-3608e8b04a7c] Ack server push request, request = NotifySubscriberRequest, requestId = 82
15:50:29.888 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:50:29.888 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:50:29.888 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:50:29.888 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:50:29.907 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:50:29.907 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:23:18.593 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:23:18.596 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:23:18.934 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:23:18.936 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3cc9bc53[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:23:18.936 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754206528566_127.0.0.1_14839
16:23:18.940 [nacos-grpc-client-executor-582] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754206528566_127.0.0.1_14839]Ignore complete event,isRunning:false,isAbandon=false
16:23:18.944 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1ee99700[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 583]
16:23:19.102 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:23:19.102 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:23:19.104 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:23:19.104 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:23:19.106 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:23:19.106 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:23:24.614 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:23:25.156 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0
16:23:25.204 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 6 values 
16:23:25.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 4 keys and 9 values 
16:23:25.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:23:25.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
16:23:25.252 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
16:23:25.262 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:23:25.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:23:25.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000259373be480
16:23:25.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000259373be6a0
16:23:25.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:23:25.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:23:25.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:23:25.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754209405762_127.0.0.1_4684
16:23:25.948 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Notify connected event to listeners.
16:23:25.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:23:25.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [50c9d854-f103-4b3a-940c-76c7b4fdfea5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000259374f8228
16:23:26.030 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:23:28.495 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:23:28.496 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:23:28.496 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:23:28.605 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:23:29.062 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:23:29.062 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:23:29.062 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:23:34.052 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:23:36.151 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e269157d-9041-47f0-ba85-b64e0a2abd4e
16:23:36.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] RpcClient init label, labels = {module=naming, source=sdk}
16:23:36.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:23:36.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:23:36.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:23:36.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:23:36.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Success to connect to server [localhost:8848] on start up, connectionId = 1754209416152_127.0.0.1_4695
16:23:36.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:23:36.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000259374f8228
16:23:36.273 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Notify connected event to listeners.
16:23:36.302 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:23:36.329 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:23:36.416 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.304 seconds (JVM running for 13.206)
16:23:36.422 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:23:36.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:23:36.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:23:36.800 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Receive server push request, request = NotifySubscriberRequest, requestId = 90
16:23:36.812 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e269157d-9041-47f0-ba85-b64e0a2abd4e] Ack server push request, request = NotifySubscriberRequest, requestId = 90
16:23:36.947 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:23:49.622 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:23:49.622 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:23:49.622 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:23:49.630 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:23:49.632 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:23:49.632 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:23:49.637 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:23:49.637 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:23:49.639 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:23:49.639 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:45:57.526 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:45:57.530 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:45:57.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:45:57.865 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@27fabb86[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:45:57.865 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754209416152_127.0.0.1_4695
18:45:57.867 [nacos-grpc-client-executor-1800] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754209416152_127.0.0.1_4695]Ignore complete event,isRunning:false,isAbandon=false
18:45:57.871 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4fcc1169[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1801]
18:45:58.030 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:45:58.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:45:58.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:45:58.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:45:58.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:45:58.032 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
