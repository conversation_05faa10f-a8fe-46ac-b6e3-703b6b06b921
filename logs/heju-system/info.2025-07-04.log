09:24:03.324 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:07.508 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0
09:24:07.903 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 145 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:08.115 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:08.150 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:08.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:08.241 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:08.294 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:08.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:08.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001900b39c730
09:24:08.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001900b39c950
09:24:08.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:08.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:08.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:11.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:12.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:12.065 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:12.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:12.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001900b4aa228
09:24:12.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:12.547 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:12.907 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:13.374 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:13.949 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:14.546 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:14.748 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:16.162 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:17.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:18.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:19.256 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:20.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:21.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:23.341 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:24.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.464 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.798 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:24:26.798 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:26.798 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:27.081 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:28.087 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:24:28.089 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:24:28.089 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:28.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:30.013 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:31.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:33.927 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:38.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:40.838 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:43.250 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:44.820 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:45.923 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:48.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:51.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:53.378 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 75550ce1-cee0-4586-a4ab-859447567047
09:24:53.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] RpcClient init label, labels = {module=naming, source=sdk}
09:24:53.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:53.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:53.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:53.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:53.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:53.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:53.537 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:53.537 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:53.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001900b4aa228
09:24:53.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:53.877 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:24:53.885 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f08bbcc0-45e3-4c8c-abea-7f4c3902d41e_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:54.887 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:54.887 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@14a204f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:54.887 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4dd4a4e[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:24:54.887 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75550ce1-cee0-4586-a4ab-859447567047] Client is shutdown, stop reconnect to server
09:24:54.896 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7c016efc-fc73-4591-943e-86e8130a5c5d
09:24:54.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] RpcClient init label, labels = {module=naming, source=sdk}
09:24:54.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:54.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:54.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:54.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:54.952 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:54.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:54.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001900b4aa228
09:24:55.083 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.308 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:55.352 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:24:55.361 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:24:55.380 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:24:55.380 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:24:55.415 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
09:24:55.415 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:24:55.460 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
09:24:55.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
09:24:55.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:56.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c016efc-fc73-4591-943e-86e8130a5c5d] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:26.764 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:28.101 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0669b388-db88-4583-8992-d7b863843a91_config-0
09:25:28.211 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
09:25:28.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:25:28.350 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:25:28.369 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
09:25:28.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 7 values 
09:25:28.424 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:25:28.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:28.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f8133b6d38
09:25:28.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f8133b6f58
09:25:28.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:28.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:28.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:29.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592329487_127.0.0.1_9820
09:25:29.759 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Notify connected event to listeners.
09:25:29.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:29.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0669b388-db88-4583-8992-d7b863843a91_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f8134f0ad8
09:25:29.955 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:25:35.739 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:25:35.740 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:25:35.740 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:25:35.974 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:25:36.962 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:25:36.966 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:25:36.966 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:25:48.366 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:25:54.991 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa196cd2-0398-482f-bceb-b95e7c1605a4
09:25:54.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] RpcClient init label, labels = {module=naming, source=sdk}
09:25:54.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:54.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:54.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:54.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:55.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Success to connect to server [localhost:8848] on start up, connectionId = 1751592355019_127.0.0.1_10272
09:25:55.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:55.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Notify connected event to listeners.
09:25:55.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f8134f0ad8
09:25:55.264 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:25:55.341 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:25:55.674 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.288 seconds (JVM running for 32.09)
09:25:55.710 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:25:55.711 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:25:55.713 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:25:55.713 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:25:55.741 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa196cd2-0398-482f-bceb-b95e7c1605a4] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:50:07.403 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:50:10.519 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:50:10.519 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:05:16.203 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:05:16.214 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:05:16.524 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:05:16.524 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6b48b4cb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:05:16.524 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592355019_127.0.0.1_10272
11:05:16.526 [nacos-grpc-client-executor-1199] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751592355019_127.0.0.1_10272]Ignore complete event,isRunning:false,isAbandon=false
11:05:16.531 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2008a27a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1200]
11:05:16.673 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:05:16.677 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:05:16.683 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:05:16.683 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:05:16.684 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:05:16.685 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:05:23.013 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:05:23.640 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0
11:05:23.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
11:05:23.723 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:05:23.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:05:23.736 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:05:23.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:05:23.761 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
11:05:23.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:05:23.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002712039ed38
11:05:23.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002712039ef58
11:05:23.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:05:23.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:05:23.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:05:24.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751598324329_127.0.0.1_5785
11:05:24.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Notify connected event to listeners.
11:05:24.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:05:24.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c63bfe6-4c28-469e-84dd-1db76f8dd589_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027120518ad8
11:05:24.634 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:05:27.429 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:05:27.430 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:05:27.430 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:05:27.587 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:05:28.167 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:05:28.171 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:05:28.172 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:05:34.062 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:05:36.625 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 63914c32-c228-4ac1-921a-c41d5b49f9a9
11:05:36.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] RpcClient init label, labels = {module=naming, source=sdk}
11:05:36.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:05:36.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:05:36.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:05:36.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:05:36.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Success to connect to server [localhost:8848] on start up, connectionId = 1751598336635_127.0.0.1_5872
11:05:36.758 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Notify connected event to listeners.
11:05:36.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:05:36.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027120518ad8
11:05:36.823 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:05:36.858 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:05:36.973 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.562 seconds (JVM running for 15.392)
11:05:37.004 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:05:37.005 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:05:37.005 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:05:37.444 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:05:37.463 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:05:37.888 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:05:56.732 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:05:56.732 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:42:03.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:42:03.689 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:42:04.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:42:04.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@120687fe[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:42:04.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751598336635_127.0.0.1_5872
11:42:04.254 [nacos-grpc-client-executor-445] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:42:04.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ff35073[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 445]
11:42:04.280 [nacos-grpc-client-executor-445] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63914c32-c228-4ac1-921a-c41d5b49f9a9] Ack server push request, request = NotifySubscriberRequest, requestId = 32
11:42:04.284 [nacos-grpc-client-executor-445] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751598336635_127.0.0.1_5872]Ignore complete event,isRunning:false,isAbandon=false
11:42:04.464 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:42:04.467 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:42:04.476 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:42:04.480 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:42:04.480 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:42:04.484 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:42:34.789 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:42:35.669 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0
11:42:35.770 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
11:42:35.819 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:42:35.832 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
11:42:35.844 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:42:35.856 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
11:42:35.868 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:42:35.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:42:35.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000191953b8b08
11:42:35.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000191953b8d28
11:42:35.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:42:35.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:42:35.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:42:37.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751600556977_127.0.0.1_14021
11:42:37.255 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Notify connected event to listeners.
11:42:37.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:42:37.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51b52ce3-3f20-4a95-a088-30b8f4059b68_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000191954f0668
11:42:37.530 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:42:44.120 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:42:44.124 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:42:44.125 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:42:44.343 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:42:45.198 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:42:45.201 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:42:45.201 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:42:55.641 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:43:00.072 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 63ac8df0-61ea-41f0-8e1f-8d3a18f5252d
11:43:00.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] RpcClient init label, labels = {module=naming, source=sdk}
11:43:00.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:43:00.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:43:00.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:43:00.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:00.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Success to connect to server [localhost:8848] on start up, connectionId = 1751600580088_127.0.0.1_14136
11:43:00.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Notify connected event to listeners.
11:43:00.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:00.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000191954f0668
11:43:00.326 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:43:00.372 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:43:00.535 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.948 seconds (JVM running for 29.91)
11:43:00.556 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:43:00.556 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:43:00.556 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:43:00.834 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Receive server push request, request = NotifySubscriberRequest, requestId = 35
11:43:00.875 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ac8df0-61ea-41f0-8e1f-8d3a18f5252d] Ack server push request, request = NotifySubscriberRequest, requestId = 35
11:44:27.509 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:44:28.737 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:44:28.737 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:55:24.399 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:55:24.446 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:55:24.808 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:55:24.808 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@750ff907[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:55:24.808 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600580088_127.0.0.1_14136
11:55:24.811 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@230bae4c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 157]
11:55:25.003 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:55:25.005 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:55:25.008 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:55:25.008 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:55:25.008 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:55:25.008 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:55:30.843 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:55:31.621 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5753952f-9c9d-4a69-909b-73912af9c90e_config-0
11:55:31.681 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
11:55:31.714 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:55:31.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:55:31.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:55:31.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:55:31.748 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
11:55:31.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:55:31.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001569339eaf8
11:55:31.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001569339ed18
11:55:31.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:55:31.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:55:31.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:55:32.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751601332405_127.0.0.1_3567
11:55:32.605 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Notify connected event to listeners.
11:55:32.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:55:32.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5753952f-9c9d-4a69-909b-73912af9c90e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015693518ad8
11:55:32.772 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:55:35.879 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:55:35.879 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:55:35.880 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:55:36.009 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:55:36.545 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:55:36.548 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:55:36.548 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:55:43.664 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:55:47.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ff6ff12e-27b4-41a6-91da-e49e9e9464de
11:55:47.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] RpcClient init label, labels = {module=naming, source=sdk}
11:55:47.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:55:47.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:55:47.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:55:47.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:55:47.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Success to connect to server [localhost:8848] on start up, connectionId = 1751601347671_127.0.0.1_3660
11:55:47.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:55:47.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015693518ad8
11:55:47.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Notify connected event to listeners.
11:55:47.998 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:55:48.038 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:55:48.211 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.94 seconds (JVM running for 18.724)
11:55:48.250 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:55:48.251 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:55:48.252 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:55:48.460 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Receive server push request, request = NotifySubscriberRequest, requestId = 40
11:55:48.483 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Ack server push request, request = NotifySubscriberRequest, requestId = 40
11:55:48.623 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:56:04.167 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:56:04.167 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:57:38.427 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:57:38.430 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:57:38.789 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:57:38.789 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26c31f2e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:57:38.789 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751601347671_127.0.0.1_3660
11:57:38.792 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7c08857a[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 32]
11:57:38.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff6ff12e-27b4-41a6-91da-e49e9e9464de] Notify disconnected event to listeners
11:57:38.953 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:57:38.956 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:57:38.963 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:57:38.964 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:57:38.965 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:57:38.965 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:57:44.603 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:57:45.319 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0
11:57:45.515 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 136 ms to scan 1 urls, producing 3 keys and 6 values 
11:57:45.556 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:57:45.564 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:57:45.572 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:57:45.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:57:45.588 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:57:45.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:57:45.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022b013ce8d8
11:57:45.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022b013ceaf8
11:57:45.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:57:45.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:57:45.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:57:46.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751601466191_127.0.0.1_4606
11:57:46.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Notify connected event to listeners.
11:57:46.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:57:46.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0c05365-45d3-4cba-a9d6-32dc1022776a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022b01508fb0
11:57:46.554 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:57:49.405 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:57:49.405 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:57:49.405 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:57:49.533 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:57:50.057 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:57:50.058 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:57:50.059 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:57:56.764 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:58:07.200 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c876f1b-07f0-47cd-9037-633efbbac0bf
11:58:07.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] RpcClient init label, labels = {module=naming, source=sdk}
11:58:07.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:58:07.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:58:07.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:58:07.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:58:07.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Success to connect to server [localhost:8848] on start up, connectionId = 1751601487225_127.0.0.1_4772
11:58:07.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:58:07.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022b01508fb0
11:58:07.353 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Notify connected event to listeners.
11:58:07.590 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:58:07.653 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:58:07.929 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.878 seconds (JVM running for 24.681)
11:58:07.961 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:58:07.962 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:58:07.970 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:58:07.995 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Receive server push request, request = NotifySubscriberRequest, requestId = 43
11:58:08.019 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c876f1b-07f0-47cd-9037-633efbbac0bf] Ack server push request, request = NotifySubscriberRequest, requestId = 43
11:58:08.190 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:58:45.707 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:58:45.707 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:59:27.430 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:59:27.435 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:59:27.750 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:59:27.750 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@20b3183d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:59:27.750 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751601487225_127.0.0.1_4772
11:59:27.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@241e135b[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 24]
11:59:27.917 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:59:27.919 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:59:27.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:59:27.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:59:27.932 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:59:27.933 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:59:33.702 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:59:34.435 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0
11:59:34.504 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:59:34.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
11:59:34.548 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
11:59:34.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:59:34.569 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:59:34.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:59:34.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:59:34.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c31139dd70
11:59:34.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c31139df90
11:59:34.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:59:34.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:59:34.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:59:35.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751601575291_127.0.0.1_5294
11:59:35.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Notify connected event to listeners.
11:59:35.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:59:35.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1f38f21-52c4-40f8-a96e-d3fad69ee2a7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c311517cb0
11:59:35.640 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:59:39.195 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:59:39.196 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:59:39.196 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:59:39.359 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:59:40.041 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:59:40.043 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:59:40.043 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:59:50.549 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:59:54.022 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9fc7c204-65eb-433c-90c6-b25d60b66e99
11:59:54.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] RpcClient init label, labels = {module=naming, source=sdk}
11:59:54.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:59:54.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:59:54.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:59:54.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:59:54.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Success to connect to server [localhost:8848] on start up, connectionId = 1751601594046_127.0.0.1_5372
11:59:54.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:59:54.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c311517cb0
11:59:54.166 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Notify connected event to listeners.
11:59:54.232 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:59:54.404 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:59:54.543 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.404 seconds (JVM running for 22.197)
11:59:54.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:59:54.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:59:54.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:59:54.756 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Receive server push request, request = NotifySubscriberRequest, requestId = 49
11:59:54.775 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fc7c204-65eb-433c-90c6-b25d60b66e99] Ack server push request, request = NotifySubscriberRequest, requestId = 49
11:59:54.857 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:00:09.986 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:00:09.986 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:09.994 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:00:10.036 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
12:00:09.999 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:00:10.037 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
12:00:10.046 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:00:10.046 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.050 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:00:10.050 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:00:10.050 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.050 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
12:00:10.057 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
12:00:10.057 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.067 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-10} inited
12:00:10.067 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
12:00:10.069 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
12:00:10.069 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.070 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
12:00:10.072 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-9} inited
12:00:10.072 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-10} closing ...
12:00:10.072 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-10} closed
12:00:10.072 [http-nio-9600-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.074 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
12:00:10.074 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
12:00:10.074 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.077 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
12:00:10.077 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-9} closing ...
12:00:10.077 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-9} closed
12:00:10.077 [http-nio-9600-exec-11] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-8} inited
12:00:10.077 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.077 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-12} inited
12:00:10.082 [http-nio-9600-exec-11] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
12:00:10.082 [http-nio-9600-exec-11] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
12:00:10.082 [http-nio-9600-exec-11] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.082 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-8} closing ...
12:00:10.082 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-8} closed
12:00:10.082 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.094 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-13} inited
12:00:10.094 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-12} closing ...
12:00:10.099 [http-nio-9600-exec-12] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-11} inited
12:00:10.099 [http-nio-9600-exec-13] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-14} inited
12:00:10.099 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-12} closed
12:00:10.102 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.102 [http-nio-9600-exec-13] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-13} closing ...
12:00:10.107 [http-nio-9600-exec-13] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-13} closed
12:00:10.107 [http-nio-9600-exec-13] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:10.107 [http-nio-9600-exec-12] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-14} closing ...
12:00:10.111 [http-nio-9600-exec-12] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-14} closed
12:00:10.111 [http-nio-9600-exec-12] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:01:58.729 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:01:58.733 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:01:59.120 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:01:59.120 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7f00636c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:01:59.120 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751601594046_127.0.0.1_5372
12:01:59.132 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7564915[Running, pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 36]
12:01:59.136 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751601594046_127.0.0.1_5372]Ignore complete event,isRunning:false,isAbandon=false
12:01:59.290 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:01:59.291 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-11} closing ...
12:01:59.294 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-11} closed
12:01:59.294 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:01:59.295 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:01:59.295 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:02:04.397 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:02:05.008 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6d54b7af-50a1-4cf3-999a-f069badd3417_config-0
12:02:05.059 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
12:02:05.087 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
12:02:05.094 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
12:02:05.101 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
12:02:05.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
12:02:05.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:02:05.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:02:05.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000186b839ed38
12:02:05.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000186b839ef58
12:02:05.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:02:05.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:02:05.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:02:06.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751601725832_127.0.0.1_5947
12:02:06.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:02:06.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Notify connected event to listeners.
12:02:06.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d54b7af-50a1-4cf3-999a-f069badd3417_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000186b8518ad8
12:02:06.276 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:02:09.439 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:02:09.439 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:02:09.439 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:02:09.618 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:02:10.269 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:02:10.270 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:02:10.271 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:02:18.976 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:02:21.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f033b9ad-b806-41ec-ac78-5d3e8345ecd8
12:02:21.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] RpcClient init label, labels = {module=naming, source=sdk}
12:02:21.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:02:21.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:02:21.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:02:21.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:02:21.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Success to connect to server [localhost:8848] on start up, connectionId = 1751601741752_127.0.0.1_6025
12:02:21.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:02:21.864 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Notify connected event to listeners.
12:02:21.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000186b8518ad8
12:02:21.927 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:02:21.974 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:02:22.110 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.235 seconds (JVM running for 19.009)
12:02:22.123 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:02:22.123 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:02:22.123 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:02:22.326 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:02:22.486 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Receive server push request, request = NotifySubscriberRequest, requestId = 55
12:02:22.500 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f033b9ad-b806-41ec-ac78-5d3e8345ecd8] Ack server push request, request = NotifySubscriberRequest, requestId = 55
12:02:24.322 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:02:24.323 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:02:24.324 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:02:24.328 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:02:24.341 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:02:24.341 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:28:39.265 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:28:39.267 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:28:39.604 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:28:39.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@65d500e6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:28:39.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751601741752_127.0.0.1_6025
12:28:39.608 [nacos-grpc-client-executor-327] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751601741752_127.0.0.1_6025]Ignore complete event,isRunning:false,isAbandon=false
12:28:39.617 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@329dd5e0[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 328]
12:28:39.767 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:28:39.767 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:28:39.774 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:28:39.774 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:28:39.777 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:28:39.777 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:28:44.722 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:28:45.359 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0
12:28:45.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
12:28:45.441 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
12:28:45.447 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:28:45.454 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
12:28:45.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
12:28:45.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:28:45.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:28:45.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002323f3aeaf8
12:28:45.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002323f3aed18
12:28:45.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:28:45.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:28:45.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:28:46.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751603326105_127.0.0.1_11713
12:28:46.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:28:46.299 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Notify connected event to listeners.
12:28:46.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf00f767-dd77-4a10-a8be-fe9a151a922d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002323f508ad8
12:28:46.415 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:28:49.404 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:28:49.405 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:28:49.406 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:28:49.535 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:28:50.157 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:28:50.159 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:28:50.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:28:56.697 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:28:59.350 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5df98571-c43d-4e8d-921f-0f7291119b3d
12:28:59.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] RpcClient init label, labels = {module=naming, source=sdk}
12:28:59.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:28:59.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:28:59.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:28:59.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:28:59.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Success to connect to server [localhost:8848] on start up, connectionId = 1751603339362_127.0.0.1_11816
12:28:59.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:28:59.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002323f508ad8
12:28:59.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Notify connected event to listeners.
12:28:59.644 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:28:59.671 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:28:59.775 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.6 seconds (JVM running for 16.439)
12:28:59.787 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:28:59.787 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:28:59.788 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:29:00.077 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Receive server push request, request = NotifySubscriberRequest, requestId = 60
12:29:00.098 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5df98571-c43d-4e8d-921f-0f7291119b3d] Ack server push request, request = NotifySubscriberRequest, requestId = 60
12:29:00.167 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:29:45.520 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:29:45.524 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:29:45.532 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:29:45.532 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
12:29:45.532 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:29:45.536 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
12:29:45.545 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
12:29:45.545 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:29:45.545 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:29:45.545 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:29:45.549 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:29:45.549 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:29:45.549 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
12:29:45.549 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
12:29:45.549 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:29:45.549 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
12:29:45.549 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
12:29:45.549 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:29:45.553 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
12:29:45.553 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
12:29:45.553 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
12:29:45.553 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:34:11.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:34:11.003 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:34:11.343 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:34:11.343 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@728361dc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:34:11.343 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751603339362_127.0.0.1_11816
12:34:11.346 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751603339362_127.0.0.1_11816]Ignore complete event,isRunning:false,isAbandon=false
12:34:11.349 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3443d361[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 76]
12:34:11.496 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:34:11.498 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
12:34:11.498 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
12:34:11.498 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:34:11.500 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:34:11.500 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:34:16.855 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:34:17.482 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-a1fc-4c71-83c7-ab267e83d6fd_config-0
12:34:17.548 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
12:34:17.578 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
12:34:17.585 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:34:17.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
12:34:17.598 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
12:34:17.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:34:17.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:34:17.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001de4339dd70
12:34:17.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001de4339df90
12:34:17.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:34:17.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:34:17.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:34:18.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751603658205_127.0.0.1_13263
12:34:18.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Notify connected event to listeners.
12:34:18.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:34:18.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001de43517d88
12:34:18.510 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:34:21.196 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:34:21.197 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:34:21.197 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:34:21.319 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:34:21.868 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:34:21.871 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:34:21.871 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:34:27.379 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:34:29.793 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 702c3213-5309-4f80-b073-9712c570dc5b
12:34:29.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] RpcClient init label, labels = {module=naming, source=sdk}
12:34:29.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:34:29.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:34:29.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:34:29.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:34:29.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Success to connect to server [localhost:8848] on start up, connectionId = 1751603669803_127.0.0.1_13363
12:34:29.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:34:29.926 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Notify connected event to listeners.
12:34:29.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001de43517d88
12:34:29.993 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:34:30.094 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:34:30.182 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.851 seconds (JVM running for 14.622)
12:34:30.194 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:34:30.194 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:34:30.195 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:34:30.399 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:34:30.517 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Receive server push request, request = NotifySubscriberRequest, requestId = 64
12:34:30.531 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Ack server push request, request = NotifySubscriberRequest, requestId = 64
13:04:44.615 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:44.615 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Server healthy check fail, currentConnection = 1751603669803_127.0.0.1_13363
13:04:44.615 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:54.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Success to connect a server [localhost:8848], connectionId = 1751605494244_127.0.0.1_6554
13:04:54.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Abandon prev connection, server is localhost:8848, connectionId is 1751603669803_127.0.0.1_13363
13:04:54.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751603669803_127.0.0.1_13363
13:04:54.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Notify disconnected event to listeners
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Notify connected event to listeners.
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Success to connect a server [localhost:8848], connectionId = 1751605494252_127.0.0.1_6555
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751603658205_127.0.0.1_13263
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751603658205_127.0.0.1_13263
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Notify disconnected event to listeners
13:04:54.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-a1fc-4c71-83c7-ab267e83d6fd_config-0] Notify connected event to listeners.
13:04:59.852 [nacos-grpc-client-executor-376] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Receive server push request, request = NotifySubscriberRequest, requestId = 79
13:04:59.866 [nacos-grpc-client-executor-376] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Ack server push request, request = NotifySubscriberRequest, requestId = 79
13:20:39.537 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:20:39.539 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:21:16.703 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:21:16.707 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:21:17.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:21:17.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@566fa80d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:21:17.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751605494244_127.0.0.1_6554
13:21:17.100 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@51d7dfc1[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 572]
13:21:17.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [702c3213-5309-4f80-b073-9712c570dc5b] Notify disconnected event to listeners
13:21:17.261 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:21:17.265 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:21:17.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:21:17.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:21:17.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:21:17.274 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:21:20.641 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:21:21.256 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0
13:21:21.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
13:21:21.347 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:21:21.353 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:21:21.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:21:21.377 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
13:21:21.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
13:21:21.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:21:21.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d1673bdd70
13:21:21.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d1673bdf90
13:21:21.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:21:21.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:21:21.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:21:22.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751606481967_127.0.0.1_9519
13:21:22.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:21:22.152 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Notify connected event to listeners.
13:21:22.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [356a8cd8-5730-486e-a921-cbfdfb403ab2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d1674f7d88
13:21:22.300 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:21:24.949 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:21:24.949 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:21:24.949 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:21:25.090 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:21:25.602 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:21:25.604 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:21:25.605 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:21:31.823 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:21:34.291 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-f029-4c85-9b70-0b9e569836fa
13:21:34.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] RpcClient init label, labels = {module=naming, source=sdk}
13:21:34.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:21:34.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:21:34.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:21:34.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:21:34.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Success to connect to server [localhost:8848] on start up, connectionId = 1751606494302_127.0.0.1_9577
13:21:34.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:21:34.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Notify connected event to listeners.
13:21:34.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d1674f7d88
13:21:34.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:21:34.514 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:21:34.624 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.58 seconds (JVM running for 15.647)
13:21:34.638 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:21:34.638 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:21:34.640 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:21:35.244 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Receive server push request, request = NotifySubscriberRequest, requestId = 84
13:21:35.262 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-f029-4c85-9b70-0b9e569836fa] Ack server push request, request = NotifySubscriberRequest, requestId = 84
13:21:35.287 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:21:57.108 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:21:57.108 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:09:03.500 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:03.504 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:03.871 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:03.871 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1ccf7498[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:03.873 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751606494302_127.0.0.1_9577
15:09:03.877 [nacos-grpc-client-executor-1300] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751606494302_127.0.0.1_9577]Ignore complete event,isRunning:false,isAbandon=false
15:09:03.906 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7c06fac0[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1301]
15:09:04.161 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:09:04.170 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:09:04.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:09:04.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:09:04.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:09:04.201 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
