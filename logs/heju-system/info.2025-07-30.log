09:01:55.822 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:01:56.732 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0
09:01:56.837 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 47 ms to scan 1 urls, producing 3 keys and 6 values 
09:01:56.889 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:01:56.899 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:01:56.909 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:01:56.920 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:01:56.930 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:01:56.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:01:56.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000017bbb3b6d38
09:01:56.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000017bbb3b6f58
09:01:56.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:01:56.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:01:56.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:01:58.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753837318074_127.0.0.1_10362
09:01:58.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Notify connected event to listeners.
09:01:58.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:01:58.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4193f083-f3f9-4f0d-b052-3751fe9fd61a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017bbb4f0668
09:01:58.460 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:02:05.109 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:02:05.111 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:02:05.111 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:02:05.422 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:02:06.911 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:02:06.913 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:02:06.914 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:02:18.725 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:02:21.920 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e3bca1c-0f57-490e-b075-e76c878a4da0
09:02:21.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] RpcClient init label, labels = {module=naming, source=sdk}
09:02:21.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:02:21.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:02:21.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:02:21.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:22.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Success to connect to server [localhost:8848] on start up, connectionId = 1753837341933_127.0.0.1_10544
09:02:22.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:22.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Notify connected event to listeners.
09:02:22.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017bbb4f0668
09:02:22.137 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:02:22.177 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:02:22.318 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.509 seconds (JVM running for 29.428)
09:02:22.335 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:02:22.335 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:02:22.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:02:22.627 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:02:22.640 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:04:07.945 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:04:09.516 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:04:09.517 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3bca1c-0f57-490e-b075-e76c878a4da0] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:04:09.663 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:04:09.664 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:04:09.854 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:04:09.854 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:06:59.368 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:06:59.372 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:06:59.704 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:06:59.705 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7a0e0bb8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:06:59.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753837341933_127.0.0.1_10544
09:06:59.708 [nacos-grpc-client-executor-72] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753837341933_127.0.0.1_10544]Ignore complete event,isRunning:false,isAbandon=false
09:06:59.710 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@92ffd3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 73]
09:06:59.852 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:06:59.855 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:06:59.861 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:06:59.861 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:06:59.862 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:06:59.862 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:06:59.863 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:06:59.863 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:07:04.294 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:07:04.838 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9086396e-d604-43ad-a28c-2be1fb18a599_config-0
09:07:04.889 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
09:07:04.915 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
09:07:04.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:07:04.928 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:07:04.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
09:07:04.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
09:07:04.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:07:04.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000237243ce8d8
09:07:04.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000237243ceaf8
09:07:04.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:07:04.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:07:04.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:05.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753837625458_127.0.0.1_12280
09:07:05.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Notify connected event to listeners.
09:07:05.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:05.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9086396e-d604-43ad-a28c-2be1fb18a599_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023724508fd0
09:07:05.715 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:07:08.065 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:07:08.067 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:07:08.067 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:07:08.177 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:07:08.677 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:07:08.679 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:07:08.679 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:07:13.741 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:07:15.849 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 267e8a6f-b708-4871-ac12-3dbfbab48024
09:07:15.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] RpcClient init label, labels = {module=naming, source=sdk}
09:07:15.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:07:15.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:07:15.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:07:15.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:15.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Success to connect to server [localhost:8848] on start up, connectionId = 1753837635857_127.0.0.1_12337
09:07:15.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:15.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023724508fd0
09:07:15.971 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Notify connected event to listeners.
09:07:16.019 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:07:16.041 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:07:16.136 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.334 seconds (JVM running for 13.343)
09:07:16.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:07:16.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:07:16.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:07:16.574 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:07:16.588 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [267e8a6f-b708-4871-ac12-3dbfbab48024] Ack server push request, request = NotifySubscriberRequest, requestId = 21
09:07:16.612 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:10:39.868 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:10:39.869 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:10:39.869 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:10:39.874 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:10:39.886 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:10:39.887 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:20:23.382 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:23.430 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:23.808 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:23.810 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3075a5dd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:23.811 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753837635857_127.0.0.1_12337
19:20:23.820 [nacos-grpc-client-executor-7361] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753837635857_127.0.0.1_12337]Ignore complete event,isRunning:false,isAbandon=false
19:20:23.852 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5190d387[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7362]
19:20:24.268 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:20:24.276 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:20:24.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:20:24.284 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:20:24.290 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:20:24.290 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:24:23.065 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:24:25.522 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0
19:24:25.768 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 104 ms to scan 1 urls, producing 3 keys and 6 values 
19:24:25.878 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
19:24:25.904 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
19:24:25.928 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
19:24:25.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
19:24:25.966 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
19:24:25.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:24:25.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002784f3b9030
19:24:25.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002784f3b9250
19:24:25.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:24:25.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:24:25.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:24:28.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753874667812_127.0.0.1_6170
19:24:28.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Notify connected event to listeners.
19:24:28.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:24:28.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [848ea05c-6cf1-46fd-a8a0-8d6c81b8ccb5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002784f4f0fb0
19:24:28.438 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:24:35.997 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:24:35.998 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:24:35.999 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:24:36.287 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:24:37.970 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:24:37.974 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:24:37.974 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:24:56.964 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:25:02.251 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f8e6c59-dd95-4154-a310-c167dbccf46e
19:25:02.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] RpcClient init label, labels = {module=naming, source=sdk}
19:25:02.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:25:02.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:25:02.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:25:02.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:25:02.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Success to connect to server [localhost:8848] on start up, connectionId = 1753874702279_127.0.0.1_6570
19:25:02.425 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Notify connected event to listeners.
19:25:02.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:25:02.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002784f4f0fb0
19:25:02.551 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:25:02.662 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:25:03.031 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Receive server push request, request = NotifySubscriberRequest, requestId = 33
19:25:03.063 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Ack server push request, request = NotifySubscriberRequest, requestId = 33
19:25:03.267 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 41.888 seconds (JVM running for 45.496)
19:25:03.321 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:25:03.322 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:25:03.324 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:25:45.392 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:25:50.167 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Receive server push request, request = NotifySubscriberRequest, requestId = 36
19:25:50.168 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f8e6c59-dd95-4154-a310-c167dbccf46e] Ack server push request, request = NotifySubscriberRequest, requestId = 36
19:25:52.605 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:25:52.606 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:25:52.682 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:25:52.690 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:25:52.710 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:25:52.710 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
