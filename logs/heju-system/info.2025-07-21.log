09:04:13.502 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:04:14.614 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0
09:04:14.731 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:04:14.802 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:04:14.815 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:04:14.829 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:04:14.842 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:04:14.860 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:04:14.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:14.867 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002a0293b8b08
09:04:14.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002a0293b8d28
09:04:14.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:14.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:14.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:16.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059856367_127.0.0.1_1638
09:04:16.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Notify connected event to listeners.
09:04:16.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:16.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2da1501c-6495-47dc-a7a3-65c9be109dbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002a0294f0ad8
09:04:17.195 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:04:27.410 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:04:27.411 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:04:27.411 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:04:27.726 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:04:29.269 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:04:29.271 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:04:29.272 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:04:41.451 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:04:44.855 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1971def2-c6c4-4def-9c4d-d8ef51b77a09
09:04:44.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] RpcClient init label, labels = {module=naming, source=sdk}
09:04:44.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:04:44.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:04:44.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:04:44.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:44.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Success to connect to server [localhost:8848] on start up, connectionId = 1753059884867_127.0.0.1_1734
09:04:44.989 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Notify connected event to listeners.
09:04:44.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:44.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002a0294f0ad8
09:04:45.066 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:04:45.109 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:04:45.257 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.646 seconds (JVM running for 36.469)
09:04:45.275 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:04:45.276 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:04:45.276 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:04:45.611 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:04:45.633 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:06:50.813 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:06:54.351 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:06:54.352 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:06:55.221 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:06:55.221 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:06:55.525 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:06:55.525 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
15:36:23.562 [nacos-grpc-client-executor-4709] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Receive server push request, request = NotifySubscriberRequest, requestId = 20
15:36:23.562 [nacos-grpc-client-executor-4709] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1971def2-c6c4-4def-9c4d-d8ef51b77a09] Ack server push request, request = NotifySubscriberRequest, requestId = 20
17:57:13.999 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:14.007 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:14.329 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:14.329 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@410e7ea8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:14.329 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753059884867_127.0.0.1_1734
17:57:14.333 [nacos-grpc-client-executor-6413] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753059884867_127.0.0.1_1734]Ignore complete event,isRunning:false,isAbandon=false
17:57:14.339 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@77b15583[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6414]
17:57:14.541 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:57:14.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:57:14.555 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:57:14.555 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:57:14.558 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:57:14.558 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:57:14.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:57:14.559 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
