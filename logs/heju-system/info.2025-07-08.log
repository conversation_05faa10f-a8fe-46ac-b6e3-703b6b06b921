09:36:23.553 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:24.681 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d47c10c-a508-4629-b209-0db016ba1e45_config-0
09:36:24.773 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 3 keys and 6 values 
09:36:24.829 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:36:24.836 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:36:24.845 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:36:24.852 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 1 keys and 7 values 
09:36:24.864 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:36:24.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:24.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001340a39f410
09:36:24.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001340a39f630
09:36:24.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:24.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:24.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:25.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:25.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:25.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:25.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:25.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001340a4e9650
09:36:25.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:26.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:26.442 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:26.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:27.389 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:27.864 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:36:28.013 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:28.734 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:29.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:30.462 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:31.478 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:32.673 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:33.365 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:36:33.366 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:36:33.366 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:36:33.520 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:36:33.955 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:34.364 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:36:34.365 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:36:34.365 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:36:35.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:36.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.429 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:40.112 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:41.949 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:43.980 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:45.013 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:36:46.011 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:48.128 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:50.363 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:51.221 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 826e77b7-0071-4991-9993-6ec16e056c62
09:36:51.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] RpcClient init label, labels = {module=naming, source=sdk}
09:36:51.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:51.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:51.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:51.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:51.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:51.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:51.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:51.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:51.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001340a4e9650
09:36:51.403 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:51.599 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:36:51.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:51.947 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:52.374 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:52.586 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d47c10c-a508-4629-b209-0db016ba1e45_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:52.610 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:36:52.611 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2e006f67[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:36:52.611 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@63eebb56[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:36:52.611 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [826e77b7-0071-4991-9993-6ec16e056c62] Client is shutdown, stop reconnect to server
09:36:52.616 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c60fe93-a199-440c-8aea-62a0032de893
09:36:52.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] RpcClient init label, labels = {module=naming, source=sdk}
09:36:52.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:52.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:52.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:52.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:52.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:52.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:52.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:52.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:52.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001340a4e9650
09:36:52.844 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:53.066 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:36:53.068 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:53.069 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:36:53.080 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:36:53.080 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:36:53.099 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
09:36:53.100 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:36:53.126 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
09:36:53.131 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
09:36:53.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:53.827 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c60fe93-a199-440c-8aea-62a0032de893] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.571 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:45:42.959 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 338d9408-e9ed-4465-9e63-378959b925e8_config-0
09:45:43.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
09:45:43.111 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:45:43.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:45:43.134 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:45:43.149 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:45:43.162 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:45:43.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:45:43.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002428139e8d8
09:45:43.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002428139eaf8
09:45:43.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:45:43.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:45:43.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:44.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:44.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:44.681 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:44.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:44.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000242814e9000
09:45:44.833 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.381 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:46.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:46.799 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:45:47.070 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:47.791 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:48.636 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:49.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:50.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:51.764 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:53.305 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:55.510 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:56.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:45:56.088 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:45:56.089 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:45:57.083 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:57.095 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:45:58.352 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:45:58.354 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:45:58.354 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:45:58.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:00.813 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:03.071 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:05.341 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:07.397 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:09.710 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:12.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:14.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:16.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:16.774 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:46:19.250 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:21.298 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 85c14cd3-4828-44bf-801e-6a7e6b42b521
09:46:21.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] RpcClient init label, labels = {module=naming, source=sdk}
09:46:21.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:46:21.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:46:21.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:46:21.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:21.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:21.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:21.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:46:21.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:46:21.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000242814e9000
09:46:21.522 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:21.739 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:46:21.765 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:21.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [338d9408-e9ed-4465-9e63-378959b925e8_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:22.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:22.525 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:22.744 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:46:22.745 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1928dc66[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:46:22.745 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3dad8926[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:46:22.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85c14cd3-4828-44bf-801e-6a7e6b42b521] Client is shutdown, stop reconnect to server
09:46:22.747 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7a377dc1-0a99-4c5e-b9e7-eede795f5c8e
09:46:22.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] RpcClient init label, labels = {module=naming, source=sdk}
09:46:22.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:46:22.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:46:22.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:46:22.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:22.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:22.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:46:22.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:46:22.810 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:46:22.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000242814e9000
09:46:22.935 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:23.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:23.189 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:46:23.192 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:46:23.203 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:46:23.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:46:23.224 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
09:46:23.224 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:46:23.250 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
09:46:23.255 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
09:46:23.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:46:23.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a377dc1-0a99-4c5e-b9e7-eede795f5c8e] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.662 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:15.402 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2d92e655-df0a-40de-bafb-2f9a39304db5_config-0
10:08:15.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:15.502 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:15.512 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:15.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:15.535 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:15.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:15.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:15.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ff603b5d00
10:08:15.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ff603b5f20
10:08:15.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:15.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:15.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:16.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:16.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:16.525 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:16.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:16.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff604c5b68
10:08:16.646 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.867 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:17.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:17.613 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:18.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:18.582 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:08:18.747 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:19.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:20.294 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:21.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:22.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:23.512 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:23.517 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:08:23.518 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:08:23.518 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:08:23.688 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:08:24.468 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:08:24.470 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:08:24.471 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:08:24.764 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:26.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:27.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:29.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:30.945 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:32.711 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:33.275 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:08:34.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:36.537 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:36.841 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9e2f5afc-2596-4d71-a38c-0cb34998c690
10:08:36.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] RpcClient init label, labels = {module=naming, source=sdk}
10:08:36.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:36.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:36.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:36.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:36.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:36.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:36.864 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:36.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:36.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff604c5b68
10:08:37.013 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:37.202 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:08:37.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:37.554 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:37.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:38.207 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:08:38.207 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@536e3aab[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:08:38.207 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@14d3bee8[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
10:08:38.209 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e2f5afc-2596-4d71-a38c-0cb34998c690] Client is shutdown, stop reconnect to server
10:08:38.212 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2fbd25b-638f-41f8-898e-8a18c7a432e4
10:08:38.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] RpcClient init label, labels = {module=naming, source=sdk}
10:08:38.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:38.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:38.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:38.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:38.717 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d92e655-df0a-40de-bafb-2f9a39304db5_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:38.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:38.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:38.764 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:38.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:38.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff604c5b68
10:08:39.166 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:08:39.166 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:08:39.182 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:08:39.182 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:08:39.215 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
10:08:39.215 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:08:39.253 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
10:08:39.259 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
10:08:39.265 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:39.503 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:39.836 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2fbd25b-638f-41f8-898e-8a18c7a432e4] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:47.838 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:48.735 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 808bb7e3-454a-4d61-b6da-2963256b32ac_config-0
10:08:48.826 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:48.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:48.876 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:48.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:48.896 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:48.908 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:48.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:48.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001829539f1c0
10:08:48.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001829539f3e0
10:08:48.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:48.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:48.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:50.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751940529766_127.0.0.1_7806
10:08:50.002 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Notify connected event to listeners.
10:08:50.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:50.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [808bb7e3-454a-4d61-b6da-2963256b32ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018295518668
10:08:50.227 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:08:55.231 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:08:55.232 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:08:55.233 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:08:55.527 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:08:56.485 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:08:56.487 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:08:56.487 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:09:06.405 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:09:09.963 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a713dc6b-d4b8-487d-89cf-35e47bb1d4e3
10:09:09.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] RpcClient init label, labels = {module=naming, source=sdk}
10:09:09.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:09:09.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:09:09.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:09:09.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:10.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Success to connect to server [localhost:8848] on start up, connectionId = 1751940549981_127.0.0.1_8075
10:09:10.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:10.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Notify connected event to listeners.
10:09:10.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018295518668
10:09:10.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:09:10.234 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:09:10.383 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.27 seconds (JVM running for 24.339)
10:09:10.400 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:09:10.400 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:09:10.400 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:09:10.701 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:09:10.724 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a713dc6b-d4b8-487d-89cf-35e47bb1d4e3] Ack server push request, request = NotifySubscriberRequest, requestId = 9
10:09:10.754 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:49:26.743 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:49:26.745 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:11:25.134 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:11:25.138 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:11:25.469 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:11:25.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5270b0d7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:11:25.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751940549981_127.0.0.1_8075
13:11:25.472 [nacos-grpc-client-executor-2202] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751940549981_127.0.0.1_8075]Ignore complete event,isRunning:false,isAbandon=false
13:11:25.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25fdfc7d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2203]
13:11:25.646 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:11:25.649 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:11:25.658 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:11:25.658 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:11:25.659 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:11:25.659 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:52:37.907 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:52:39.904 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0
15:52:40.021 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
15:52:40.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
15:52:40.100 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:52:40.113 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:52:40.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:52:40.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
15:52:40.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:52:40.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000292da39c730
15:52:40.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000292da39c950
15:52:40.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:52:40.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:52:40.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:41.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:41.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:41.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:41.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:41.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000292da4aa840
15:52:41.371 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:41.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:41.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:42.371 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:42.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:43.336 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:52:43.506 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:44.249 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:45.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.242 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:49.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:49.736 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:52:49.737 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:52:49.737 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:52:49.952 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:52:50.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:50.986 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:52:50.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:52:50.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:52:52.396 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:53.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:56.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:58.100 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:59.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:02.028 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:03.057 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:53:04.146 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:06.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:07.803 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 039310f1-c0ab-4620-b216-1f3c428cf751
15:53:07.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] RpcClient init label, labels = {module=naming, source=sdk}
15:53:07.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:53:07.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:53:07.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:53:07.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:07.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:07.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:07.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:07.884 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:53:07.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000292da4aa840
15:53:08.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:08.206 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:53:08.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:08.536 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24d56bd1-7a33-424a-afcf-fb761ecd2352_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:08.564 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:08.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:09.208 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:53:09.208 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@686a08f4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:53:09.211 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60b90fd3[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
15:53:09.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [039310f1-c0ab-4620-b216-1f3c428cf751] Client is shutdown, stop reconnect to server
15:53:09.215 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e7669a5e-b1f4-490b-895a-52eacfee939a
15:53:09.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] RpcClient init label, labels = {module=naming, source=sdk}
15:53:09.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:53:09.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:53:09.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:53:09.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:09.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:09.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:09.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:09.237 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:53:09.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000292da4aa840
15:53:09.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:09.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:09.602 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:53:09.606 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:53:09.617 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:53:09.617 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:53:09.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
15:53:09.690 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:53:09.718 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
15:53:09.723 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
15:53:09.876 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:10.305 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7669a5e-b1f4-490b-895a-52eacfee939a] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:25.281 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:53:26.234 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0
15:53:26.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
15:53:26.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:53:26.371 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
15:53:26.381 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
15:53:26.391 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
15:53:26.404 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:53:26.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:53:26.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b3013beaf8
15:53:26.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b3013bed18
15:53:26.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:53:26.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:53:26.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:27.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751961207219_127.0.0.1_6385
15:53:27.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Notify connected event to listeners.
15:53:27.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:27.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ace9d565-f982-4d28-9ffc-d68b50632f4f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b3014f8ad8
15:53:27.695 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:53:32.538 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:53:32.538 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:53:32.539 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:53:32.800 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:53:33.828 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:53:33.829 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:53:33.830 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:53:44.543 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:53:47.976 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a381fd83-5969-45d9-9e92-3434ac427ce9
15:53:47.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] RpcClient init label, labels = {module=naming, source=sdk}
15:53:47.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:53:47.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:53:47.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:53:47.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:48.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Success to connect to server [localhost:8848] on start up, connectionId = 1751961227988_127.0.0.1_6609
15:53:48.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:48.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Notify connected event to listeners.
15:53:48.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b3014f8ad8
15:53:48.172 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:53:48.216 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:53:48.353 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.801 seconds (JVM running for 24.975)
15:53:48.367 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:53:48.368 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:53:48.369 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:53:48.691 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Receive server push request, request = NotifySubscriberRequest, requestId = 8
15:53:48.718 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:53:48.718 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a381fd83-5969-45d9-9e92-3434ac427ce9] Ack server push request, request = NotifySubscriberRequest, requestId = 8
16:19:17.435 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:19:17.436 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
16:21:08.427 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:21:08.429 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:52:45.319 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:52:45.319 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:52:45.657 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:52:45.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@14b65d63[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:52:45.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751961227988_127.0.0.1_6609
17:52:45.661 [nacos-grpc-client-executor-1445] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751961227988_127.0.0.1_6609]Ignore complete event,isRunning:false,isAbandon=false
17:52:45.666 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1446]
17:52:45.823 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:52:45.823 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:52:45.839 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:52:45.839 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:52:45.842 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:52:45.842 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:52:45.845 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:52:45.845 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
