11:19:25.605 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:19:26.508 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0
11:19:26.598 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 3 keys and 6 values 
11:19:26.655 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
11:19:26.679 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 3 keys and 10 values 
11:19:26.694 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:19:26.714 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
11:19:26.728 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:19:26.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:19:26.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000018d8138b8c8
11:19:26.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x0000018d8138bae8
11:19:26.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:19:26.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:19:26.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:30.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:33.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:36.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:19:36.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:19:36.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x0000018d814bd610
11:19:38.569 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:19:42.612 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c25d0d-0bd3-406a-bbcc-b0c03f27907e_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
11:19:43.373 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:19:43.490 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:19:43.498 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:32:58.519 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:32:59.395 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0
11:32:59.479 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
11:32:59.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:32:59.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
11:32:59.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
11:32:59.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:32:59.592 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:32:59.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:32:59.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001370138a590
11:32:59.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x000001370138a7b0
11:32:59.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:32:59.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:32:59.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:33:03.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:33:06.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:33:09.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:33:09.261 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:33:09.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a973ae09-33da-4a2c-be52-2e15ab8095f3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x00000137014bf000
11:33:11.297 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:33:14.901 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:33:14.997 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:33:15.006 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:43:04.391 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:05.399 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0
11:43:05.499 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:05.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:05.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:05.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:05.624 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:05.636 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:05.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:05.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001594d39ed38
11:43:05.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001594d39ef58
11:43:05.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:05.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:05.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:09.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:12.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:15.403 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:43:15.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:15.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b9ac21-32c0-44d3-b48e-5008d985bb2d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001594d4eda28
11:43:17.467 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:43:21.226 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:43:21.334 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:43:21.341 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:47:28.461 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:47:29.359 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0
11:47:29.448 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
11:47:29.503 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
11:47:29.524 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
11:47:29.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
11:47:29.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
11:47:29.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:47:29.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:47:29.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022ba739a0c0
11:47:29.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x0000022ba739a2e0
11:47:29.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:47:29.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:47:29.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:47:33.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:47:36.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:47:39.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:47:39.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:47:39.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa1a3564-097b-4968-b103-8a2ff7ef2cb6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x0000022ba74ac7c0
11:47:41.347 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:47:44.865 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:47:44.990 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:47:45.000 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:48:15.670 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:48:16.577 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0
11:48:16.676 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:16.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:16.742 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:16.755 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:16.770 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:16.794 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:16.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:16.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001feb93b6d38
11:48:16.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001feb93b6f58
11:48:16.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:16.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:16.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:48:20.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:48:23.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:48:26.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:26.561 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:48:26.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b86ba9d-2e32-46cd-9012-b12329ee36d4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001feb94c5a28
11:48:28.674 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:48:32.282 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:48:32.397 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:48:32.405 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:51:30.049 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:51:31.075 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0
11:51:31.183 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
11:51:31.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:51:31.254 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:51:31.271 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:51:31.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:51:31.302 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
11:51:31.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:51:31.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001104a3b6d38
11:51:31.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001104a3b6f58
11:51:31.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:51:31.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:51:31.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:51:32.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305092406_127.0.0.1_1270
11:51:32.673 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Notify connected event to listeners.
11:51:32.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:51:32.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6457a3c4-7d1a-4dc7-a8f4-3324e267c2c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001104a4f0ad8
11:51:32.924 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:51:39.303 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:51:39.308 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:51:39.308 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:51:48.139 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:51:51.996 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa
11:51:51.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] RpcClient init label, labels = {module=naming, source=sdk}
11:51:51.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:51:51.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:51:51.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:51:52.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:51:52.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305112018_127.0.0.1_1323
11:51:52.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:51:52.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001104a4f0ad8
11:51:52.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Notify connected event to listeners.
11:51:52.264 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
11:51:52.438 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.258 seconds (JVM running for 24.677)
11:51:52.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:51:52.456 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:51:52.457 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:51:52.527 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:51:52.533 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:51:52.759 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Receive server push request, request = NotifySubscriberRequest, requestId = 5
11:51:52.760 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3dc62fdb-282e-48cd-84fa-a7e86a2ef6fa] Ack server push request, request = NotifySubscriberRequest, requestId = 5
11:51:53.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:51:53.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26fff31e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:51:53.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305112018_127.0.0.1_1323
11:51:53.166 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@35d6517e[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 8]
11:51:53.216 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:51:53.221 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:51:53.233 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:51:53.234 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:54:18.057 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:54:19.084 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 987e26b3-cd84-41cd-befc-24c051826f9b_config-0
11:54:19.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
11:54:19.270 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
11:54:19.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
11:54:19.312 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:54:19.325 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
11:54:19.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:54:19.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:54:19.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000136b23b6480
11:54:19.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000136b23b66a0
11:54:19.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:54:19.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:54:19.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:54:20.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305260376_127.0.0.1_1626
11:54:20.644 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Notify connected event to listeners.
11:54:20.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:54:20.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000136b24f0228
11:54:20.865 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:54:25.797 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:54:25.798 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:54:25.799 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:54:26.049 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:54:26.967 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:54:26.969 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:54:26.970 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:54:35.919 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:54:39.939 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a86acd4-1dc1-44eb-8c65-3d44680ae847
11:54:39.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] RpcClient init label, labels = {module=naming, source=sdk}
11:54:39.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:54:39.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:54:39.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:54:39.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:54:40.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305279965_127.0.0.1_1658
11:54:40.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:54:40.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000136b24f0228
11:54:40.099 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Notify connected event to listeners.
11:54:40.203 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:54:40.256 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
11:54:40.419 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.215 seconds (JVM running for 24.643)
11:54:40.444 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:54:40.445 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:54:40.445 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:54:40.700 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Receive server push request, request = NotifySubscriberRequest, requestId = 13
11:54:40.727 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a86acd4-1dc1-44eb-8c65-3d44680ae847] Ack server push request, request = NotifySubscriberRequest, requestId = 13
11:55:23.605 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:55:27.899 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:55:27.901 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
12:40:43.106 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:40:43.107 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:19:27.454 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Server healthy check fail, currentConnection = 1750305260376_127.0.0.1_1626
13:19:27.455 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:19:31.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750310370717_127.0.0.1_14207
13:19:31.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750305260376_127.0.0.1_1626
13:19:31.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305260376_127.0.0.1_1626
13:19:31.209 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Notify disconnected event to listeners
13:19:32.907 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [987e26b3-cd84-41cd-befc-24c051826f9b_config-0] Notify connected event to listeners.
15:45:38.660 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:45:38.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:45:39.024 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:45:39.024 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63d3ea9e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:45:39.028 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305279965_127.0.0.1_1658
15:45:39.029 [nacos-grpc-client-executor-2774] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305279965_127.0.0.1_1658]Ignore complete event,isRunning:false,isAbandon=false
15:45:39.029 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d06d85d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2775]
15:45:39.329 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:45:39.348 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:45:39.379 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:45:39.379 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:45:39.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:45:39.385 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:45:39.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:45:39.393 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:38:29.582 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:38:30.616 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-c311-4167-9b46-4b07a058c710_config-0
16:38:30.753 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 60 ms to scan 1 urls, producing 3 keys and 6 values 
16:38:30.825 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:38:30.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:38:30.855 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
16:38:30.860 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 1 keys and 7 values 
16:38:30.881 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
16:38:30.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:38:30.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000023cad39af18
16:38:30.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000023cad39b138
16:38:30.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:38:30.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:38:30.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:32.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:32.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:32.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:38:32.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:32.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023cad4a8cc0
16:38:32.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:32.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:32.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:33.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:33.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:34.290 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:38:34.310 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:35.022 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:35.834 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:36.747 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:37.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:39.020 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:40.327 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:40.341 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:38:40.342 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:38:40.343 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:38:40.583 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:38:41.555 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:38:41.557 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:38:41.557 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:38:41.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:43.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:45.139 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:46.886 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:48.835 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:50.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:52.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:54.924 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:57.213 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:58.985 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:38:59.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:01.973 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.462 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:05.787 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ea634c99-d7b9-4b95-a9d6-cb716574489b
16:39:05.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] RpcClient init label, labels = {module=naming, source=sdk}
16:39:05.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:05.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:05.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:05.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:05.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:05.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:05.900 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:05.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:05.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023cad4a8cc0
16:39:06.033 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.240 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:39:06.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.560 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-c311-4167-9b46-4b07a058c710_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:07.247 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:07.247 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7e8e111d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:07.247 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7781b55f[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
16:39:07.247 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea634c99-d7b9-4b95-a9d6-cb716574489b] Client is shutdown, stop reconnect to server
16:39:07.251 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 92c9ce17-229c-4033-b9aa-bbbaa3b52e63
16:39:07.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] RpcClient init label, labels = {module=naming, source=sdk}
16:39:07.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:07.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:07.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:07.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:07.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:07.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:07.277 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:07.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:07.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023cad4a8cc0
16:39:07.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:07.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:07.659 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:39:07.660 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:39:07.675 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:39:07.675 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:39:07.695 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
16:39:07.695 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:39:07.729 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
16:39:07.734 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
16:39:07.922 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:08.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92c9ce17-229c-4033-b9aa-bbbaa3b52e63] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:40:58.465 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:40:59.678 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a623da84-1d02-435b-928a-16a32b928e55_config-0
16:40:59.814 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:59.905 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:59.920 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:59.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:59.951 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:59.968 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:59.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:59.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000171d539ed38
16:40:59.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000171d539ef58
16:40:59.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:59.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:59.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:41:01.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322461538_127.0.0.1_9250
16:41:01.863 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Notify connected event to listeners.
16:41:01.864 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:01.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000171d5518ad8
16:41:02.149 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:41:08.505 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:41:08.506 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:41:08.507 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:41:08.781 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:41:09.967 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:41:09.969 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:41:09.970 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:41:21.423 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:41:26.048 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0465df36-f05d-4a8a-87a9-e97953e9bd78
16:41:26.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] RpcClient init label, labels = {module=naming, source=sdk}
16:41:26.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:41:26.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:41:26.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:41:26.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:41:26.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322486077_127.0.0.1_9315
16:41:26.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:26.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Notify connected event to listeners.
16:41:26.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000171d5518ad8
16:41:26.311 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:41:26.368 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
16:41:26.549 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.295 seconds (JVM running for 30.916)
16:41:26.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:41:26.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:41:26.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:41:26.797 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Receive server push request, request = NotifySubscriberRequest, requestId = 6
16:41:26.825 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Ack server push request, request = NotifySubscriberRequest, requestId = 6
16:41:26.967 [RMI TCP Connection(8)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:43:44.492 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:43:44.492 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
17:08:20.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.858 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:20.930 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.070 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.473 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.941 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.662 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.515 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.572 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.798 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:08:25.142 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:08:25.425 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a623da84-1d02-435b-928a-16a32b928e55_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:25.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:08:25.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@ce60d33[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:08:25.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750322486077_127.0.0.1_9315
17:08:25.476 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750322486077_127.0.0.1_9315
17:08:25.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@11f9aa24[Running, pool size = 21, active threads = 1, queued tasks = 0, completed tasks = 352]
17:08:25.476 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465df36-f05d-4a8a-87a9-e97953e9bd78] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:25.665 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:08:25.669 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:08:25.684 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:08:25.684 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:08:25.688 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:08:25.688 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
