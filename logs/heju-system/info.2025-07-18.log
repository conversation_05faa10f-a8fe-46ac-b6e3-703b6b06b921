09:06:40.332 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:06:41.704 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0
09:06:41.835 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 56 ms to scan 1 urls, producing 3 keys and 6 values 
09:06:41.899 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:06:41.911 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:06:41.922 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:06:41.933 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:06:41.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:06:41.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:41.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001413439df80
09:06:41.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001413439e1a0
09:06:41.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:41.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:41.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:43.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:43.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:43.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:06:43.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:43.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000141344edf88
09:06:43.587 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:43.810 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:44.136 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:44.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:45.521 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:45.559 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:46.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:47.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:47.863 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:48.802 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:49.918 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:51.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Success to connect a server [localhost:8848], connectionId = 1752800811121_127.0.0.1_6286
09:06:51.255 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8aea21ab-87a3-4a85-aa71-af9a2bf3adc6_config-0] Notify connected event to listeners.
09:06:54.339 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:06:54.340 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:54.340 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:55.028 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:56.598 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:06:56.600 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:06:56.601 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:07:19.153 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:07:25.587 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d748b3f2-5bf5-41ee-88a5-c4705a7edc30
09:07:25.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] RpcClient init label, labels = {module=naming, source=sdk}
09:07:25.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:07:25.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:07:25.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:07:25.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:25.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Success to connect to server [localhost:8848] on start up, connectionId = 1752800845606_127.0.0.1_6683
09:07:25.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Notify connected event to listeners.
09:07:25.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:25.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000141344edf88
09:07:25.831 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:07:25.904 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:07:26.228 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 46.955 seconds (JVM running for 49.591)
09:07:26.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:07:26.264 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:07:26.265 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:07:26.342 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:07:26.372 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:07:26.775 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:09:21.219 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:09:21.219 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d748b3f2-5bf5-41ee-88a5-c4705a7edc30] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:09:21.520 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:09:21.520 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:09:21.586 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:09:21.586 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:14:31.410 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:14:31.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:14:31.763 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:14:31.763 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d934ea3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:14:31.763 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752800845606_127.0.0.1_6683
09:14:31.766 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3689f0c2[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 101]
09:14:31.918 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:14:31.926 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:14:31.926 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:14:31.926 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:14:31.926 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:14:31.926 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:14:31.940 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:14:31.940 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:14:37.604 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:38.435 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0
09:14:38.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:38.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:38.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:38.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:38.566 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:38.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:38.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:38.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a31939e230
09:14:38.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a31939e450
09:14:38.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:38.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:38.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:39.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752801279324_127.0.0.1_4215
09:14:39.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Notify connected event to listeners.
09:14:39.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:39.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad2637b0-30c3-4138-9c27-3bb371a73d3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a319518668
09:14:39.771 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:44.354 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:14:44.356 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:44.356 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:15:12.317 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:13.055 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:15:13.056 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:15:13.057 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:15:20.933 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:24.173 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 49ca87ad-c7c0-4253-87ff-2d6479adab70
09:15:24.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] RpcClient init label, labels = {module=naming, source=sdk}
09:15:24.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:24.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:24.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:24.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:24.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Success to connect to server [localhost:8848] on start up, connectionId = 1752801324183_127.0.0.1_4327
09:15:24.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:24.310 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Notify connected event to listeners.
09:15:24.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a319518668
09:15:24.390 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:15:24.436 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:15:24.597 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 47.688 seconds (JVM running for 48.808)
09:15:24.632 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:15:24.633 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:15:24.633 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:15:24.931 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:15:24.934 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:15:24.951 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ca87ad-c7c0-4253-87ff-2d6479adab70] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:15:29.978 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:15:29.980 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:04:02.648 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:04:02.653 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:04:02.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:04:02.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@129c5ea2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:04:02.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752801324183_127.0.0.1_4327
10:04:02.980 [nacos-grpc-client-executor-594] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752801324183_127.0.0.1_4327]Ignore complete event,isRunning:false,isAbandon=false
10:04:02.986 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@81d0265[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 595]
10:04:03.190 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:04:03.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:04:03.217 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:04:03.217 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:04:03.219 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:04:03.221 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:04:10.418 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:11.242 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fbafcf83-0771-492c-a343-9db9c4997902_config-0
10:04:11.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:11.368 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:11.377 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:11.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:11.399 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:11.408 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:11.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:11.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000220843b68d8
10:04:11.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000220843b6af8
10:04:11.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:11.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:11.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:12.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752804252174_127.0.0.1_10843
10:04:12.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Notify connected event to listeners.
10:04:12.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:12.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbafcf83-0771-492c-a343-9db9c4997902_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000220844f0668
10:04:12.582 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:17.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:04:17.861 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:17.861 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:18.199 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:19.515 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:04:19.515 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:04:19.515 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:04:29.130 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:32.144 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b7acdf2-e81b-4e6c-bc28-47f840272ae5
10:04:32.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] RpcClient init label, labels = {module=naming, source=sdk}
10:04:32.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:32.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:32.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:32.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:32.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Success to connect to server [localhost:8848] on start up, connectionId = 1752804272158_127.0.0.1_10919
10:04:32.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:32.276 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Notify connected event to listeners.
10:04:32.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000220844f0668
10:04:32.374 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:04:32.455 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:04:32.601 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.955 seconds (JVM running for 24.764)
10:04:32.615 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:04:32.615 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:04:32.620 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:04:32.855 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Receive server push request, request = NotifySubscriberRequest, requestId = 26
10:04:32.873 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Ack server push request, request = NotifySubscriberRequest, requestId = 26
10:04:48.443 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:04:50.143 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:04:50.143 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:04:50.154 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:04:50.158 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:04:50.168 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:04:50.168 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:44:11.693 [nacos-grpc-client-executor-1922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Receive server push request, request = NotifySubscriberRequest, requestId = 29
12:44:11.693 [nacos-grpc-client-executor-1922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b7acdf2-e81b-4e6c-bc28-47f840272ae5] Ack server push request, request = NotifySubscriberRequest, requestId = 29
14:40:26.867 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:40:26.876 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:40:27.216 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:40:27.216 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@79a2d3d5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:40:27.216 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752804272158_127.0.0.1_10919
14:40:27.221 [nacos-grpc-client-executor-3324] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752804272158_127.0.0.1_10919]Ignore complete event,isRunning:false,isAbandon=false
14:40:27.228 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@18514dce[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3325]
14:40:27.434 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:40:27.434 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:40:27.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:40:27.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:40:27.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:40:27.442 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:40:33.736 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:40:34.536 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0
14:40:34.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
14:40:34.647 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:40:34.655 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:40:34.667 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:40:34.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:40:34.686 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:40:34.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:40:34.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000206a339dd00
14:40:34.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000206a339df20
14:40:34.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:40:34.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:40:34.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:40:35.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752820835486_127.0.0.1_3080
14:40:35.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Notify connected event to listeners.
14:40:35.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:35.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b85cb7-dbf4-4a2e-9614-7dfd58bfa860_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000206a3517cb0
14:40:35.892 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:40:39.972 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:40:39.973 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:40:39.973 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:40:40.155 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:40:40.842 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:40:40.844 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:40:40.845 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:40:48.696 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:40:51.765 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c9a705b6-fa8b-4963-8ebb-eb8f9a583353
14:40:51.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] RpcClient init label, labels = {module=naming, source=sdk}
14:40:51.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:40:51.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:40:51.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:40:51.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:40:51.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Success to connect to server [localhost:8848] on start up, connectionId = 1752820851781_127.0.0.1_3106
14:40:51.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:51.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000206a3517cb0
14:40:51.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Notify connected event to listeners.
14:40:51.976 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:40:52.021 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:40:52.189 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.067 seconds (JVM running for 20.17)
14:40:52.234 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:40:52.235 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:40:52.236 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:40:52.343 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:40:52.446 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:40:52.473 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9a705b6-fa8b-4963-8ebb-eb8f9a583353] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:41:07.749 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:41:07.749 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:45:09.799 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:45:09.805 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:45:10.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:45:10.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@65ba1401[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:45:10.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752820851781_127.0.0.1_3106
17:45:10.143 [nacos-grpc-client-executor-2224] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752820851781_127.0.0.1_3106]Ignore complete event,isRunning:false,isAbandon=false
17:45:10.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b2cdbd7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2225]
17:45:10.282 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:45:10.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:45:10.293 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:45:10.293 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:45:10.293 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:45:10.293 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:45:39.880 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:45:40.487 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f5993f4-9097-4517-97ab-f4577487e9af_config-0
17:45:40.537 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
17:45:40.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
17:45:40.584 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
17:45:40.584 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
17:45:40.584 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
17:45:40.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
17:45:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:45:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000195813b9288
17:45:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000195813b94a8
17:45:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:45:40.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:45:40.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:45:41.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752831941112_127.0.0.1_9096
17:45:41.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Notify connected event to listeners.
17:45:41.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:45:41.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f5993f4-9097-4517-97ab-f4577487e9af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000195814f0fb0
17:45:41.441 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:45:43.921 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:45:43.921 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:45:43.921 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:45:44.014 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:45:44.827 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:45:44.827 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:45:44.827 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:45:49.630 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:45:51.691 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 32a107ad-b64a-4a10-8742-cefe0e185f3a
17:45:51.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] RpcClient init label, labels = {module=naming, source=sdk}
17:45:51.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:45:51.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:45:51.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:45:51.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:45:51.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Success to connect to server [localhost:8848] on start up, connectionId = 1752831951700_127.0.0.1_9117
17:45:51.827 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Notify connected event to listeners.
17:45:51.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:45:51.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000195814f0fb0
17:45:51.875 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:45:51.914 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:45:51.990 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.562 seconds (JVM running for 14.533)
17:45:52.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:45:52.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:45:52.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:45:52.387 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Receive server push request, request = NotifySubscriberRequest, requestId = 45
17:45:52.390 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32a107ad-b64a-4a10-8742-cefe0e185f3a] Ack server push request, request = NotifySubscriberRequest, requestId = 45
17:47:09.072 [http-nio-9600-exec-4] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:47:10.039 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:47:10.039 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:03:04.293 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:03:04.293 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:03:04.626 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:03:04.626 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d27476e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:03:04.626 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752831951700_127.0.0.1_9117
18:03:04.626 [nacos-grpc-client-executor-142] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752831951700_127.0.0.1_9117]Ignore complete event,isRunning:false,isAbandon=false
18:03:04.631 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b9c75a1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 143]
18:03:04.771 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:03:04.773 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:03:04.778 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:03:04.778 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:03:04.780 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:03:04.780 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:03:08.906 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:03:09.460 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0
18:03:09.517 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
18:03:09.548 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
18:03:09.555 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:03:09.563 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
18:03:09.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
18:03:09.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
18:03:09.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:03:09.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002bc1e3a6480
18:03:09.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002bc1e3a66a0
18:03:09.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:03:09.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:03:09.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:03:10.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752832990101_127.0.0.1_11021
18:03:10.273 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Notify connected event to listeners.
18:03:10.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:03:10.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38dbb9fb-4608-45fd-9b87-dba0668e0dbe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002bc1e508228
18:03:10.364 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:03:12.787 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:03:12.788 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:03:12.788 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:03:12.896 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:03:13.416 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:03:13.417 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:03:13.417 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:03:18.590 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:03:20.732 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 85423d32-2da8-4fab-a1d3-e1492470d40e
18:03:20.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] RpcClient init label, labels = {module=naming, source=sdk}
18:03:20.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:03:20.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:03:20.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:03:20.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:03:20.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Success to connect to server [localhost:8848] on start up, connectionId = 1752833000735_127.0.0.1_11043
18:03:20.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:03:20.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Notify connected event to listeners.
18:03:20.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002bc1e508228
18:03:20.888 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:03:20.907 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:03:21.002 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.577 seconds (JVM running for 13.397)
18:03:21.014 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:03:21.014 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:03:21.014 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:03:21.136 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:03:21.458 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Receive server push request, request = NotifySubscriberRequest, requestId = 54
18:03:21.476 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85423d32-2da8-4fab-a1d3-e1492470d40e] Ack server push request, request = NotifySubscriberRequest, requestId = 54
18:03:33.238 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:03:33.238 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:05:30.467 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:05:30.483 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:05:30.809 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:05:30.809 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1262fb12[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:05:30.809 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752833000735_127.0.0.1_11043
18:05:30.811 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752833000735_127.0.0.1_11043]Ignore complete event,isRunning:false,isAbandon=false
18:05:30.811 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@24b32a99[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 37]
18:05:30.941 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:05:30.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:05:30.945 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:05:30.945 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:05:30.945 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:05:30.945 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:05:34.955 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:05:35.506 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0
18:05:35.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
18:05:35.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
18:05:35.589 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:05:35.596 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
18:05:35.604 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
18:05:35.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
18:05:35.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:05:35.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000226503c2d28
18:05:35.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000226503c2f48
18:05:35.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:05:35.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:05:35.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:05:36.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752833136090_127.0.0.1_11370
18:05:36.262 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Notify connected event to listeners.
18:05:36.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:05:36.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d21ea61-77e9-473c-91d5-ed4fc004fe3f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000226504fc200
18:05:36.336 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:05:38.671 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:05:38.672 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:05:38.672 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:05:38.783 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:05:39.341 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:05:39.342 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:05:39.343 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:05:44.637 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:05:47.197 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 73a45521-f709-4d27-9cc6-807f96e864c1
18:05:47.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] RpcClient init label, labels = {module=naming, source=sdk}
18:05:47.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:05:47.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:05:47.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:05:47.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:05:47.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Success to connect to server [localhost:8848] on start up, connectionId = 1752833147209_127.0.0.1_11406
18:05:47.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:05:47.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000226504fc200
18:05:47.334 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Notify connected event to listeners.
18:05:47.375 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:05:47.400 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:05:47.524 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.058 seconds (JVM running for 13.854)
18:05:47.538 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:05:47.538 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:05:47.539 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:05:47.637 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:05:47.888 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Receive server push request, request = NotifySubscriberRequest, requestId = 63
18:05:47.905 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Ack server push request, request = NotifySubscriberRequest, requestId = 63
18:05:59.464 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:05:59.464 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:09:49.040 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Receive server push request, request = NotifySubscriberRequest, requestId = 67
18:09:49.040 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73a45521-f709-4d27-9cc6-807f96e864c1] Ack server push request, request = NotifySubscriberRequest, requestId = 67
18:13:41.765 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:13:41.765 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:13:42.108 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:13:42.108 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48341a30[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:13:42.108 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752833147209_127.0.0.1_11406
18:13:42.108 [nacos-grpc-client-executor-119] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752833147209_127.0.0.1_11406]Ignore complete event,isRunning:false,isAbandon=false
18:13:42.108 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6984bc22[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 120]
18:13:42.242 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:13:42.242 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:13:42.258 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:13:42.258 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:13:42.258 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:13:42.258 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:13:46.313 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:13:46.835 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0
18:13:46.887 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
18:13:46.914 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
18:13:46.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:13:46.927 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
18:13:46.933 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
18:13:46.940 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
18:13:46.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:13:46.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c8913bed38
18:13:46.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c8913bef58
18:13:46.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:13:46.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:13:46.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:13:47.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752833627436_127.0.0.1_12454
18:13:47.610 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Notify connected event to listeners.
18:13:47.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:13:47.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c8914f8ad8
18:13:47.689 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:13:49.968 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:13:49.968 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:13:49.968 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:13:50.080 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:13:50.658 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:13:50.658 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:13:50.658 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:13:55.800 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:13:57.860 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 95f4f854-ade3-41f4-a6ce-dee8a45c7b57
18:13:57.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] RpcClient init label, labels = {module=naming, source=sdk}
18:13:57.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:13:57.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:13:57.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:13:57.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:13:57.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Success to connect to server [localhost:8848] on start up, connectionId = 1752833637860_127.0.0.1_12483
18:13:57.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:13:57.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c8914f8ad8
18:13:57.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Notify connected event to listeners.
18:13:58.027 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:13:58.042 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:13:58.126 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.284 seconds (JVM running for 13.082)
18:13:58.137 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:13:58.141 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:13:58.141 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:13:58.428 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:13:58.567 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Receive server push request, request = NotifySubscriberRequest, requestId = 73
18:13:58.582 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Ack server push request, request = NotifySubscriberRequest, requestId = 73
18:14:15.482 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Receive server push request, request = NotifySubscriberRequest, requestId = 77
18:14:15.482 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Ack server push request, request = NotifySubscriberRequest, requestId = 77
18:14:15.676 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:14:15.677 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:14:15.680 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
18:14:15.680 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
18:14:15.680 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
18:14:15.682 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:14:15.687 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:14:15.688 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:14:15.688 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
18:14:15.688 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
18:14:15.688 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:14:15.689 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:14:15.689 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:14:15.689 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:15:06.699 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Receive server push request, request = NotifySubscriberRequest, requestId = 78
18:15:06.699 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Ack server push request, request = NotifySubscriberRequest, requestId = 78
18:18:11.701 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Server healthy check fail, currentConnection = 1752833637860_127.0.0.1_12483
18:18:11.701 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Server healthy check fail, currentConnection = 1752833627436_127.0.0.1_12454
18:18:11.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:18:11.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Success to connect a server [localhost:8848], connectionId = 1752833911913_127.0.0.1_13012
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Success to connect a server [localhost:8848], connectionId = 1752833911913_127.0.0.1_13011
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752833627436_127.0.0.1_12454
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Abandon prev connection, server is localhost:8848, connectionId is 1752833637860_127.0.0.1_12483
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752833627436_127.0.0.1_12454
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752833637860_127.0.0.1_12483
18:18:32.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Notify disconnected event to listeners
18:18:32.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f0c95d0-c1d5-4225-b0fd-b315fcc7dfe7_config-0] Notify connected event to listeners.
18:18:32.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Notify disconnected event to listeners
18:18:32.030 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Notify connected event to listeners.
18:18:35.964 [nacos-grpc-client-executor-72] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Receive server push request, request = NotifySubscriberRequest, requestId = 83
18:18:35.964 [nacos-grpc-client-executor-72] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Ack server push request, request = NotifySubscriberRequest, requestId = 83
18:18:35.968 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Receive server push request, request = NotifySubscriberRequest, requestId = 85
18:18:35.968 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Ack server push request, request = NotifySubscriberRequest, requestId = 85
18:18:35.974 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Receive server push request, request = NotifySubscriberRequest, requestId = 84
18:18:35.981 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f4f854-ade3-41f4-a6ce-dee8a45c7b57] Ack server push request, request = NotifySubscriberRequest, requestId = 84
18:21:37.293 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:21:37.308 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:21:37.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:21:37.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3dda694b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:21:37.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752833911913_127.0.0.1_13011
18:21:37.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@20a7430b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 117]
18:21:37.781 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:21:37.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
18:21:37.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
18:21:37.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:21:37.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:21:37.781 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:21:41.834 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:21:42.354 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0
18:21:42.404 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
18:21:42.430 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
18:21:42.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
18:21:42.443 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
18:21:42.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
18:21:42.454 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 2 keys and 8 values 
18:21:42.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:21:42.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019a043ce480
18:21:42.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000019a043ce6a0
18:21:42.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:21:42.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:21:42.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:21:43.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752834102929_127.0.0.1_13445
18:21:43.115 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Notify connected event to listeners.
18:21:43.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:21:43.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfcc9ef2-5b54-4097-aa44-b108c280465d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019a04508668
18:21:43.192 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:21:45.501 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:21:45.501 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:21:45.501 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:21:45.609 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:21:46.194 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:21:46.195 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:21:46.195 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:21:51.246 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:21:53.396 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1
18:21:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] RpcClient init label, labels = {module=naming, source=sdk}
18:21:53.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:21:53.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:21:53.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:21:53.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:21:53.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Success to connect to server [localhost:8848] on start up, connectionId = 1752834113431_127.0.0.1_13465
18:21:53.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:21:53.543 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Notify connected event to listeners.
18:21:53.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019a04508668
18:21:53.604 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:21:53.662 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:21:53.811 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.459 seconds (JVM running for 13.292)
18:21:53.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:21:53.833 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:21:53.833 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:21:54.016 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:21:54.164 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Receive server push request, request = NotifySubscriberRequest, requestId = 97
18:21:54.178 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Ack server push request, request = NotifySubscriberRequest, requestId = 97
18:24:28.220 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:24:28.220 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:24:49.243 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Receive server push request, request = NotifySubscriberRequest, requestId = 103
18:24:49.243 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2db3280-20aa-4f9f-8ec7-c0f3fe7c65c1] Ack server push request, request = NotifySubscriberRequest, requestId = 103
18:47:19.410 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:47:19.414 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:47:19.754 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:47:19.754 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d4d163f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:47:19.754 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752834113431_127.0.0.1_13465
18:47:19.755 [nacos-grpc-client-executor-342] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752834113431_127.0.0.1_13465]Ignore complete event,isRunning:false,isAbandon=false
18:47:19.759 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@8bc515b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 343]
18:47:19.919 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:47:19.925 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:47:19.935 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:47:19.935 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:47:19.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:47:19.937 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
