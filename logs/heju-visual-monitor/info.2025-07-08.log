19:27:45.829 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
19:27:46.887 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @2680ms to org.eclipse.jetty.util.log.Slf4jLog
19:27:47.010 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
19:27:47.024 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
19:27:47.050 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.<PERSON>mbeddedWebAppContext@33379242{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.8012.392636585189394432/],STOPPED}
19:44:26.463 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
19:44:27.463 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @2442ms to org.eclipse.jetty.util.log.Slf4jLog
19:44:27.596 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
19:44:27.614 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
19:44:27.643 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@4091b9c3{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.8012.455774576942579354/],STOPPED}
20:14:41.296 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:14:43.517 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @4495ms to org.eclipse.jetty.util.log.Slf4jLog
20:14:43.767 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
20:14:43.796 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
20:14:43.958 [main] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
20:14:43.958 [main] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
20:14:43.960 [main] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
20:14:43.969 [main] INFO  o.e.j.s.h.ContextHandler - [doStart,921] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@77d381e6{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.8012.15614778642418945934/],AVAILABLE}
20:14:43.970 [main] INFO  o.e.j.server.Server - [doStart,415] - Started @4951ms
20:14:44.436 [main] INFO  c.h.k.s.FileConvertQueueTask - [startTask,36] - 队列处理文件转换任务启动完成 
20:14:44.452 [main] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
20:14:44.455 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@77d381e6{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.8012.15614778642418945934/],STOPPED}
20:25:55.095 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:25:55.157 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:25:55.787 [main] INFO  c.h.k.HeJuFileviewApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:25:57.984 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @4413ms to org.eclipse.jetty.util.log.Slf4jLog
20:25:58.191 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
20:25:58.210 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
20:25:58.340 [main] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
20:25:58.340 [main] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
20:25:58.341 [main] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
20:25:58.347 [main] INFO  o.e.j.s.h.ContextHandler - [doStart,921] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@31f31b20{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.8012.2151398036725122374/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],AVAILABLE}
20:25:58.348 [main] INFO  o.e.j.server.Server - [doStart,415] - Started @4778ms
20:25:58.647 [main] INFO  c.h.k.s.FileConvertQueueTask - [startTask,36] - 队列处理文件转换任务启动完成 
20:25:58.659 [main] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
20:25:58.661 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@31f31b20{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.8012.2151398036725122374/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],STOPPED}
20:33:48.259 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:33:48.308 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:33:48.820 [main] INFO  c.h.k.HeJuFileviewApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:33:50.275 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @3478ms to org.eclipse.jetty.util.log.Slf4jLog
20:33:50.419 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
20:33:50.450 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
20:33:50.551 [main] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
20:33:50.551 [main] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
20:33:50.552 [main] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
20:33:50.560 [main] INFO  o.e.j.s.h.ContextHandler - [doStart,921] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@1be8c122{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.3178744577833202876/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],AVAILABLE}
20:33:50.560 [main] INFO  o.e.j.server.Server - [doStart,415] - Started @3764ms
20:33:53.098 [main] INFO  c.h.k.s.FileConvertQueueTask - [startTask,36] - 队列处理文件转换任务启动完成 
20:33:53.106 [main] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
20:33:53.108 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@1be8c122{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.3178744577833202876/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],STOPPED}
20:38:52.949 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:38:53.000 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:38:53.498 [main] INFO  c.h.k.HeJuFileviewApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:38:54.919 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @3196ms to org.eclipse.jetty.util.log.Slf4jLog
20:38:55.052 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
20:38:55.072 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
20:38:55.159 [main] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
20:38:55.159 [main] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
20:38:55.159 [main] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
20:38:55.165 [main] INFO  o.e.j.s.h.ContextHandler - [doStart,921] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@2c678c7b{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.8842178117022827598/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],AVAILABLE}
20:38:55.166 [main] INFO  o.e.j.server.Server - [doStart,415] - Started @3443ms
20:38:55.481 [main] INFO  c.h.k.s.FileConvertQueueTask - [startTask,36] - 队列处理文件转换任务启动完成 
20:38:55.492 [main] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
20:38:55.494 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@2c678c7b{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.8842178117022827598/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],STOPPED}
20:44:48.117 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:44:48.166 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:44:48.637 [main] INFO  c.h.k.HeJuFileviewApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:44:49.991 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @3071ms to org.eclipse.jetty.util.log.Slf4jLog
20:44:50.122 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
20:44:50.141 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
20:44:50.230 [main] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
20:44:50.230 [main] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
20:44:50.231 [main] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 660000ms
20:44:50.238 [main] INFO  o.e.j.s.h.ContextHandler - [doStart,921] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@a5e8260{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.9423659332757746186/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],AVAILABLE}
20:44:50.238 [main] INFO  o.e.j.server.Server - [doStart,415] - Started @3318ms
20:44:50.585 [main] INFO  c.h.k.s.FileConvertQueueTask - [startTask,36] - 队列处理文件转换任务启动完成 
20:44:50.595 [main] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
20:44:50.596 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@a5e8260{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.9423659332757746186/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],STOPPED}
20:54:59.974 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
20:55:00.022 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:55:00.491 [main] INFO  c.h.k.HeJuFileviewApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:55:01.816 [main] INFO  o.e.jetty.util.log - [initialized,170] - Logging initialized @3059ms to org.eclipse.jetty.util.log.Slf4jLog
20:55:01.948 [main] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.50.v20221201; built: 2022-12-01T22:07:03.915Z; git: da9a0b30691a45daf90a9f17b5defa2f1434f882; jvm 17.0.9+9-jvmci-23.0-b22
20:55:01.968 [main] INFO  o.e.j.s.h.C.application - [log,2368] - Initializing Spring embedded WebApplicationContext
20:55:02.060 [main] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
20:55:02.061 [main] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
20:55:02.061 [main] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
20:55:02.067 [main] INFO  o.e.j.s.h.ContextHandler - [doStart,921] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@2c678c7b{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.16545376098146508758/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],AVAILABLE}
20:55:02.067 [main] INFO  o.e.j.server.Server - [doStart,415] - Started @3312ms
20:55:02.402 [main] INFO  c.h.k.s.FileConvertQueueTask - [startTask,36] - 队列处理文件转换任务启动完成 
20:55:02.907 [jodconverter-offprocmng-0] INFO  o.j.l.o.OfficeDescriptor - [fromExecutablePath,111] - soffice info (from exec path): Product: LibreOffice - Version: ??? - useLongOptionNameGnuStyle: true
20:55:02.907 [jodconverter-offprocmng-1] INFO  o.j.l.o.OfficeDescriptor - [fromExecutablePath,111] - soffice info (from exec path): Product: LibreOffice - Version: ??? - useLongOptionNameGnuStyle: true
20:55:03.213 [jodconverter-offprocmng-1] INFO  o.j.l.o.LocalOfficeProcessManager - [executeStartProcessAndConnect,416] - Starting process with --accept 'socket,host=127.0.0.1,port=2002,tcpNoDelay=1;urp;StarOffice.ServiceManager' and profileDir 'C:\Users\<USER>\AppData\Local\Temp\.jodconverter_socket_host-127.0.0.1_port-2002_tcpNoDelay-1'
20:55:03.213 [jodconverter-offprocmng-0] INFO  o.j.l.o.LocalOfficeProcessManager - [executeStartProcessAndConnect,416] - Starting process with --accept 'socket,host=127.0.0.1,port=2001,tcpNoDelay=1;urp;StarOffice.ServiceManager' and profileDir 'C:\Users\<USER>\AppData\Local\Temp\.jodconverter_socket_host-127.0.0.1_port-2001_tcpNoDelay-1'
20:55:03.440 [main] INFO  c.h.k.c.WebConfig - [addResourceHandlers,29] - Add resource locations: D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-kkfileview\src\main\file\
20:55:04.312 [main] INFO  c.h.k.s.OfficePluginManager - [destroyOfficeManager,139] - Shutting down office process
20:55:04.312 [main] INFO  o.j.c.o.AbstractOfficeManagerPool - [stop,141] - Stopping the office manager pool...
20:55:06.967 [jodconverter-offprocmng-1] INFO  o.j.l.o.OfficeConnection - [connect,119] - Connected: 'socket,host=127.0.0.1,port=2002,tcpNoDelay=1'
20:55:06.967 [jodconverter-offprocmng-1] INFO  o.j.l.o.LocalOfficeProcessManager - [executeStartProcessAndConnect,436] - Started process; pid: 18404
20:55:07.731 [jodconverter-offprocmng-0] INFO  o.j.l.o.OfficeConnection - [connect,119] - Connected: 'socket,host=127.0.0.1,port=2001,tcpNoDelay=1'
20:55:07.731 [jodconverter-offprocmng-0] INFO  o.j.l.o.LocalOfficeProcessManager - [executeStartProcessAndConnect,436] - Started process; pid: 31256
20:55:07.951 [MessageDispatcher] INFO  o.j.l.o.OfficeConnection - [disposing,166] - Disconnected from 'socket,host=127.0.0.1,port=2001,tcpNoDelay=1'
20:55:07.991 [jodconverter-offprocmng-0] INFO  o.j.l.o.LocalOfficeProcessManager - [ensureProcessExited,750] - Process exited with code 0
20:55:08.112 [MessageDispatcher] INFO  o.j.l.o.OfficeConnection - [disposing,166] - Disconnected from 'socket,host=127.0.0.1,port=2002,tcpNoDelay=1'
20:55:08.274 [jodconverter-offprocmng-1] INFO  o.j.l.o.LocalOfficeProcessManager - [ensureProcessExited,750] - Process exited with code 0
20:55:08.300 [main] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
20:55:08.301 [main] INFO  o.e.j.s.h.ContextHandler - [doStop,1159] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@2c678c7b{application,/,[file:///C:/Users/<USER>/AppData/Local/Temp/jetty-docbase.9900.16545376098146508758/, jar:file:/D:/devapps/Java-repo/repository/org/webjars/swagger-ui/4.15.5/swagger-ui-4.15.5.jar!/META-INF/resources],STOPPED}
