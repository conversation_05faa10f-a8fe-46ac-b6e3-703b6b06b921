package com.heju.tenant.api.tenant.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.feign.factory.RemoteInviteRegisFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 源策略服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteInviteRegisService", value = ServiceConstants.TENANT_SERVICE, fallbackFactory = RemoteInviteRegisFallbackFactory.class)
public interface RemoteInviteRegisService {

    /**
     * 更改微信绑定信息
     *
     * @param source 请求来源
     * @return 结果
     */
    @PutMapping("/inviteRegister/editWechat")
    AjaxResult editWechatBind(@RequestHeader(SecurityConstants.FROM_SOURCE) String source, @RequestBody BaseUserDto dto);

    /**
     * 查询基础用户信息
     * @param baseUserDto BaseUserDto
     * @param source 请求来源
     * @return 基础用户信息 BaseUser
     */
    @GetMapping("/inviteRegister/getBaseUser")
    R<BaseUserDto> selectBaseUser(@RequestParam(value = "baseUserDto") BaseUserDto baseUserDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}