package com.heju.tenant.api.tenant.domain.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "sys_base_user", excludeProperty = {"name"})
public class BaseUserDto {

    /** baseUserId */
    private String id;

    /** 手机号 */
    private String telephone;

    /** openId */
    private String openId;

    /** unionId */
    private String unionId;

}
