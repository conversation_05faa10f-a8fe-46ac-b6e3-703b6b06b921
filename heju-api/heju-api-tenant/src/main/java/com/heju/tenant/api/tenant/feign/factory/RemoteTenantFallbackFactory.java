package com.heju.tenant.api.tenant.feign.factory;

import com.alibaba.fastjson2.JSONObject;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.dto.UserTenantMergeDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import com.heju.tenant.api.tenant.feign.RemoteTenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 租户服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteTenantFallbackFactory implements FallbackFactory<RemoteTenantService> {

    @Override
    public RemoteTenantService create(Throwable throwable) {
        log.error("租户服务调用失败:{}", throwable.getMessage());
        return new RemoteTenantService() {
            @Override
            public R<Boolean> registerTenantInfo(JSONObject register, String source)
            {
                return R.fail("注册租户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<TeTenantDto>> listByIds(String telephone, String source) {
                return R.fail("查询租户列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<TeTenantPo>> listByUnionId(String unionId, String source) {
                return R.fail("查询租户列表失败:" + throwable.getMessage());
            }

            @Override
            public R<TeTenantDto> getMyTenant(String sourceName, String source) {
                return R.fail("查询租户列表失败:" + throwable.getMessage());
            }

            @Override
            public R<BaseUserDto> getBaseUserByOpenId(String openId, String source) {
                return R.fail("查询BaseUser失败:" + throwable.getMessage());
            }

            @Override
            public R<BaseUserDto> addBaseUser(String openId, String source) {
                return R.fail("添加BaseUser失败:" + throwable.getMessage());
            }

            @Override
            public R<UserTenantMergeDto> setUserTenantMerge(UserTenantMergeDto dto, String source) {
                return R.fail("添加UserTenantMerge失败:" + throwable.getMessage());
            }

            @Override
            public R<Integer> getDatabaseVersion(String source) {
                return R.fail("获取版本号失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult updateTheDatabase(String sql, String source) {
                return AjaxResult.error("远程调用更新数据库失败");
            }
        };
    }
}