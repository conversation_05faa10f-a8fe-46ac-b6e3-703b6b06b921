package com.heju.tenant.api.tenant.domain.dto;
import com.heju.common.core.annotation.Excel;
import com.heju.tenant.api.tenant.domain.po.TeTenantApprovalPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 租户审批 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeTenantApprovalDto extends TeTenantApprovalPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 验证码 */
    @Excel(name = "验证码")
    protected String code;

    /** 权限Ids */
    @Excel(name = "权限Ids")
    protected Long[] authIds;

}