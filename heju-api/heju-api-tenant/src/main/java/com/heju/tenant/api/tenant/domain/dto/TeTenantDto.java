package com.heju.tenant.api.tenant.domain.dto;

import com.heju.common.core.constant.system.AuthorityConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 租户 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeTenantDto extends TeTenantPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户中的用户id */
    private String userId;

    /** 策略信息 */
    private TeStrategyDto strategy;

    /** 权限Ids */
    private Long[] authIds;

    /** 校验是否非租管租户 */
    public boolean isNotAdmin() {
        return !isAdmin(this.getIsLessor());
    }

    /** 校验是否为租管租户 */
    public boolean isAdmin() {
        return isAdmin(this.getIsLessor());
    }

    /** 校验是否为租管租户 */
    public static boolean isAdmin(String isLessor) {
        return StrUtil.equals(AuthorityConstants.TenantType.ADMIN.getCode(), isLessor);
    }

}