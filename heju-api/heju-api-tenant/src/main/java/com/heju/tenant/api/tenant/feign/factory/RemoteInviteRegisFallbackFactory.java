package com.heju.tenant.api.tenant.feign.factory;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.feign.RemoteInviteRegisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * 源策略服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteInviteRegisFallbackFactory implements FallbackFactory<RemoteInviteRegisService> {

    @Override
    public RemoteInviteRegisService create(Throwable throwable) {
        log.error("服务调用失败:{}", throwable.getMessage());
        return new RemoteInviteRegisService() {
            @Override
            public AjaxResult editWechatBind(String source, BaseUserDto dto) {
                return AjaxResult.error("更改微信绑定信息失败:" + throwable.getMessage());
            }

            @Override
            public R<BaseUserDto> selectBaseUser(BaseUserDto dto, String source) {
                return R.fail("调用查询基础用户信息失败:" + throwable.getMessage());
            }
        };
    }
}