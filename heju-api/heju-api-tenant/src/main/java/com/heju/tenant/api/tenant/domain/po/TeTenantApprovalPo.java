package com.heju.tenant.api.tenant.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 租户审批 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "te_tenant_approval", excludeProperty = { DEL_FLAG, CREATE_TIME, REMARK, NAME })
public class TeTenantApprovalPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户名称 */
    @Excel(name = "租户名称")
    protected String nick;

    /** 租户logo */
    @Excel(name = "租户logo")
    protected String logo;

    /** 状态（0未审批 1已通过 2已驳回） */
    @Excel(name = "状态", readConverterExp = "0=未审批,1=已通过,2=已驳回")
    protected String status;

    /** 管理员手机号 */
    @Excel(name = "管理员手机号")
    protected String phone;

    /** 管理员微信号 */
    @Excel(name = "管理员微信号")
    protected String wechatId;

    /** 管理员微信名 */
    @Excel(name = "管理员微信名")
    protected String wechatName;

    /** OpenId */
    @Excel(name = "OpenId")
    protected String openid;

    /** UniId */
    @Excel(name = "UniId")
    protected String uniid;

    /** 申请时间 */
    @Excel(name = "申请时间")
    protected Date applicationDate;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    protected String reason;

}