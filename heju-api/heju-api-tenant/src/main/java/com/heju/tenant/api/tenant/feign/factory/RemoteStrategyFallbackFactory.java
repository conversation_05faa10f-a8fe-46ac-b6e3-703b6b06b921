package com.heju.tenant.api.tenant.feign.factory;

import com.heju.common.core.web.result.R;
import com.heju.tenant.api.tenant.feign.RemoteStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * 源策略服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteStrategyFallbackFactory implements FallbackFactory<RemoteStrategyService> {

    @Override
    public RemoteStrategyService create(Throwable throwable) {
        log.error("源策略服务调用失败:{}", throwable.getMessage());
        return source -> R.fail("刷新源策略缓存失败:" + throwable.getMessage());
    }
}