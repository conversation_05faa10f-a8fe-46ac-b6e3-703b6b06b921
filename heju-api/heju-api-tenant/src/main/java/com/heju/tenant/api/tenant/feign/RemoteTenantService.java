package com.heju.tenant.api.tenant.feign;

import com.alibaba.fastjson2.JSONObject;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.dto.UserTenantMergeDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import com.heju.tenant.api.tenant.feign.factory.RemoteTenantFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteTenantService", value = ServiceConstants.TENANT_SERVICE, fallbackFactory = RemoteTenantFallbackFactory.class)
public interface RemoteTenantService {

    /**
     * 注册租户信息
     *
     * @param register 注册信息 | 约定json内容tenant = tenant, dept = dept, post = post, user = user
     * @param source   请求来源
     * @return 结果
     */
    @PostMapping("/tenant/register")
    R<Boolean> registerTenantInfo(@RequestBody JSONObject register, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/tenant/listByIds/{telephone}")
    R<List<TeTenantDto>> listByIds(@PathVariable("telephone") String telephone, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/tenant/listByUnionId/{unionId}")
    R<List<TeTenantPo>> listByUnionId(@PathVariable("unionId") String unionId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/tenant/tenant/getMyTenant/{sourceName}")
    R<TeTenantDto> getMyTenant(@PathVariable("sourceName") String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    @GetMapping("/tenant/tenant/getMyTenant/{openId}")
    R<BaseUserDto> getBaseUserByOpenId(@PathVariable("openId") String openId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/tenant/tenant/addBaseUser")
    R<BaseUserDto> addBaseUser(String openId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/tenant/tenant/addMerge")
    R<UserTenantMergeDto> setUserTenantMerge(@RequestBody UserTenantMergeDto dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    @GetMapping("/tenant/getDatabaseVersion")
    R<Integer> getDatabaseVersion(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    @PostMapping("/tenant/updateTheDatabase")
    AjaxResult updateTheDatabase(@RequestParam(value = "sql")String sql,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}