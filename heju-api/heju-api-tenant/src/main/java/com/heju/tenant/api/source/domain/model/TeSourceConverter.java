package com.heju.tenant.api.source.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.source.domain.po.TeSourcePo;
import com.heju.tenant.api.source.domain.query.TeSourceQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 数据源 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TeSourceConverter extends BaseConverter<TeSourceQuery, TeSourceDto, TeSourcePo> {
}