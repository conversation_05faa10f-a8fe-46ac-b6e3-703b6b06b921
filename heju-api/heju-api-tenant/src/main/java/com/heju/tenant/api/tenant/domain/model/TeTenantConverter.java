package com.heju.tenant.api.tenant.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 租户 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TeTenantConverter extends BaseConverter<TeTenantQuery, TeTenantDto, TeTenantPo> {
}