package com.heju.tenant.api.tenant.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantApprovalPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 租户审批 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TeTenantApprovalConverter extends BaseConverter<TeTenantApprovalQuery, TeTenantApprovalDto, TeTenantApprovalPo> {
}
