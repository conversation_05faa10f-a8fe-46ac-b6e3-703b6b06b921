package com.heju.tenant.tenant.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.model.TeTenantConverter;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantQuery;
import com.heju.tenant.tenant.manager.ITeTenantManager;
import com.heju.tenant.tenant.mapper.TeTenantMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 租户管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class TeTenantManagerImpl extends BaseManagerImpl<TeTenantQuery, TeTenantDto, TeTenantPo, Te<PERSON>enantMapper, TeTenantConverter> implements ITeTenantManager {

    /**
     * 校验数据源策略是否被使用
     *
     * @param strategyId 数据源策略id
     * @return 结果
     */
    @Override
    public TeTenantDto checkStrategyExist(Long strategyId) {
        TeTenantPo tenant = baseMapper.selectOne(
                Wrappers.<TeTenantPo>query().lambda()
                        .eq(TeTenantPo::getStrategyId, strategyId)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(tenant);
    }

    @Override
    public List<TeTenantDto> listByIds(String telephone) {
        List<TeTenantDto> teTenantDtos = baseMapper.listByIds(telephone);
        return teTenantDtos;
    }

    @Override
    public List<TeTenantPo> listByUnionId(String unionId) {
        return baseMapper.listByUnionId(unionId);
    }
}