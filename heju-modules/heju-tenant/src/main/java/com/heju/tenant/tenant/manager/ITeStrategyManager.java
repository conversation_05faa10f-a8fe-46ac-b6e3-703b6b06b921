package com.heju.tenant.tenant.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.query.TeStrategyQuery;


/**
 * 数据源策略管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ITeStrategyManager extends IBaseManager<TeStrategyQuery, TeStrategyDto> {

    /**
     * 校验数据源是否被使用
     *
     * @param sourceId 数据源id
     * @return 结果
     */
    TeStrategyDto checkSourceExist(Long sourceId);

    /**
     * 根据数据源slave查询策略源信息
     * @param sourceSlave 数据源salve
     * @return 策略源信息
     */
    TeStrategyDto selectBySourceSlave(String sourceSlave);
}