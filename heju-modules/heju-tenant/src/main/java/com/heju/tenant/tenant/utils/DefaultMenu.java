package com.heju.tenant.tenant.utils;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Getter
@Component
@Configuration
@ConfigurationProperties(prefix = "default-menu")
public class DefaultMenu {

    /**
     * 默认菜单Ids
     */
    private Long[] menuIds;

    private String roleKey;

    private String name;

    private String code;


    public void setMenuIds(Long[] menuIds) {
        this.menuIds = menuIds;
    }

    public void setRoleKey(String roleKey) {
        this.roleKey = roleKey;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
