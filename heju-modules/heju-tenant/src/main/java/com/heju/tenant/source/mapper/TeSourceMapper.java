package com.heju.tenant.source.mapper;

import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.source.domain.po.TeSourcePo;
import com.heju.tenant.api.source.domain.query.TeSourceQuery;
import org.apache.ibatis.annotations.Select;

/**
 * 数据源管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface TeSourceMapper extends BaseMapper<TeSourceQuery, TeSourceDto, TeSourcePo> {
    @Select("Select * from te_source where slave = #{slave}")
    TeSourceDto selectBySlave(String slave);
}