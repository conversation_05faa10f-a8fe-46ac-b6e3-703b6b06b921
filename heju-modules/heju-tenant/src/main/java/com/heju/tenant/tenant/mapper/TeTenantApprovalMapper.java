package com.heju.tenant.tenant.mapper;


import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantApprovalPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 租户审批管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface TeTenantApprovalMapper extends BaseMapper<TeTenantApprovalQuery, TeTenantApprovalDto, TeTenantApprovalPo> {

    /**
     * 根据手机号查询用户表信息
     *
     * @param phone 手机号
     * @return baseUserID
     */

    @Select("select `id` from sys_base_user where telephone=#{telephone}")
    String selectBaseUserByPhone(@Param("telephone") String phone);
    @Insert("insert sys_base_user (`id`,telephone, open_id) VALUES (#{baseUserId},#{phone},#{openId})")
    int addBaseUser(@Param("baseUserId") String baseUserId,@Param("phone") String phone, @Param("openId") String openId);

    @Select("select `id` from sys_base_user where open_id=#{openid}")
    String selectBaseUserByOpenId(String openid);

    @Insert("insert into sys_user_tenant_merge (user_id, tenant_id, tenant_user_id) values (#{userId},#{tenantId},#{tenantUserId})")
    int addUserTenantMerge(@Param("userId") String userId, @Param("tenantId") Long tenantId, @Param("tenantUserId") Long tenantUserId);

}