package com.heju.tenant.tenant.service.impl;

import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.DictConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.query.TeStrategyQuery;
import com.heju.tenant.tenant.manager.ITeStrategyManager;
import com.heju.tenant.tenant.service.ITeStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据源策略管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class TeStrategyServiceImpl extends BaseServiceImpl<TeStrategyQuery, TeStrategyDto, ITeStrategyManager> implements ITeStrategyService {

    @Autowired
    ITeStrategyManager StrategyManager;

    /**
     * 缓存主键命名定义
     */
    @Override
    protected String getCacheKey() {
        return CacheConstants.CacheType.TE_STRATEGY_KEY.getCode();
    }

    /**
     * 校验数据源是否被使用
     *
     * @param sourceId 数据源id
     * @return 结果 | true/false 存在/不存在
     */
    @Override
    public boolean checkSourceExist(Long sourceId) {
        return ObjectUtil.isNotNull(baseManager.checkSourceExist(sourceId));
    }

    /**
     * 校验源策略是否为默认源策略
     *
     * @param id 源策略id
     * @return 结果 | true/false 是/不是
     */
    @Override
    public boolean checkIsDefault(Long id) {
        TeStrategyDto strategy = baseManager.selectById(id);
        return ObjectUtil.isNotNull(strategy) && StrUtil.equals(strategy.getIsDefault(), DictConstants.DicYesNo.YES.getCode());
    }

    /**
     * 根据数据源slave查询策略源信息
     * @param sourceSlave 数据源slave
     * @return 策略源信息
     */
    public TeStrategyDto selectBySourceSlave(String sourceSlave) {
        return StrategyManager.selectBySourceSlave(sourceSlave);
    }
}