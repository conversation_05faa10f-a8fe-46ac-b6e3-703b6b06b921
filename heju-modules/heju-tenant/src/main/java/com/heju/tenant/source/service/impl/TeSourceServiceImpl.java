package com.heju.tenant.source.service.impl;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.DictConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.SpringUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.redis.constant.RedisConstants;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.source.domain.query.TeSourceQuery;
import com.heju.tenant.source.manager.ITeSourceManager;
import com.heju.tenant.source.manager.impl.TeSourceManagerImpl;
import com.heju.tenant.source.service.ITeSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据源管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class TeSourceServiceImpl extends BaseServiceImpl<TeSourceQuery, TeSourceDto, TeSourceManagerImpl> implements ITeSourceService {

    @Autowired
    ITeSourceManager sourceManager;

    /**
     * 缓存主键命名定义
     */
    @Override
    protected String getCacheKey() {
        return CacheConstants.CacheType.TE_SOURCE_KEY.getCode();
    }

    /**
     * 校验数据源是否为默认数据源
     *
     * @param id 数据源id
     * @return 结果 | true/false 是/不是
     */
    @Override
    public boolean checkIsDefault(Long id) {
        TeSourceDto source = baseManager.selectById(id);
        return ObjectUtil.isNotNull(source) && StrUtil.equals(source.getIsDefault(), DictConstants.DicYesNo.YES.getCode());
    }

    /**
     * 新增数据源对象（批量）
     *
     * @param sourceList 数据源对象集合
     * @return 结果
     */
    @Override
    public int insertBatch(Collection<TeSourceDto> sourceList) {
//        if (CollUtil.isNotEmpty(sourceList))
//            sourceList.forEach(source -> source.setSlave(IdUtil.simpleUUID()));
        return super.insertBatch(sourceList);
    }

    /**
     * 缓存更新
     *
     * @param operate      服务层 - 操作类型
     * @param operateCache 缓存操作类型
     * @param dto          数据对象
     * @param dtoList      数据对象集合
     */
    @Override
    protected void refreshCache(OperateConstants.ServiceType operate, RedisConstants.OperateType operateCache, TeSourceDto dto, Collection<TeSourceDto> dtoList) {
        switch (operateCache) {
            case REFRESH_ALL -> {
                List<TeSourceDto> allList = baseManager.selectList(null);
                redisService.deleteObject(getCacheKey());
                redisService.refreshMapCache(getCacheKey(), allList, TeSourceDto::getSlave, TeSourceDto -> TeSourceDto);
            }
            case REFRESH -> {
                if (operate.isSingle())
                    redisService.refreshMapValueCache(getCacheKey(), dto::getSlave, () -> dto);
                else if (operate.isBatch())
                    dtoList.forEach(item -> redisService.refreshMapValueCache(getCacheKey(), item::getSlave, () -> item));
            }
            case REMOVE -> {
                if (operate.isSingle())
                    redisService.removeMapValueCache(getCacheKey(), dto.getSlave());
                else if (operate.isBatch())
                    redisService.removeMapValueCache(getCacheKey(), dtoList.stream().map(TeSourceDto::getSlave).toArray(String[]::new));
            }
        }
    }

    /**
     * 根据数据源名查找
     * @param slave 数据源slave
     * @return 数据源信息
     */
    public TeSourceDto selectBySlave(String slave) {
        return sourceManager.selectBySlave(slave);
    }


}