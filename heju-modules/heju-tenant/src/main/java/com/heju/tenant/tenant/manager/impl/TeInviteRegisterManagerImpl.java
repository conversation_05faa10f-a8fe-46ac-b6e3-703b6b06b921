package com.heju.tenant.tenant.manager.impl;

import cn.hutool.core.lang.UUID;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.domain.dto.UserTenantMergeDto;
import com.heju.tenant.tenant.manager.ITeInviteRegisterManager;
import com.heju.tenant.tenant.mapper.TeInviteRegisterMapper;
import com.heju.tenant.tenant.service.ITeTenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TeInviteRegisterManagerImpl implements ITeInviteRegisterManager {

    @Autowired
    TeInviteRegisterMapper inviteRegisterMapper;

    @Autowired
    ITeTenantService tenantService;

    @Override
    public String baseUserBinding(String id, String openId, String telephone) {
        BaseUserDto baseUser = new BaseUserDto();
        if (id == null){
            id = UUID.randomUUID().toString();
            baseUser.setId(id);
            baseUser.setOpenId(openId);
            baseUser.setTelephone(telephone);
            inviteRegisterMapper.insertBaseUser(baseUser);
        }
        baseUser.setId(id);
        baseUser.setOpenId(openId);
        baseUser.setTelephone(telephone);
        inviteRegisterMapper.updateBaseUser(baseUser);
        return id;
    }

    @Override
    public UserTenantMergeDto userTenantMergeBinding(String baseUserId, Long tenantId, Long userId) {
        UserTenantMergeDto userTenantMergeDto = new UserTenantMergeDto();
        userTenantMergeDto.setBaseUserId(baseUserId);
        userTenantMergeDto.setTenantId(tenantId);
        userTenantMergeDto.setTenantUserId(userId);
        return userTenantMergeDto;
    }

}
