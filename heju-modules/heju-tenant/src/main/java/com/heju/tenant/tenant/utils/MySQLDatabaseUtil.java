package com.heju.tenant.tenant.utils;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.job.ScheduleConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.file.api.domain.SysFile;
import com.heju.file.api.feign.RemoteFileManageService;
import com.heju.file.api.feign.RemoteFileService;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.tenant.config.DatabaseConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileReader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Objects;

@Component
public class MySQLDatabaseUtil {

    @Autowired
    private DatabaseConfig databaseConfig;

    @Autowired
    private RemoteFileManageService remoteFileManageService;


    private MySQLDatabaseUtil() {
        // 私有构造方法,防止外部实例化
    }

    /**
     * 创建 MySQL 数据库
     *
     * @param dbName 数据库名称后缀(uuid)
     * @return 数据库名称
     */
    public String createDatabase(String dbName) {

        try (Connection connection = DriverManager.getConnection(databaseConfig.getConnectionAddress(),databaseConfig.getUsername(), databaseConfig.getPassword())) {
            String createDatabaseSql = "CREATE DATABASE " + "hj_cloud_" + dbName + ";";  //数据库名称
            String useDatabaseSql = "USE " + "hj_cloud_" + dbName + ";";  //使用数据库sql名称
            //获取建表语句字符串并进行分割
            String createTableSql = TableCreator.readTableSqlFromFile();
            String[] tableSqlStatements = createTableSql.split(";");
            //获取触发器sql语句字符串并进行分割
            String createTriggerSql = TriggerCreator.readTriggerSqlFromFile();
            String[] triggerSqlStatements = createTriggerSql.split("delimiter ;");

            try (Statement statement = connection.createStatement()) {
                statement.executeUpdate(createDatabaseSql);
                statement.executeUpdate(useDatabaseSql);
                for (String tableSql : tableSqlStatements) {
                    if (tableSql.trim().length() > 0) {
                        System.out.println(tableSql);
                        statement.execute(tableSql);
                    }
                }
                for (String triggerSql : triggerSqlStatements) {
                    if (triggerSql.trim().length() > 0) {
                        System.out.println(triggerSql);
                        statement.execute(triggerSql);
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "hj_cloud_" + dbName;
    }

    /**
     * 删除指定数据库
     *
     * @param dbname 数据库名称
     */
    public void deleteDateBase(String dbname) {
        try {
            // 加载数据库驱动
            Class.forName(databaseConfig.getDriverClass());
            // 建立数据库连接
            Connection connection = DriverManager.getConnection(databaseConfig.getConnectionAddress(), databaseConfig.getUsername(), databaseConfig.getPassword());
            // 创建 Statement 对象
            Statement statement = connection.createStatement();
            // 删除指定数据库
            String dropDatabaseQuery = "DROP DATABASE " + dbname + ";";
            statement.executeUpdate(dropDatabaseQuery);

            System.out.println("数据库 " + dbname + " 已成功删除!");
        } catch (ClassNotFoundException e) {
            System.out.println("无法加载数据库驱动: " + e.getMessage());
        } catch (SQLException e) {
            System.out.println("删除数据库时出现错误: " + e.getMessage());
        }
    }


    /**
     * 创建并获取数据源连接
     *
     * @param dbName    数据库名称
     * @param urlAppend 连接参数
     * @return 数据库连接
     */
    public Connection getConnection(String dbName, String urlAppend) throws SQLException {
        String Url = databaseConfig.getConnectionAddress() + dbName + urlAppend;
        return DriverManager.getConnection(Url, databaseConfig.getUsername(), databaseConfig.getPassword());
    }

    public void updateTheDatabase(TeTenantDto tenantDto, String sql) {
        String nick = tenantDto.getNick();
        if (nick.equals("heju")){
            nick = "hj-cloud1";
        }else {
            nick = "hj_cloud_" + nick;
        }
        try (Connection connection = getConnection(nick,databaseConfig.getAppend())) {
            String[] updateSqlStatements = sql.split(";");
            try (Statement statement = connection.createStatement()) {
                for (String tableSql : updateSqlStatements) {
                    if (tableSql.trim().length() > 0) {
                        System.out.println(tableSql);
                        statement.execute(tableSql);
                    }
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }


//测试方法
//    public static void main(String[] args) throws SQLException {
//
//        try (Connection connection = DriverManager.getConnection(CONNECTION_ADDRESS, USERNAME, PASSWORD)) {
//            String createDatabaseSql = "CREATE DATABASE " + "hj_cloud" + "1234" + ";";
//            String useDatabaseSql = "USE " + "hj_cloud" + "1234" + ";";
//            String createTableSql = TableCreator.readTableSqlFromFile();
//            String[] tableSqlStatements = createTableSql.split(";");
//            String createTriggerSql = TriggerCreator.readTriggerSqlFromFile();
//            String[] triggerSqlStatements = createTriggerSql.split("delimiter ;");
//
//            try (Statement statement = connection.createStatement()) {
//                statement.executeUpdate(createDatabaseSql);
//                statement.executeUpdate(useDatabaseSql);
//                for (String tableSql : tableSqlStatements) {
//                    if (tableSql.trim().length() > 0) {
//                        System.out.println(tableSql);
//                        statement.execute(tableSql);
//                    }
//                }
//                for (String triggerSql : triggerSqlStatements){
//                    if (triggerSql.trim().length() > 0){
//                        System.out.println(triggerSql);
//                        statement.execute(triggerSql);
//                    }
//                }
//            } catch (SQLException e) {
//                e.printStackTrace();
//            }
//
//        }
//    }
}