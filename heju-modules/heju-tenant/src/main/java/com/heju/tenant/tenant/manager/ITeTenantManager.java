package com.heju.tenant.tenant.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantQuery;

import java.util.List;

/**
 * 租户管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ITeTenantManager extends IBaseManager<TeTenantQuery, TeTenantDto> {

    /**
     * 校验数据源策略是否被使用
     *
     * @param strategyId 数据源策略id
     * @return 结果
     */
    TeTenantDto checkStrategyExist(Long strategyId);

    List<TeTenantDto>  listByIds(String telephone);

    List<TeTenantPo>  listByUnionId(String unionId);
}