package com.heju.tenant.tenant.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
    public class DatabaseConfig {

        @Getter
        @Value("${createDB.connection.address}")
        private String connectionAddress;

        @Value("${createDB.connection.driver-class}")
        private String driverClass;

        @Value("${createDB.connection.username}")
        private String username;

        @Getter
        @Value("${createDB.connection.password}")
        private String password;

        @Getter
        @Value("${createDB.connection.append}")
        private String append;

        
    public String getConnectionAddress() {
        return connectionAddress;
    }

    public String getDriverClass() {
        return driverClass;
    }

    public String getUsername() {
        return username;
    }

}