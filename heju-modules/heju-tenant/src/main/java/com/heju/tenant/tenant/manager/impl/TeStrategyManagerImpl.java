package com.heju.tenant.tenant.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.model.TeStrategyConverter;
import com.heju.tenant.api.tenant.domain.po.TeStrategyPo;
import com.heju.tenant.api.tenant.domain.query.TeStrategyQuery;
import com.heju.tenant.tenant.manager.ITeStrategyManager;
import com.heju.tenant.tenant.mapper.TeStrategyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 数据源策略管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class TeStrategyManagerImpl extends BaseManagerImpl<TeStrategyQuery, TeStrategyDto, TeStrategyPo, TeStrategyMapper, TeStrategyConverter> implements ITeStrategyManager {

    @Autowired
    TeStrategyMapper strategyMapper;

    /**
     * 校验数据源是否被使用
     *
     * @param sourceId 数据源id
     * @return 结果
     */
    @Override
    public TeStrategyDto checkSourceExist(Long sourceId) {
        TeStrategyPo strategy = baseMapper.selectOne(
                Wrappers.<TeStrategyPo>query().lambda()
                        .eq(TeStrategyPo::getSourceId, sourceId)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(strategy);
    }

    /**
     * 根据数据源slave查询策略源信息
     * @param sourceSlave 数据源slave
     * @return 策略源信息
     */
    @Override
    public TeStrategyDto selectBySourceSlave(String sourceSlave) {
        return strategyMapper.selectBySourceSlave(sourceSlave);
    }
}