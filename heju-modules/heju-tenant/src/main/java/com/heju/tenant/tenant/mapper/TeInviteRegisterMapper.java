package com.heju.tenant.tenant.mapper;


import com.heju.common.datasource.annotation.Master;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.domain.dto.UserTenantMergeDto;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Master
public interface TeInviteRegisterMapper {

    @Select("select * from sys_base_user where open_id = #{openId}")
    BaseUserDto getBaseUserByOpenId(String openId);

    @Select("select * from sys_base_user where telephone = #{phone}")
    BaseUserDto getBaseUserByPhone(String phone);

    @Insert("insert into sys_base_user (id, telephone, open_id) VALUES (#{id},#{telephone},#{openId})")
    int insertBaseUser(BaseUserDto baseUser);

    @Update("update sys_base_user set open_id = #{openId} where id = #{id} and telephone = #{telephone}")
    int updateBaseUser(BaseUserDto baseUser);

    @Insert("insert into sys_user_tenant_merge (user_id, tenant_id, tenant_user_id) VALUES (#{baseUserId},#{tenantId},#{tenantUserId})")
    int setUserTenantMerge(UserTenantMergeDto dto);

    @Update("update sys_base_user set open_id = null")
    int wechatUnbinding(String id);
}
