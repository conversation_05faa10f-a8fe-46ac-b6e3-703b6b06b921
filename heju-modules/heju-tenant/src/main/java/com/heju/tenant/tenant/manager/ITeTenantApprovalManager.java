package com.heju.tenant.tenant.manager;


import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;

/**
 * 租户审批管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ITeTenantApprovalManager extends IBaseManager<TeTenantApprovalQuery, TeTenantApprovalDto> {
    TeSourceDto wrapSource(TeTenantApprovalDto dto, String uuid);

    TeStrategyDto wrapStrategy(TeSourceDto tempSource, TeTenantApprovalDto dto);

    TeTenantDto wrapTenant(TeStrategyDto tempStrategy, TeSourceDto teSourceDto, TeTenantApprovalDto dto);

    SysUserDto wrapUser(TeTenantApprovalDto dto);

    SysRoleDto wrapDefaultEmployee(Long tenantId,Long roleGroupId);

    SysRoleGroupDto wrapBaseRoleGroup(Long userId);
}