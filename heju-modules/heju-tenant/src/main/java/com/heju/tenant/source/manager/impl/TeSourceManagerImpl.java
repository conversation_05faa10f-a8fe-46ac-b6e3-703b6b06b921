package com.heju.tenant.source.manager.impl;

import com.heju.common.core.utils.core.IdUtil;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.source.domain.model.TeSourceConverter;
import com.heju.tenant.api.source.domain.po.TeSourcePo;
import com.heju.tenant.api.source.domain.query.TeSourceQuery;
import com.heju.tenant.source.manager.ITeSourceManager;
import com.heju.tenant.source.mapper.TeSourceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 数据源管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class TeSourceManagerImpl extends BaseManagerImpl<TeSourceQuery, TeSourceDto, TeSourcePo, TeSourceMapper, TeSourceConverter> implements ITeSourceManager {

    @Autowired
    TeSourceMapper sourceMapper;

    /**
     * /workflow对象
     *
     * @param source 数据源对象
     * @return 结果
     */
    @Override
    public int insert(TeSourceDto source) {
        return baseMapper.insert(source);
    }

    /**
     * 根据数据源slave查找
     * @param slave 数据源slave
     * @return 数据源信息
     */

    @Override
    public TeSourceDto selectBySlave(String slave) {
        return sourceMapper.selectBySlave(slave);
    }
}