package com.heju.tenant.tenant.manager.impl;
import com.heju.common.core.constant.basic.TenantConstants;
import com.heju.common.core.constant.system.AuthorityConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.model.TeTenantApprovalConverter;
import com.heju.tenant.api.tenant.domain.po.TeTenantApprovalPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;
import com.heju.tenant.tenant.config.DatabaseConfig;
import com.heju.tenant.tenant.manager.ITeTenantApprovalManager;
import com.heju.tenant.tenant.mapper.TeTenantApprovalMapper;
import com.heju.tenant.tenant.utils.DefaultMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 租户审批管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class TeTenantApprovalManager extends BaseManagerImpl<TeTenantApprovalQuery, TeTenantApprovalDto, TeTenantApprovalPo, TeTenantApprovalMapper, TeTenantApprovalConverter> implements ITeTenantApprovalManager {

    @Autowired
    private DatabaseConfig databaseConfig;

    @Autowired
    private DefaultMenu defaultMenu;

    /**
     * 封装数据源数据
     * @param dto 租户申请信息
     * @param uuid uuid
     * @return 封装好的SourceDto
     */
    @Override
    public TeSourceDto wrapSource(TeTenantApprovalDto dto, String uuid) {
        TeSourceDto teSourceDto = new TeSourceDto();
        teSourceDto.setName(dto.getNick() + "数据源");//数据源名称
        teSourceDto.setSlave(uuid);
        teSourceDto.setDriverClassName(databaseConfig.getDriverClass());//驱动
        teSourceDto.setUrlPrepend(databaseConfig.getConnectionAddress() + "hj_cloud_" + uuid);//连接地址
        teSourceDto.setUrlAppend(databaseConfig.getAppend());//连接参数
        teSourceDto.setUserName(databaseConfig.getUsername());//用户名
        teSourceDto.setPassword(databaseConfig.getPassword());//密码
        return teSourceDto;
    }

    /**
     * 封装策略源数据
     * @param tempSource 数据源信息
     * @param dto 租户申请信息
     * @return 封装好的strategyDto
     */
    @Override
    public TeStrategyDto wrapStrategy(TeSourceDto tempSource, TeTenantApprovalDto dto) {
        TeStrategyDto strategyDto = new TeStrategyDto();
        strategyDto.setSource(tempSource); //数据源信息
        strategyDto.setSourceId(tempSource.getId());//数据源id
        strategyDto.setSourceSlave(tempSource.getSlave());//数据源slave
        strategyDto.setName(dto.getNick() + "数据源策略"); //策略名称
        return strategyDto;
    }

    /**
     * 封装租户数据
     * @param tempStrategy 策略源信息
     * @param teSourceDto 数据源信息
     * @param dto 租户申请信息
     * @return 封装好的TenantDto
     */
    @Override
    public TeTenantDto wrapTenant(TeStrategyDto tempStrategy, TeSourceDto teSourceDto, TeTenantApprovalDto dto) {
        TeTenantDto teTenantDto = new TeTenantDto();
        teTenantDto.setStrategy(tempStrategy); //策略信息
        teTenantDto.setStrategyId(tempStrategy.getId());//策略源id
        teTenantDto.setNick(teSourceDto.getSlave()); //租户名称
        teTenantDto.setName(teSourceDto.getSlave()); //账号
        teTenantDto.setSystemName(dto.getNick()); //系统名称
        teTenantDto.setNameFrequency(5);//账号修改次数
        teTenantDto.setAuthIds(dto.getAuthIds()); //设置权限范围
        teTenantDto.setLogo(dto.getLogo());
        return teTenantDto;
    }

    /**
     * 封装用户数据
     * @param dto 租户申请信息
     * @return 封装好的User
     */
    @Override
    public SysUserDto wrapUser(TeTenantApprovalDto dto) {
        SysUserDto user = new SysUserDto();
        user.setUserName(TenantConstants.DEFAULT_USERNAME); //用户名
        user.setPassword(TenantConstants.DEFAULT_PASSWORD); //密码
        user.setNickName(TenantConstants.DEFAULT_USERNAME); //用户昵称
        user.setPhone(dto.getPhone()); //手机号
        user.setOpenId(dto.getOpenid());//openId
        return user;
    }

    /**
     * 封装默认普通员工角色
     * @return 封装好的Role
     */
    @Override
    public SysRoleDto wrapDefaultEmployee(Long tenantId,Long roleGroupId) {
        SysRoleDto sysRoleDto = new SysRoleDto();
        sysRoleDto.setName(defaultMenu.getName());
        sysRoleDto.setCode(defaultMenu.getCode());
        sysRoleDto.setRoleKey(defaultMenu.getRoleKey());
        sysRoleDto.setEnterpriseId(tenantId);
        sysRoleDto.setDataScope(AuthorityConstants.DataScope.SELF.getCode());
        sysRoleDto.setAuthIds(defaultMenu.getMenuIds());
        sysRoleDto.setRoleGroupId(roleGroupId);
        return sysRoleDto;
    }

    /**
     * 封装基础角色组
     * @return 封装好的roleGroup
     */
    @Override
    public SysRoleGroupDto wrapBaseRoleGroup(Long userId) {
        SysRoleGroupDto roleGroupDto = new SysRoleGroupDto();
        roleGroupDto.setRoleGroupName("基础角色组");
        roleGroupDto.setUserId(userId);
        return roleGroupDto;
    }
}