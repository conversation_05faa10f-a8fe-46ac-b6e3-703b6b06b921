package com.heju.tenant.tenant.mapper;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.dto.UserTenantMergeDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import com.heju.tenant.api.tenant.domain.query.TeTenantQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 租户管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface TeTenantMapper extends BaseMapper<TeTenantQuery, TeTenantDto, TeTenantPo> {


    @Select("select c.* ,CAST(b.tenant_user_id AS VARCHAR(50)) AS user_id from sys_base_user a left join sys_user_tenant_merge b on a.id=b.user_id " +
            "left join te_tenant c on b.tenant_id=c.id" +
            " where a.telephone =#{telephone} group by tenant_id" )
    List<TeTenantDto> listByIds(String phone);

    @Select("select * from te_tenant where nick = #{nick}")
    TeTenantDto selectByCondition(String nick);

    @Select("select c.* from sys_base_user a left join sys_user_tenant_merge b on a.id=b.user_id " +
            "left join te_tenant c on b.tenant_id=c.id" +
            " where a.open_id =#{unionId}" )
    List<TeTenantPo> listByUnionId(String unionId);

    @Select("select * from te_tenant where name = #{sourceName}")
    TeTenantDto selectByName(String sourceName);

    @Select("select max(version) from sys_version_change_history")
    int getDatabaseVersion();

    @Select("select * from te_tenant")
    List<TeTenantDto> selectAll();
}