package com.heju.tenant.tenant.mapper;

import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.po.TeStrategyPo;
import com.heju.tenant.api.tenant.domain.query.TeStrategyQuery;
import org.apache.ibatis.annotations.Select;

/**
 * 数据源策略管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface TeStrategyMapper extends BaseMapper<TeStrategyQuery, TeStrategyDto, TeStrategyPo> {
    @Select("Select * from te_strategy where source_slave = #{sourceSlave}")
    TeStrategyDto selectBySourceSlave(String sourceSlave);
}