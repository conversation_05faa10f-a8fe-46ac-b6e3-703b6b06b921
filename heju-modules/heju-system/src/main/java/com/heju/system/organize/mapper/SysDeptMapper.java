package com.heju.system.organize.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.TreeMapper;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.po.SysDeptPo;
import com.heju.system.api.organize.domain.query.SysDeptQuery;

/**
 * 部门管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysDeptMapper extends TreeMapper<SysDeptQuery, SysDeptDto, SysDeptPo> {
}
