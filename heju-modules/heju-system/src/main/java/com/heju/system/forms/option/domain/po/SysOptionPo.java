package com.heju.system.forms.option.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;


/**
 * 选项 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_option", excludeProperty = { STATUS,SORT })
public class SysOptionPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 选项类型（1-通用；2-自定） */
    @Excel(name = "选项类型", readConverterExp = "1-通用；2-自定；3-业务")
    protected Integer optionType;

    /** API名称 */
    @Excel(name = "API名称")
    protected String apiName;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}