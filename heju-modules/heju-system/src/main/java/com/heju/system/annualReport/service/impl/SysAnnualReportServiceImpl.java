package com.heju.system.annualReport.service.impl;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import com.heju.system.annualReport.manager.ISysAnnualReportManager;
import com.heju.system.annualReport.service.ISysAnnualReportService;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.organize.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工商年报管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysAnnualReportServiceImpl extends BaseServiceImpl<SysAnnualReportQuery, SysAnnualReportDto, ISysAnnualReportManager> implements ISysAnnualReportService {

    @Resource
    private SysEntityMapper sysEntityMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 查询工商年报对象列表 | 数据权限
     * @param query 工商年报对象
     * @return 工商年报对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysAnnualReportMapper"})
    public AjaxResult selectGlobalList(SysAnnualReportQuery query) {
        // 1.取出所有 sys_annual_report 数据
        query.setPage((query.getPage() - 1) * query.getPageSize());
        List<Map<String, Object>> stringObjectMap = baseManager.selectByEntity(query);
        for (Map<String, Object> objectMap : stringObjectMap) {
            Long entityId = (Long) objectMap.get("entityId");
            SysEntityPo sysEntityPo = sysEntityMapper.selectById(entityId);
            if (sysEntityPo != null) {
                objectMap.put("entityName", sysEntityPo.getName());
            }
            // 从 relatedItems 取出 create_by 查找出对应 名称 给 createName
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> relatedItems = (List<Map<String, Object>>) objectMap.get("relatedItems");
            if (relatedItems != null && !relatedItems.isEmpty()) {
                relatedItems.forEach(item -> {
                    Long createBy = (Long) item.get("create_by");
                    SysUserPo sysUserPo = sysUserMapper.selectById(createBy);
                    if (sysUserPo != null) {
                        item.put("createName", sysUserPo.getNickName());
                    }
                });
            }
        }
        // 返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("list", stringObjectMap);
        resultMap.put("total", stringObjectMap.size());
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    /**
     * 查询工商年报对象列表部分 | 数据权限
     *
     * @param annualReport 工商年报对象
     * @return 工商年报对象集合
     */
    @Override
    public List<SysAnnualReportDto> selectPartList(SysAnnualReportQuery annualReport) {
        // 1.构造查询条件 查出所有 sys_annual_report 数据
        List<SysAnnualReportPo> POs = baseManager.selectPartList(annualReport);
        return setEntityNameAndCreateName(POs);
    }

    /**
     * 设置实体名称和创建人名称 代码复用
     *
     * @param POs 查询结果
     * @return sysAnnualReportDtos 数据传输对象
     */
    private List<SysAnnualReportDto> setEntityNameAndCreateName(List<SysAnnualReportPo> POs) {
        List<SysAnnualReportDto> DTOs = new ArrayList<>();
        if (POs == null || POs.isEmpty()) {
            return DTOs;
        }
        // 设置缓存
        Map<Long, String> entityNameCache = new HashMap<>();
        Map<Long, String> createNameCache = new HashMap<>();
        // 将 po 转为 dto
        POs.forEach(PO -> {
            SysAnnualReportDto DTO = new SysAnnualReportDto();
            BeanUtils.copyProperties(PO, DTO);
            // 2.根据 sysAnnualReportDto 中的 entityId 取出 sys_entity 中的 name
            if (DTO.getEntityId() != null) {
                Long entityId = DTO.getEntityId();
                String entityName = entityNameCache.get(entityId);// 尝试从缓存中获取
                if (entityName == null) { // 未命中缓存
                    SysEntityPo sysEntityPo = sysEntityMapper.selectById(entityId);
                    entityName = sysEntityPo != null ? sysEntityPo.getName() : "";
                    entityNameCache.put(entityId, entityName); // 放入缓存
                }
                DTO.setEntityName(entityName);
            }
            // 3.根据 sysAnnualReportDto 的 createBy 取出 sys_user 中的 nickName
            if (DTO.getCreateBy() != null) {
                Long createBy = DTO.getCreateBy();
                String createName = createNameCache.get(createBy); // 尝试从缓存中获取
                if (createName == null) { // 未命中缓存
                    SysUserPo sysUserPo = sysUserMapper.selectById(createBy);
                    createName = sysUserPo != null ? sysUserPo.getNickName() : "";
                    createNameCache.put(createBy, createName); // 放入缓存
                }
                DTO.setCreateName(createName);
            }
            DTOs.add(DTO);
        });
        return DTOs;
    }

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysAnnualReportDto selectById(Serializable id) {
        // 1.取出 id下 的 sys_annual_report 表记录
        SysAnnualReportDto dto = baseManager.selectById(id);
        if (dto == null) {
            return null;
        }
        // 2.根据 sysAnnualReportDto 中的 entityId 取出 sys_entity 中的 name
        if (dto.getEntityId() != null) {
            SysEntityPo sysEntityPo = sysEntityMapper.selectById(dto.getEntityId());
            dto.setEntityName(sysEntityPo != null ? sysEntityPo.getName() : "");
        }
        // 3.根据 sysAnnualReportDto 的 createBy 取出 sys_user 中的 nickName
        if (dto.getCreateBy() != null) {
            SysUserPo sysUserPo = sysUserMapper.selectById(dto.getCreateBy());
            dto.setCreateName(sysUserPo != null ? sysUserPo.getNickName() : "");
        }
        return dto;
    }

    /**
     * 查询实体名称选择框列表
     *
     * @return 工商年报对象集合
     */
    @Override
    public List<SysAnnualReportDto> selectEntityOption() {
        // 1.查出所有 sys_entity 的 id, name
        List<SysEntityPo> sysEntityPos = sysEntityMapper.select();
        List<SysAnnualReportDto> sysAnnualReportDtos = new ArrayList<>();
        // 2.将 id, name 赋值给 SysAnnualReportDto 对应值后 返回
        sysEntityPos.forEach(sysEntityPo -> {
            SysAnnualReportDto sysAnnualReportDto = new SysAnnualReportDto();
            sysAnnualReportDto.setEntityId(sysEntityPo.getId());
            sysAnnualReportDto.setEntityName(sysEntityPo.getName());
            sysAnnualReportDtos.add(sysAnnualReportDto);
        });
        // 3. 返回
        return sysAnnualReportDtos;
    }

    /**
     * 查询单个工商年报 通过entityId and reportYear
     *
     * @param annualReport 工商年报对象
     * @return 工商年报对象
     */
    @Override
    public SysAnnualReportDto selectByEntityAndYear(SysAnnualReportDto annualReport) {
        SysAnnualReportPo sysAnnualReportPo = baseManager.selectByEntityAndYear(annualReport);
        if (sysAnnualReportPo == null) {
            return null;
        }
        SysAnnualReportDto sysAnnualReportDto = new SysAnnualReportDto();
        BeanUtils.copyProperties(sysAnnualReportPo, sysAnnualReportDto);
        return sysAnnualReportDto;
    }

    /**
     * 工商年报-通过
     *
     * @param annualReport 工商年报对象
     * @return int
     */
    @Override
    public int pass(SysAnnualReportDto annualReport) {
        SysAnnualReportDto sysAnnualReportDto = baseManager.selectById(annualReport.getId());
        sysAnnualReportDto.setStatus("3");
        return baseManager.update(sysAnnualReportDto);
    }

    /**
     * 工商年报-驳回
     *
     * @param annualReport 工商年报对象
     * @return int
     */
    @Override
    public int reject(SysAnnualReportDto annualReport) {
        SysAnnualReportDto sysAnnualReportDto = baseManager.selectById(annualReport.getId());
        sysAnnualReportDto.setStatus("4");
        sysAnnualReportDto.setRemark(annualReport.getRemark());
        return baseManager.update(sysAnnualReportDto);
    }

    /**
     * 检查当前年份是否需要新增年报
     * @return int - 返回实际新增的记录数
     */
    @Override
    public int check() {
        int currentYear = LocalDate.now().getYear();
        // 1.查询所有 sys_entity
        List<SysEntityPo> sysEntityPos = sysEntityMapper.select();
        if (sysEntityPos.isEmpty()) { return 0; }

        // 2.查询 当前年份 的 sys_annual_report 记录
        SysAnnualReportQuery reportQuery = new SysAnnualReportQuery();
        reportQuery.setReportYear(String.valueOf(currentYear));
        List<SysAnnualReportDto> sysAnnualReportDtos = baseManager.selectList(reportQuery);

        // 3.将 sys_annual_report 记录中的 entityId 和 reportYear 组合成 key
        Set<String> reportKeys = sysAnnualReportDtos.stream()
                .map(dto -> dto.getEntityId() + "_" + dto.getReportYear())
                .collect(Collectors.toSet());
        int addedCount = 0;

        // 4.遍历 sys_entity，判断是否需要新增记录
        for (SysEntityPo sysEntityPo : sysEntityPos) {
            String key = sysEntityPo.getId() + "_" + currentYear;
            if (!reportKeys.contains(key)) {
                // 4.1没有记录，新增一条记录
                SysAnnualReportDto sysAnnualReportDto = new SysAnnualReportDto();
                sysAnnualReportDto.setEntityId(sysEntityPo.getId());
                sysAnnualReportDto.setReportYear(String.valueOf(currentYear));
                sysAnnualReportDto.setStatus("0");
                baseManager.insert(sysAnnualReportDto);
                addedCount++;
            }
        }
        return addedCount;
    }
}