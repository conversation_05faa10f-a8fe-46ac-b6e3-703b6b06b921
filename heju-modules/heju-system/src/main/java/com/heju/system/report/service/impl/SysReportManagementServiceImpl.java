package com.heju.system.report.service.impl;

import com.heju.common.core.constant.system.ReportConstants;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.declaration.service.impl.SysTaxFilingsServiceImpl;
import com.heju.system.report.domain.dto.*;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import com.heju.system.report.manager.impl.SysReportManagementManager;
import com.heju.system.report.service.ISysReportManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * 报表管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class    SysReportManagementServiceImpl extends BaseServiceImpl<SysReportManagementQuery, SysReportManagementDto, SysReportManagementManager> implements ISysReportManagementService {

    @Autowired
    SysReportManagementManager reportManagementManager;

    @Autowired
    SysBankReportServiceImpl bankReportService;

    @Autowired
    SysBillReportServiceImpl billReportService;

    @Autowired
    SysFinanceReportServiceImpl financeReportService;

    @Autowired
    SysTaxFilingsServiceImpl taxFilingsService;


    /**
     * 查询公司报表类型
     *
     * @param query 数据查询对象
     * @return 报表类型list  List<SysReportManagementDto>
     */
    public Map<String, Object> selectListPage(SysReportManagementQuery query) {

        //查询所有报表
        List<SysReportManagementQuery> allReport = reportManagementManager.Conformity(query);
        List<SysReportManagementDto> result = new ArrayList<>();
        Map<String, Object> pageResult = new HashMap<>();
        if (Objects.isNull(allReport)){
            return pageResult;
        }
        for (SysReportManagementQuery report : allReport) {
            SysReportManagementDto dto = new SysReportManagementDto();
            dto.setEntityId(report.getEntityId());
            dto.setEntityName(report.getEntityName());

            //银行报表
            dto.updateBankMap(report.getBankReports());
            //发票
            dto.updateBillMap(report.getBillReports());
            //财税报表
            dto.updateFinanceMap(report.getFinanceReports());
            //税务申报
            dto.updateTaxMap(report.getTaxReports());

            result.add(dto);
        }
        // 计算总记录数和总页数
        int totalCount = result.size();
        int totalPages = (totalCount + query.getPageSize() - 1) / query.getPageSize();

        // 分页查询结果
        int fromIndex = (query.getPage() - 1) * query.getPageSize();
        int toIndex = Math.min(fromIndex + query.getPageSize(), totalCount);
        List<SysReportManagementDto> pagedResult = result.subList(fromIndex, toIndex);

        // 封装分页信息到 Map 中
        pageResult.put("data", pagedResult);
        pageResult.put("page", query.getPage());
        pageResult.put("pageSize", query.getPageSize());
        pageResult.put("totalCount", totalCount);
        pageResult.put("totalPages", totalPages);
        return pageResult;
    }

    @Override
    public List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query) {

        if (query.getReportClass().equals(ReportConstants.reportClass.BANK_REPORT.getCode())){
            return bankReportService.selectByCondition(query);
        }
        if (query.getReportClass().equals(ReportConstants.reportClass.BILL_REPORT.getCode())){
            return billReportService.selectByCondition(query);
        }
        if (query.getReportClass().equals(ReportConstants.reportClass.FINANCE_REPORT.getCode())){
            return financeReportService.selectByCondition(query);
        }
        if (query.getReportClass().equals(ReportConstants.reportClass.TAX_REPORT.getCode())){
            return taxFilingsService.selectByCondition(query);
        }
        return null;
    }
}
