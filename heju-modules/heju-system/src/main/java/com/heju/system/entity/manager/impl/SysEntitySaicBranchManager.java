package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.query.SysEntitySaicBranchQuery;
import com.heju.system.entity.domain.model.SysEntitySaicBranchConverter;
import com.heju.system.entity.mapper.SysEntitySaicBranchMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntitySaicBranchManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体工商分支机构管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntitySaicBranchManager extends BaseManagerImpl<SysEntitySaicBranchQuery, SysEntitySaicBranchDto, SysEntitySaicBranchPo, SysEntitySaicBranchMapper, SysEntitySaicBranchConverter> implements ISysEntitySaicBranchManager {
    @Override
    public List<SysEntitySaicBranchPo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntitySaicBranchPo> lambdaBranch = new LambdaQueryWrapper<>();
        lambdaBranch.eq(SysEntitySaicBranchPo::getEntityId,id);
        return baseMapper.selectList(lambdaBranch);
    }
}