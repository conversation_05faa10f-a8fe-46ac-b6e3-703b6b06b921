package com.heju.system.annualReport.domain.query;

import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工商年报 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAnnualReportQuery extends SysAnnualReportPo {

    @Serial
    private static final long serialVersionUID = 1L;

}