package com.heju.system.monitor.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.log.domain.dto.SysOperateLogDto;
import com.heju.system.api.log.domain.po.SysOperateLogPo;
import com.heju.system.api.log.domain.query.SysOperateLogQuery;

/**
 * 操作日志管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysOperateLogMapper extends BaseMapper<SysOperateLogQuery, SysOperateLogDto, SysOperateLogPo> {
}
