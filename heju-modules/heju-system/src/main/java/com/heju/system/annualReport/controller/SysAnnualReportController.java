package com.heju.system.annualReport.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import com.heju.system.annualReport.service.ISysAnnualReportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 工商年报管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/report")
public class SysAnnualReportController extends BaseController<SysAnnualReportQuery, SysAnnualReportDto, ISysAnnualReportService> {

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "工商年报";
    }

    /**
     * 查询工商年报管理列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_ANNUAL_REPORT_LIST)
    public AjaxResult list(SysAnnualReportQuery annualReport) {
        startPage();
        List<SysAnnualReportDto> sysAnnualReportDtos = baseService.selectListScope(annualReport);
        return getDataTable(sysAnnualReportDtos);
    }

    /**
     * 查询工商年报列表 排除未申报
     */
    @GetMapping("/partList")
    public AjaxResult partList(SysAnnualReportQuery annualReport) {
        startPage();
        List<SysAnnualReportDto> list = baseService.selectPartList(annualReport);
        return getDataTable(list);
    }

    /**
     * 查询工商年报详细
     */
    @Override
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(Auth.SYS_ANNUAL_REPORT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        SysAnnualReportDto sysAnnualReportDto = baseService.selectById(id);
        return AjaxResult.success(sysAnnualReportDto);
    }

    /**
     * 工商年报新增/修改
     *
     * @param annualReport 工商年报对象
     * @return 判断是否存在记录，存在即走修改; 否则走新增
     */
    @Override
    @PostMapping
//    @RequiresPermissions(Auth.SYS_ANNUAL_REPORT_ADD)
    @Log(title = "工商年报管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysAnnualReportDto annualReport) {
        SysAnnualReportDto dto = baseService.selectByEntityAndYear(annualReport);
        // 有记录， 修改
        if (dto != null) {
            annualReport.setId(dto.getId());
            // 根据 能否申报 修改状态
            if (annualReport.getIsReport() != null) {
                if (annualReport.getIsReport() == 0) {
                    annualReport.setStatus("1");
                } else {
                    annualReport.setStatus("2");
                }
            }
            annualReport.initOperate(BaseConstants.Operate.EDIT);
            AEHandle(annualReport.getOperate(), annualReport);
            return toAjax(baseService.update(annualReport));
        } else {
        // 无记录， 新增
            // 根据 能否申报 修改状态
            if (annualReport.getIsReport() != null) {
                if (annualReport.getIsReport() == 0) {
                    annualReport.setStatus("1");
                } else {
                    annualReport.setStatus("2");
                }
            }
            annualReport.initOperate(BaseConstants.Operate.ADD);
            AEHandle(annualReport.getOperate(), annualReport);
            return toAjax(baseService.insert(annualReport));
        }
    }

    /**
     * 工商年报修改
     */

//    @Override
//    @PutMapping
//    @RequiresPermissions(Auth.SYS_ANNUAL_REPORT_EDIT)
//    @Log(title = "工商年报管理", businessType = BusinessType.UPDATE)
//    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysAnnualReportDto annualReport) {
//        annualReport.initOperate(BaseConstants.Operate.EDIT);
//        AEHandle(annualReport.getOperate(), annualReport);
//        return toAjax(baseService.update(annualReport));
//    }

    /**
     * 工商年报修改状态
     */
    @Override
    @PutMapping("/status")
//    @RequiresPermissions(value = {Auth.SYS_ANNUAL_REPORT_EDIT, Auth.SYS_ANNUAL_REPORT_ES}, logical = Logical.OR)
    @Log(title = "工商年报管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysAnnualReportDto annualReport) {
        annualReport.initOperate(BaseConstants.Operate.EDIT_STATUS);
        AEHandle(annualReport.getOperate(), annualReport);
        return toAjax(baseService.updateStatus(annualReport));
    }

    /**
     * 工商年报批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
//    @RequiresPermissions(Auth.SYS_ANNUAL_REPORT_DEL)
    @Log(title = "工商年报管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        RHandleEmpty(idList);
        RHandle(BaseConstants.Operate.DELETE, idList);
        return toAjax(baseService.deleteByIds(idList));
    }

    /**
     * 获取工商年报选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 获取实体名称选择框列表
     */
    @GetMapping("/entity")
    public AjaxResult entity() {
        List<SysAnnualReportDto> sysEntityPos = baseService.selectEntityOption();
        return AjaxResult.success(sysEntityPos);
    }

    /**
     * 工商年报-通过
     */
    @PostMapping("/pass")
    @Log(title = "工商年报管理", businessType = BusinessType.PASS)
    public AjaxResult pass(@RequestBody SysAnnualReportDto annualReport) {
        return baseService.pass(annualReport) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 工商年报-驳回
     */
    @PostMapping("/reject")
    @Log(title = "工商年报管理", businessType = BusinessType.NO_PASS)
    public AjaxResult reject(@RequestBody SysAnnualReportDto annualReport) {
        return baseService.reject(annualReport) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 检查当前年份是否需要新增年报 定时检查
     */
    @PostMapping("/check")
    @InnerAuth
    public R<Integer> check() {
        return R.ok(baseService.check());
    }
}
