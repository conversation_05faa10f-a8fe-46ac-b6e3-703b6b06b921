package com.heju.system.forms.cascade.service.impl;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.po.CascadeTree;
import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.cascade.service.ISysCascadeService;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.service.ISysOptionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 级联管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysCascadeServiceImpl extends BaseServiceImpl<SysCascadeQuery, SysCascadeDto, ISysCascadeManager> implements ISysCascadeService {



    @Resource
    private ISysOptionService optionService;


    /**
     * 查询级联对象列表 | 数据权限
     *
     * @param cascade 级联对象
     * @return 级联对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysCascadeMapper"})
    public List<SysCascadeDto> selectListScope(SysCascadeQuery cascade) {
        List<SysCascadeDto> sysCascadeDtos = baseManager.selectList(cascade);
        Set<Long> collect = sysCascadeDtos.stream().map(SysCascadeDto::getMainOptionId).collect(Collectors.toSet());
        collect.addAll(sysCascadeDtos.stream().map(SysCascadeDto::getCascade1OptionId).collect(Collectors.toSet()));
        collect.addAll(sysCascadeDtos.stream().map(SysCascadeDto::getCascade2OptionId).collect(Collectors.toSet()));
        collect.addAll(sysCascadeDtos.stream().map(SysCascadeDto::getCascade3OptionId).collect(Collectors.toSet()));
        collect.addAll(sysCascadeDtos.stream().map(SysCascadeDto::getCascade4OptionId).collect(Collectors.toSet()));
        if(!collect.isEmpty()) {
            List<SysOptionDto> sysOptionDtos = optionService.selectListByIds(collect);
            Map<Long, String> optionIdNameMap = sysOptionDtos.stream().collect(Collectors.toMap(SysOptionDto::getId, SysOptionDto::getName, (v1, v2) -> v1));
            sysCascadeDtos.forEach(
                    sysCascadeDto -> {
                        sysCascadeDto.setMainOptionName(optionIdNameMap.get(sysCascadeDto.getMainOptionId()));
                        sysCascadeDto.setCascade1OptionName(optionIdNameMap.get(sysCascadeDto.getCascade1OptionId()));
                        sysCascadeDto.setCascade2OptionName(optionIdNameMap.get(sysCascadeDto.getCascade2OptionId()));
                        sysCascadeDto.setCascade3OptionName(optionIdNameMap.get(sysCascadeDto.getCascade3OptionId()));
                        sysCascadeDto.setCascade4OptionName(optionIdNameMap.get(sysCascadeDto.getCascade4OptionId()));
                    }

            );
        }
        return sysCascadeDtos;
    }

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysCascadeDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        if(row>0) {
            List<CascadeTree> cascadeTreeList = dto.getCascadeTreeList();
            baseManager.insertCascadeRelation(cascadeTreeList,dto.getId());
        }
        return row;
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysCascadeDto dto) {
        SysCascadeDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        baseManager.deleteCascadeRelation(dto.getId());
        baseManager.insertCascadeRelation(dto.getCascadeTreeList(),dto.getId());
        return row;
    }

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysCascadeDto selectById(Serializable id) {
        SysCascadeDto dto = baseManager.selectById(id);
        List<Tree<String>>sysCascadeTrees = baseManager.selectCascadeRelation(dto.getId());
        dto.setCascadeTreeStringList(sysCascadeTrees);
        return dto;
    }

}