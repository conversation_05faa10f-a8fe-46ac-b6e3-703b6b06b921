package com.heju.system.notice.manager.impl;

import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.notice.domain.dto.SysNoticeDto;
import com.heju.system.notice.domain.model.SysNoticeConverter;
import com.heju.system.notice.domain.po.SysNoticePo;
import com.heju.system.notice.domain.query.SysNoticeQuery;
import com.heju.system.notice.manager.ISysNoticeManager;
import com.heju.system.notice.mapper.SysNoticeMapper;
import org.springframework.stereotype.Component;

/**
 * 通知公告管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysNoticeManagerImpl extends BaseManagerImpl<SysNoticeQuery, SysNoticeDto, SysNoticePo, SysNoticeMapper, SysNoticeConverter> implements ISysNoticeManager {
}
