package com.heju.system.third.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import java.util.List;

/**
 * 第三方认证管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysThirdAuthMasterService extends IBaseService<SysThirdAuthQuery, SysThirdAuthDto> {



    /**
     * 校验第三方认证信息编码是否唯一
     *
     * @param Id   第三方认证信息Id
     * @param code 第三方认证信息编码
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkThirdAuthCodeUnique(Long Id, String code);

    /**
     * 超管下，查询认证账号
     * @param thirdId
     * @return
     */
    List<SysCompanyThirdAuthMergeDto> getTenantCpmListAdmin(Long thirdId);

    /**
     * 超管下，配置认证账号
     * @param dto
     * @return
     */
    int addTenantCpmAuthAdmin(SysCompanyThirdAuthMergeAddDto dto);

    List<SysThirdAuthDto> optionByThirdId(Long thirdId);

}