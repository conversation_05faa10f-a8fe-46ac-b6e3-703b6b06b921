package com.heju.system.declaration.mapper;

import com.heju.system.declaration.domain.query.SysBusinessAnnualReportQuery;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.po.SysBusinessAnnualReportPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.Select;

/**
 * 工商年报管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysBusinessAnnualReportMapper extends BaseMapper<SysBusinessAnnualReportQuery, SysBusinessAnnualReportDto, SysBusinessAnnualReportPo> {
    @Select("select * from sys_business_annual_report where entity_id = #{entityId} and year = #{year} and del_flag = '0'")
    SysBusinessAnnualReportDto selectByCondition(SysBusinessAnnualReportDto dto);
}