package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntityFieldPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体字段管理 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityFieldDto extends SysEntityFieldPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private String oldfieldName;

}