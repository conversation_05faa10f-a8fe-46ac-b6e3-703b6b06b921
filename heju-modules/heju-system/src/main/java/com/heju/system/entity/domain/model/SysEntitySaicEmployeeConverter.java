package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.po.SysEntitySaicEmployeePo;
import com.heju.system.entity.domain.query.SysEntitySaicEmployeeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体工商董事会成员 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntitySaicEmployeeConverter extends BaseConverter<SysEntitySaicEmployeeQuery, SysEntitySaicEmployeeDto, SysEntitySaicEmployeePo> {
}
