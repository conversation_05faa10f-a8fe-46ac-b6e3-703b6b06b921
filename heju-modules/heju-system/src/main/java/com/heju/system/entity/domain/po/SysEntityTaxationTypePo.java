package com.heju.system.entity.domain.po;

import java.time.LocalDate;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体税务税费种认定 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_taxation_type", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK, NAME })
public class SysEntityTaxationTypePo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 征收项目 */
    @Excel(name = "征收项目")
    protected String collectionProject;

    /** 征收品目 */
    @Excel(name = "征收品目")
    protected String collectionItem;

    /** 纳税期限 */
    @Excel(name = "纳税期限")
    protected String taxPaymenPeriod;

    /** 有限期起 */
    @Excel(name = "有限期起")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected LocalDate starttime;

    /** 有限期止 */
    @Excel(name = "有限期止")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected LocalDate endtime;

    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

}