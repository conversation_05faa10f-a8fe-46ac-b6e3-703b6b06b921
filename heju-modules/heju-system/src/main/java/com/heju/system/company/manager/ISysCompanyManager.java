package com.heju.system.company.manager;

import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.company.domain.dto.CompanyDeptPostDto;
import com.heju.system.company.domain.dto.CompanyDeptPostIdDto;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.domain.query.SysCompanyQuery;
import com.heju.common.web.entity.manager.IBaseManager;

import java.util.List;

/**
 * 子公司管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysCompanyManager extends IBaseManager<SysCompanyQuery, SysCompanyDto> {

    /**
     * 校验公司编码是否唯一
     *
     * @param Id   公司Id
     * @param code 公司编码
     * @return 公司对象
     */
    SysCompanyDto checkCompanyCodeUnique(Long Id, String code);

    /**
     * 根据公司信息组装公司-部门-岗位-用户list
     * @param dto 公司信息
     */
    void getDeptPostById(SysCompanyDto dto, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList);

    /**
     * 多条信息同步第三方
     * @param companyDeptPostDtoIdList 需删除的数据id
     * @param companyDeptPostDtoList 需新增的数据list
     * @param type 操作类型 1-新增 3-删除
     * @param companyThirdAuthMergeList  公司-第三方认证信息关联
     */
    void buildCompany(List<CompanyDeptPostIdDto> companyDeptPostDtoIdList, List<CompanyDeptPostDto> companyDeptPostDtoList, int type, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList);


    void buildDept( List<SysDeptDto> originList);

    void buildPost( List<SysPostDto> originList);

    void buildUser( List<SysUserDto> originList);

    /**
     * 单条信息同步第三方
     * @param operateType 1-新增；2-修改；3-删除
     * @param organizeType 1-公司；2-部门；3-岗位；4-用户
     * @param companyThirdAuthMergeList 第三方认证信息-公司关联list
     * @param organizeInfo 同步的数据
     */
    void buildOrganizeOne(int operateType,int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList,String organizeInfo);

    /**
     * 用户-单条信息同步第三方
     * @param nowCompanyIds 修改前后公司id交集
     * @param addCompany 需新增公司id
     * @param delCompany 需删除公司id
     * @param originDto 修改前用户信息
     * @param dto 修改后用户信息
     */
    void buildOrganizeUserOne(List<Long> nowCompanyIds,List<Long> addCompany,List<Long> delCompany,SysUserDto originDto,SysUserDto dto);

    /**
     * 用户-单条信息同步第三方
     * @param operateType 1-新增；2-修改；3-删除
     * @param organizeType 1-公司；2-部门；3-岗位；4-用户
     * @param companyThirdAuthMergeList 第三方认证信息-公司关联list
     * @param dto 同步的数据
     */
    void buildOrganizeUserOne(int operateType,int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList,SysUserDto dto);
}