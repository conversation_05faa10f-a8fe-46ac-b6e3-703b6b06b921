package com.heju.system.authority.mapper.merge;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BasicMapper;
import com.heju.system.authority.domain.merge.SysRoleMenuMerge;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色-菜单关联 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysRoleMenuMergeMapper extends BasicMapper<SysRoleMenuMerge> {

    @Select("select * from sys_role_menu_merge where role_id = #{roleId}")
    List<SysRoleMenuMerge> selectByRoleId(Long roleId);
}
