package com.heju.system.entity.service.impl;

import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.entity.domain.FieldBelongConstant;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.query.SysEntityExamineQuery;
import com.heju.system.entity.manager.ISysEntityExamineManager;
import com.heju.system.entity.manager.ISysEntityTaxationInvoiceTypeManager;
import com.heju.system.entity.manager.ISysEntityTaxationTypeManager;
import com.heju.system.entity.manager.ISysEntityTaxationUserManager;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.entity.service.ISysEntityExamineService;
import com.heju.system.organize.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实体信息审核管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityExamineServiceImpl extends BaseServiceImpl<SysEntityExamineQuery, SysEntityExamineDto, ISysEntityExamineManager> implements ISysEntityExamineService {

    @Resource
    private SysEntityMapper sysEntityMapper;

    @Resource
    private ISysEntityTaxationUserManager sysEntityTaxationUserManager;

    @Resource
    private ISysEntityTaxationTypeManager sysEntityTaxationTypeManager;

    @Resource
    private ISysEntityTaxationInvoiceTypeManager sysEntityTaxationInvoiceTypeManager;

    @Resource
    private ISysUserService sysUserService;

    /**
     * 查询实体信息审核对象列表 | 数据权限
     *
     * @param entityExamine 实体信息审核对象
     * @return 实体信息审核对象集合
     */
    @Override
//    @DataScope(userAlias = "createBy", mapperScope = {"SysEntityExamineMapper"})
    public List<SysEntityExamineDto> selectListScope(SysEntityExamineQuery entityExamine) {
        List<SysEntityExamineDto> sysEntityExamineDtos = baseManager.selectList(entityExamine);
        if(sysEntityExamineDtos.size()>0) {
            List<Long> createByList = sysEntityExamineDtos.stream().map(BaseEntity::getCreateBy).collect(Collectors.toList());
            List<SysUserDto> userDtoList = sysUserService.selectListByIds(createByList);
            Map<Long, String> userMap = userDtoList.stream().collect(Collectors.toMap(SysUserDto::getId, SysUserDto::getNickName));
            sysEntityExamineDtos.forEach(sysEntityExamineDto -> sysEntityExamineDto.setCreateName(userMap.get(sysEntityExamineDto.getCreateBy())));
        }
        return sysEntityExamineDtos;
    }

    @Override
    public AjaxResult editStatus(SysEntityExamineDto entityExamine) {
        SysEntityExamineDto sysEntityExamineDto = baseManager.selectById(entityExamine.getId());
        sysEntityExamineDto.setExamineStatus(entityExamine.getExamineStatus());
        if(entityExamine.getRemark()!=null)
            sysEntityExamineDto.setRemark(entityExamine.getRemark());
        int update = baseManager.update(sysEntityExamineDto);
        //审核通过,更新实体信息表字段值
        if (update > NumberUtil.Zero && entityExamine.getExamineStatus().equals(NumberUtil.One)) {
            String fieldBelong = sysEntityExamineDto.getFieldBelong();
            switch (fieldBelong) {
                case FieldBelongConstant.SAIC_BASIC,
                        FieldBelongConstant.SAIC_OTHER -> sysEntityMapper.updateSAICExamine(sysEntityExamineDto);

                case FieldBelongConstant.TAXATION_BASIC,
                        FieldBelongConstant.TAXATION_SOFTWARE,
                        FieldBelongConstant.TAXATION_OTHER -> sysEntityMapper.updateTaxationExamine(sysEntityExamineDto);

                case FieldBelongConstant.TAXATION_USER -> sysEntityTaxationUserManager.updateByExamine(sysEntityExamineDto);

                case FieldBelongConstant.TAXATION_TYPE -> sysEntityTaxationTypeManager.updateByExamine(sysEntityExamineDto);

                case FieldBelongConstant.INVOICE_TYPE ->
                        sysEntityTaxationInvoiceTypeManager.updateByExamine(sysEntityExamineDto);

                default -> {
                }
            }
        }
        return update > 0 ? AjaxResult.success() : AjaxResult.error();
    }
}