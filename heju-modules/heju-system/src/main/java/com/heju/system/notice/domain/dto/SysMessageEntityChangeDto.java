package com.heju.system.notice.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import com.heju.system.notice.domain.po.SysMessagePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 消息实体变更通知 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMessageEntityChangeDto extends SysMessageEntityChangePo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 消息变更id */
    @TableId(type = IdType.AUTO)
    private Long id;
}
