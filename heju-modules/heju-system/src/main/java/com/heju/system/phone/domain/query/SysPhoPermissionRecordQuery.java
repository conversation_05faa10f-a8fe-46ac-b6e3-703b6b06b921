package com.heju.system.phone.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.system.phone.domain.po.SysPhoPermissionRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 手机号授权 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysPhoPermissionRecordQuery extends SysPhoPermissionRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 保管人
     */
    private Long custody;

    /**
     * 授权条件查询时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime queryTime;

}
