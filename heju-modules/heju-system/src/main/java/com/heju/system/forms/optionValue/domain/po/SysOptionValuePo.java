package com.heju.system.forms.optionValue.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 选项值 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_option_value", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, CREATE_TIME, UPDATE_TIME, REMARK })
public class SysOptionValuePo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** API名称 */
    @Excel(name = "API名称")
    protected String apiName;

    /** 选项id */
    @Excel(name = "选项id")
    protected Long optionId;

}