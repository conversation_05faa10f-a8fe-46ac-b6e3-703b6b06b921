package com.heju.system.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.entity.domain.po.CompanyAbnormalInformationPo;
import org.apache.ibatis.annotations.Insert;

import java.util.List;

@Isolate
public interface SysEntityExceptionInfoHistoryRecordsMapper extends BaseMapper<CompanyAbnormalInformationPo> {

//    @Delete("delete from sys_entity_exception_info_history_records where org_name = #{name}")
//    void delete2(String name );

    @Insert("<script>" +
            "INSERT INTO sys_entity_exception_info_history_records (company_name,org_name, i_reason, o_date, o_reason, i_date) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.companyName},#{item.orgName}, #{item.iReason}, #{item.oDate}, #{item.oReason}, #{item.iDate})" +
            "</foreach>" +
            "</script>")
    void insertBatches(List<CompanyAbnormalInformationPo> list);

}
