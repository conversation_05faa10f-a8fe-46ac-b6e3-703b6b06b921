package com.heju.system.organize.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.datascope.annotation.DataScope;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.query.SysPostQuery;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.service.ISysCompanyService;
import com.heju.system.organize.manager.impl.SysPostManagerImpl;
import com.heju.system.organize.service.ISysPostService;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.system.utils.OperateEnum;
import com.heju.system.utils.OrganizeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 岗位管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
@Isolate
public class SysPostServiceImpl extends BaseServiceImpl<SysPostQuery, SysPostDto, SysPostManagerImpl> implements ISysPostService {

    @Resource
    private ISysCompanyService companyService;

    @Resource
    private ISysThirdAuthManager thirdAuthManager;

    /**
     * 用户登录校验 | 根据部门Ids获取归属岗位对象集合
     *
     * @param deptIds 部门Ids
     * @return 岗位对象集合
     */
    @Override
    public List<SysPostDto> selectListByDeptIds(Collection<Long> deptIds) {
        return baseManager.selectListByDeptIds(deptIds);
    }

    /**
     * 新增岗位 | 内部调用
     *
     * @param post 岗位对象
     * @return 结果
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int addInner(SysPostDto post) {
        return super.insert(post);
    }

    /**
     * 查询岗位对象列表 | 数据权限 | 附加数据
     *
     * @param post 岗位对象
     * @return 岗位对象集合
     */
    @Override
    @DataScope(postAlias = "id", mapperScope = {"SysPostMapper"})
    public List<SysPostDto> selectListScope(SysPostQuery post) {
        return baseManager.selectList(post);
    }

    /**
     * 校验岗位编码是否唯一
     *
     * @param Id   岗位Id
     * @param code 岗位编码
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkPostCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkPostCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }

    @Override
    @DSTransactional
    public int insert(SysPostDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        List<Long> companyIds=new ArrayList<>();
        companyIds.add(dto.getCompanyId());
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthManager.getByCompanyIds(companyIds);
        companyService.buildOrganizeOne(OperateEnum.ADD.getCode(), OrganizeEnum.POST.getCode(),companyThirdAuthMergeList, JSON.toJSONString(dto));
        return row;
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysPostDto dto) {
        SysPostDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        if(dto.getCompanyId().equals(originDto.getCompanyId())) {
            List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthManager.getByCompanyId(dto.getCompanyId());
            companyService.buildOrganizeOne(OperateEnum.EDIT.getCode(), OrganizeEnum.POST.getCode(), companyThirdAuthMergeList, JSON.toJSONString(dto));
        }
        return row;
    }

    /**
     * 根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    @Override
    @DSTransactional
    public int deleteByIds(Collection<? extends Serializable> idList) {
        List<SysPostDto> originList = selectListByIds(idList);
        startBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, originList, null);
        int rows = baseManager.deleteByIds(idList);
        endBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, rows, originList, null);
        companyService.buildPost(originList);
        return rows;
    }


}
