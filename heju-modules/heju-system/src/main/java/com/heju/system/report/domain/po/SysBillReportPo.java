package com.heju.system.report.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 发票管理详情 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_bill_report", excludeProperty = { NAME })
public class SysBillReportPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体id */
    @Excel(name = "公司id")
    protected Long entityId;

    /** 实体名称 */
    @Excel(name = "实体名称")
    protected String entityName;

    /** 发票编码 */
    @Excel(name = "发票编码")
    protected String code;

    /** 发票类型(0 专票,1 普票) */
    @Excel(name = "发票类型(0 专票,1 普票)")
    protected String billType;

    /** 报表时间类型(0月度,1季度,2年度) */
    @Excel(name = "报表时间类型(0月度,1季度,2年度)")
    protected String reporttimeType;

    /** 年份 */
    @Excel(name = "年份")
    protected String year;

    /** 月份 */
    @Excel(name = "月份")
    protected String month;

    /** 季度(0春季,1夏季,2秋季,3冬季) */
    @Excel(name = "季度(0春季,1夏季,2秋季,3冬季)")
    protected String season;

    /** 报表地址 */
    @Excel(name = "报表地址")
    protected String reportAddress;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}