package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import com.heju.system.entity.domain.dto.SysEntityTaxationUserDto;
import com.heju.system.entity.domain.query.SysEntityTaxationUserQuery;
import com.heju.system.entity.domain.model.SysEntityTaxationUserConverter;
import com.heju.system.entity.mapper.SysEntityTaxationUserMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntityTaxationUserManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体税务人员信息管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntityTaxationUserManager extends BaseManagerImpl<SysEntityTaxationUserQuery, SysEntityTaxationUserDto, SysEntityTaxationUserPo, SysEntityTaxationUserMapper, SysEntityTaxationUserConverter> implements ISysEntityTaxationUserManager {
    @Override
    public List<SysEntityTaxationUserPo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntityTaxationUserPo> lambdaUser = new LambdaQueryWrapper<>();
        lambdaUser.eq(SysEntityTaxationUserPo::getEntityId,id);
        return baseMapper.selectList(lambdaUser);
    }

    @Override
    public int updateByExamine(SysEntityExamineDto entityExamine) {
        UpdateWrapper<SysEntityTaxationUserPo> wrapper=new UpdateWrapper<>();
        wrapper.eq("id",entityExamine.getListId()).set(entityExamine.getFieldName(),entityExamine.getAfterText());
        return baseMapper.update(null,wrapper);
    }
}