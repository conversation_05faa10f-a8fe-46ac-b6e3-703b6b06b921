package com.heju.system.authority.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.system.api.authority.domain.dto.SysTenantMenuDto;
import com.heju.system.authority.domain.merge.SysTenantMenuMerge;
import com.heju.system.authority.domain.vo.SysAuthTree;
import com.heju.system.authority.manager.ISysAuthManager;
import com.heju.system.authority.mapper.merge.SysTenantMenuMergeMapper;
import com.heju.system.authority.service.ISysAuthService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 权限管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysAuthServiceImpl implements ISysAuthService {

    @Resource
    private ISysAuthManager authManager;
    @Resource
    private SysTenantMenuMergeMapper sysTenantMenuMergeMapper;

    /**
     * 获取公共模块|菜单权限树
     *
     * @return 权限对象集合
     */
    @Override
    public List<SysAuthTree> selectCommonAuthScope() {
        return authManager.selectCommonAuthScope();
    }

    /**
     * 获取企业模块|菜单权限树
     *
     * @return 权限对象集合
     */
    @Override
    public List<SysAuthTree> selectEnterpriseAuthScope() {
        return authManager.selectEnterpriseAuthScope();
    }

    /**
     * 获取角色组菜单权限
     * @param roleGroupId
     * @return
     */
    @Override
    public List<SysAuthTree> selectRoleGroupAuthScope(Long roleGroupId) {
        return authManager.selectRoleGroupAuthScope(roleGroupId);
    }

    /**
     * 获取租户权限对象集合
     *
     * @return 权限对象集合
     */
    @Override
    public List<SysAuthTree> selectTenantAuth() {
        return authManager.selectTenantAuth();
    }

    /**
     * 获取角色权限对象集合
     *
     * @param roleId 角色Id
     * @return 权限对象集合
     */
    @Override
    public List<SysAuthTree> selectRoleAuth(Long roleId) {
        return authManager.selectRoleAuth(roleId);
    }

    /**
     * 新增租户权限
     *
     * @param authIds 权限Ids
     */
    @Override
    @DSTransactional
    public void addTenantAuth(Long[] authIds,Long enterpriseId) {
        authManager.addTenantAuth(authIds,enterpriseId);
    }

    /**
     * 修改租户权限
     *
     * @param authIds 权限Ids
     */
    @Override
    @DSTransactional
    public void editTenantAuth(Long[] authIds) {
        authManager.editTenantAuth(authIds);
    }

    /**
     * 新增角色权限
     *
     * @param roleId  角色Id
     * @param authIds 权限Ids
     */
    @Override
    @DSTransactional
    public void addRoleAuth(Long roleId, Long[] authIds) {
        authManager.addRoleAuth(roleId, authIds);
    }

    /**
     * 修改角色权限
     *
     * @param roleId  角色Id
     * @param authIds 权限Ids
     */
    @Override
    @DSTransactional
    public void editRoleAuth(Long roleId, Long[] authIds) {
        authManager.editRoleAuth(roleId, authIds);
    }
    @Override
    public void tenantDeleteMenus(List<Long> menuIds) {
        if (menuIds != null && menuIds.size() != 0) {
            QueryWrapper<SysTenantMenuMerge> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SysTenantMenuMerge::getMenuId, menuIds);
            sysTenantMenuMergeMapper.delete(queryWrapper);
        }

    }

    @Override
    public void tenantUpdateMenus(SysTenantMenuDto sysTenantMenuDto) {
        List<SysTenantMenuMerge> sysTenantMenuMergeList = new ArrayList<>();
        List<Long> menuIdList = sysTenantMenuDto.getMenuIdList();
        for (Long menuId : menuIdList) {
            SysTenantMenuMerge sysTenantMenuMerge = new SysTenantMenuMerge();
            sysTenantMenuMerge.setTenantId(sysTenantMenuDto.getTenantId());
            sysTenantMenuMerge.setMenuId(menuId);
            sysTenantMenuMergeList.add(sysTenantMenuMerge);
        }
        sysTenantMenuMergeMapper.insertBatch(sysTenantMenuMergeList);

    }

    @Override
    public List<Long> tenantHasMenuIds(Long tenantId) {
        QueryWrapper<SysTenantMenuMerge> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysTenantMenuMerge::getTenantId , tenantId);
        List<SysTenantMenuMerge> list = sysTenantMenuMergeMapper.selectList(queryWrapper);
        List<Long> menuIdList = new ArrayList<>();
        for (SysTenantMenuMerge sysTenantMenuMerge : list) {
            menuIdList.add(sysTenantMenuMerge.getMenuId());
        }
        return menuIdList;
    }


}
