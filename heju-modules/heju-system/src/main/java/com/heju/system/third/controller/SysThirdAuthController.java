package com.heju.system.third.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.service.ISysThirdAuthService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.io.Serializable;
import java.util.List;

/**
 * 第三方认证管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/thirdAuth")
public class SysThirdAuthController extends BaseController<SysThirdAuthQuery, SysThirdAuthDto, ISysThirdAuthService> {



    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "第三方认证" ;
    }

    /**
     * 查询第三方认证列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_LIST)
    public AjaxResult list(SysThirdAuthQuery thirdAuth) {
        return super.list(thirdAuth);
    }

    /**
     * 查询第三方认证详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 第三方认证新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_ADD)
    @Log(title = "第三方认证管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysThirdAuthDto thirdAuth) {
        return super.add(thirdAuth);
    }
    /**
     * 第三方认证修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_EDIT)
    @Log(title = "第三方认证管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysThirdAuthDto thirdAuth) {
        return super.edit(thirdAuth);
    }

    /**
     * 第三方认证修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_THIRD_AUTH_EDIT, Auth.SYS_THIRD_AUTH_ES}, logical = Logical.OR)
    @Log(title = "第三方认证管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysThirdAuthDto thirdAuth) {
        return super.editStatus(thirdAuth);
    }

    /**
     * 第三方认证批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_DEL)
    @Log(title = "第三方认证管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }


    /**
     * 获取第三方认证选择框列表
     */
    @GetMapping("/optionByThirdId/{thirdId}")
    public AjaxResult optionByThirdId(@PathVariable Long thirdId) {
        return AjaxResult.success(baseService.optionByThirdId(thirdId));
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysThirdAuthDto thirdAuth) {
        if (baseService.checkThirdAuthCodeUnique(thirdAuth.getId(), thirdAuth.getCode()))
            warn(StrUtil.format("{}{}{}失败，第三方认证信息编码已存在", operate.getInfo(), getNodeName(), thirdAuth.getName()));
        else if (baseService.checkNameUnique(thirdAuth.getId(), thirdAuth.getName()))
            warn(StrUtil.format("{}{}{}失败，第三方认证信息名称已存在", operate.getInfo(), getNodeName(), thirdAuth.getName()));
    }

    /**
     * 租户 认证信息认证查询企业 当前租户下的全部企业
     */
    @GetMapping("/getTenantCpmList/{thirdId}")
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_CONFIGURE)
    public AjaxResult getTenantCpmList(@PathVariable Long thirdId) {
        System.out.println("****************************");
        List<SysCompanyThirdAuthMergeDto> getTenantCpmList = baseService.getTenantCpmList(thirdId);
        return AjaxResult.success(getTenantCpmList);
    }

    /**
     * 租户 认证信息认证查询企业 当前租户下的全部企业
     */
    @PutMapping("/setTenantCpmList")
    @RequiresPermissions(Auth.SYS_THIRD_AUTH_CONFIGURE_ADD)
    public AjaxResult setTenantCpmList(@RequestBody SysCompanyThirdAuthMergeAddDto dto) {
        System.out.println("****************************");
        return AjaxResult.success(baseService.addTenantCpmAuth(dto));
    }

    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        if(baseService.checkCompanyById(idList)){
            warn(StrUtil.format("{}{}{}失败，第三方认证信息已关联公司，请先解除关联",operate.getInfo(), getNodeName()));
        }
    }
}
