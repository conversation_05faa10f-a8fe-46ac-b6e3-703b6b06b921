package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import com.heju.system.entity.domain.query.SysEntitySaicBranchQuery;

import java.util.List;

/**
 * 实体工商分支机构管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntitySaicBranchManager extends IBaseManager<SysEntitySaicBranchQuery, SysEntitySaicBranchDto> {

    List<SysEntitySaicBranchPo> getByEntityId(Long id);
}