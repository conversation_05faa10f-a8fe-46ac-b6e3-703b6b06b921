package com.heju.system.forms.option.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.domain.query.SysOptionQuery;
import com.heju.system.forms.option.manager.ISysOptionManager;
import com.heju.system.forms.option.service.ISysOptionService;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.manager.ISysOptionValueManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 选项管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysOptionServiceImpl extends BaseServiceImpl<SysOptionQuery, SysOptionDto, ISysOptionManager> implements ISysOptionService {

    @Resource
    private ISysOptionValueManager optionValueManager;


    /**
     * 查询选项对象列表 | 数据权限
     *
     * @param option 选项对象
     * @return 选项对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysOptionMapper"})
    public List<SysOptionDto> selectListScope(SysOptionQuery option) {
        List<SysOptionDto> sysOptionDtos = baseManager.selectList(option);
        if(!sysOptionDtos.isEmpty()) {
            Map<Long, List<SysOptionValuePo>> longListMap = optionValueManager.selectByOptionId(
                    sysOptionDtos.stream().map(SysOptionDto::getId).collect(Collectors.toList()));
            for (SysOptionDto sysOptionDto : sysOptionDtos) {
                List<SysOptionValuePo> sysOptionValuePos = longListMap.get(sysOptionDto.getId());
                List<String> optionValueNames=sysOptionValuePos.stream().map(SysOptionValuePo::getName).toList();
                sysOptionDto.setOptionValues(StringUtils.join(optionValueNames, "、"));
            }
        }
        return sysOptionDtos;
    }



    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysOptionDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        List<SysOptionValueDto> optionValueList = dto.getOptionValueList();
        optionValueList.forEach(sysOptionValueDto -> sysOptionValueDto.setOptionId(dto.getId()));
        optionValueManager.insertBatch(optionValueList);
        return row;
    }

    @Override
    public boolean checkConfigCodeUnique(Long Id, String name) {
        return ObjectUtil.isNotNull(baseManager.checkConfigCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, name));
    }

    /**
     * 获取选择框列表
     *
     */
    @Override
    public AjaxResult option( SysOptionQuery query) {
        try {
            query.setStatus(BaseConstants.Status.NORMAL.getCode());
            return  AjaxResult.success(baseManager.selectList(query));
        } catch (Exception ignored) {
        }
        return AjaxResult.error();
    }

}