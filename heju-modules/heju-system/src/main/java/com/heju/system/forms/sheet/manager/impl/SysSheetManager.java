package com.heju.system.forms.sheet.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.mapper.SysFieldMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.model.SysSheetConverter;
import com.heju.system.forms.sheet.domain.po.SysSheetPo;
import com.heju.system.forms.sheet.domain.query.SysSheetQuery;
import com.heju.system.forms.sheet.manager.ISysSheetManager;
import com.heju.system.forms.sheet.mapper.SysSheetMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 表单管理管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysSheetManager extends BaseManagerImpl<SysSheetQuery, SysSheetDto, SysSheetPo, SysSheetMapper, SysSheetConverter> implements ISysSheetManager {
    @Resource
    private SysFieldMapper fieldMapper;


    @Override
    public void createSheet(SysSheetDto dto) {
        baseMapper.createSheet(dto);
    }

    @Override
    public void updateSheet(SysSheetDto dto) {
//        baseMapper.updateSheet(dto);
    }

    @Override
    public void deleteSheet(SysSheetDto dto) {
        baseMapper.deleteSheet(dto);
    }

    @Override
    public SysSheetPo checkConfigCodeUnique(Long Id, String apiName) {
        SysSheetPo sheet = baseMapper.selectOne(
                Wrappers.<SysSheetPo>query().lambda()
                        .ne(SysSheetPo::getId, Id)
                        .eq(SysSheetPo::getApiName, apiName)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(sheet);
    }

    @Override
    public void insertField(SysSheetDto dto) {
        SysFieldDto fieldDto=new SysFieldDto();
        fieldDto.setName("唯一标识id");
        fieldDto.setApiName("id");
        fieldDto.setFieldType("0");
        fieldDto.setFieldSystemType(1);
        fieldDto.setSheetId(dto.getId());
        fieldDto.setDelFlag(0L);
        fieldDto.setStatus("0");
        fieldDto.setSort(0);
        fieldMapper.insert(fieldDto);
    }

    @Override
    public List<SysFieldPo> selectFieldBySheetId(List<Long> idList) {
        return fieldMapper.selectList(Wrappers.<SysFieldPo>query().lambda().in(SysFieldPo::getSheetId, idList));
    }

    @Override
    public SysSheetDto selectOne(String apiName) {
        SysSheetPo sheet = baseMapper.selectOne(
                Wrappers.<SysSheetPo>query().lambda()
                        .eq(SysSheetPo::getApiName, apiName)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(sheet);
    }

    // 客户信息审核 查出 api_name(sheetName) 对应的 sheetId
    @Override
    public List<Long> selectIdsBySheetName(String sheetName) {
        return baseMapper.selectList(Wrappers.<SysSheetPo>query().lambda()
                        .like(SysSheetPo::getApiName, sheetName))
                .stream().map(SysSheetPo::getId).toList();
    }
}