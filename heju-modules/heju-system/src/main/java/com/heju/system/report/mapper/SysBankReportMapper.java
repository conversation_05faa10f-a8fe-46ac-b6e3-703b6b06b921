package com.heju.system.report.mapper;

import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysBankReportQuery;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.po.SysBankReportPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 银行报表管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysBankReportMapper extends BaseMapper<SysBankReportQuery, SysBankReportDto, SysBankReportPo> {

    @Select("select * from sys_bank_report where del_flag = '0'" +
            "and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)")
    List<SysBankReportDto> findAll(SysReportManagementQuery query);

    @Select("select * from sys_bank_report where del_flag = '0'" +
            "and (case when #{reporttypeType} is not null then reporttype_type = #{reporttypeType} else 1=1 end)" +
            "and (case when #{reporttimeType} is not null then reporttime_type = #{reporttimeType} else 1=1 end)" +
            "and (case when #{year} is not null then year = #{year} else 1=1 end)" +
            "and entity_id = #{entityId}")
    List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query);
}