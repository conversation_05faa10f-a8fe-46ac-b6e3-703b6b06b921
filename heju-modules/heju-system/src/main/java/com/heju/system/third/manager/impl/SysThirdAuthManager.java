package com.heju.system.third.manager.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.redis.service.RedisService;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.po.SysDeptPo;
import com.heju.system.api.organize.domain.po.SysPostPo;
import com.heju.system.company.domain.dto.CompanyDeptPostDto;
import com.heju.system.company.domain.dto.CompanyDeptPostIdDto;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.manager.ISysCompanyManager;
import com.heju.system.organize.manager.ISysUserManager;
import com.heju.system.organize.mapper.SysDeptMapper;
import com.heju.system.organize.mapper.SysPostMapper;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.po.SysThirdAuthPo;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.domain.model.SysThirdAuthConverter;
import com.heju.system.third.mapper.SysThirdAuthMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.system.utils.OperateEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 第三方认证管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysThirdAuthManager extends BaseManagerImpl<SysThirdAuthQuery, SysThirdAuthDto, SysThirdAuthPo, SysThirdAuthMapper, SysThirdAuthConverter> implements ISysThirdAuthManager {

    @Autowired
    protected RedisService redisService;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysPostMapper sysPostMapper;

    @Resource
    private ISysUserManager userManager;

    @Autowired
    private ISysCompanyManager sysCompanyManager;


    @Override
    public SysThirdAuthDto checkThirdAuthCodeUnique(Long Id, String code) {
        SysThirdAuthPo company = baseMapper.selectOne(
                Wrappers.<SysThirdAuthPo>query().lambda()
                        .ne(SysThirdAuthPo::getId, Id)
                        .eq(SysThirdAuthPo::getCode, code)
                        .last(SqlConstants.LIMIT_ONE));
        return mapperDto(company);
    }

    /**
     * 获取删除列表
     * @param companyBeforeMap
     * @param companyAfterMap
     * @return
     */
    @Override
    public List<SysCompanyThirdAuthMerge> getDelList(Map<Long, Long> companyBeforeMap, Map<Long, Long> companyAfterMap){
        //先把当前的数据转成map,key是企业id，vaule是认证id
        Iterator<Map.Entry<Long, Long>> it = companyBeforeMap.entrySet().iterator();
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList=new ArrayList<>();
        while(it.hasNext()) {
            Map.Entry<Long, Long> entry = it.next();
            //companyAfterMap.get(entry.getKey()) 企业id  排除已配置的未选上的删除
            if(companyAfterMap.get(entry.getKey())==null){
                SysCompanyThirdAuthMerge companyThirdAuthMerge=new SysCompanyThirdAuthMerge();
                companyThirdAuthMerge.setCompanyId(entry.getKey());
                companyThirdAuthMerge.setThirdAuthId(entry.getValue());
                companyThirdAuthMergeList.add(companyThirdAuthMerge);

            }
        }
        return companyThirdAuthMergeList;
    }
    @Override
    public List<SysCompanyThirdAuthMerge> getAddList(List<SysCompanyThirdAuthMerge> delList,Map<Long, Long> companyAfterMap,Long thirdAuthId,Long thirdId){
        //thirdAuthId:当前的认证id，  beforeThirdAuthId：之前的认证id
        Iterator<Map.Entry<Long, Long>> it = companyAfterMap.entrySet().iterator();
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList=new ArrayList<>();
        while(it.hasNext()) {
            Map.Entry<Long, Long> entry = it.next();
            Long beforeThirdAuthId=entry.getValue();
            SysCompanyThirdAuthMerge companyThirdAuthMerge=new SysCompanyThirdAuthMerge();
            SysCompanyThirdAuthMerge companyThirdAuthDel=new SysCompanyThirdAuthMerge();
            if(!thirdAuthId.equals(beforeThirdAuthId)){
                companyThirdAuthMerge.setCompanyId(entry.getKey());
                companyThirdAuthMerge.setThirdAuthId(thirdAuthId);
                companyThirdAuthMerge.setThirdId(thirdId);
                companyThirdAuthMergeList.add(companyThirdAuthMerge);
                if(beforeThirdAuthId!=null){
                    companyThirdAuthDel.setCompanyId(entry.getKey());
                    companyThirdAuthDel.setThirdAuthId(beforeThirdAuthId);
                    delList.add(companyThirdAuthDel);
                }
            }
        }
        return companyThirdAuthMergeList;
    }

    @Override
    public void delRedis(List<SysCompanyThirdAuthMergeDto> list) {
        String thirdAuth = CacheConstants.CacheType.SYS_COMPANY_THIRD_AUTH_KEY.getCode();

        Map thirdAuthList = redisService.redisTemplate.opsForHash().entries(thirdAuth);
        Long thirdAuthId = null;

        //redis中所有的企业数据
        for (SysCompanyThirdAuthMergeDto thirdAuthIdList : list) {
            thirdAuthId = thirdAuthIdList.getThirdAuthId();
            String companyList = String.valueOf(thirdAuthList.get(String.valueOf(thirdAuthIdList.getThirdAuthId())));
            if(!ObjectUtil.isNull(companyList) && !ObjectUtil.isNull(thirdAuthIdList) && StringUtils.contains(companyList, String.valueOf(thirdAuthIdList.getCompanyId()))){
                List<String> orders = StrUtil.split(companyList,",");
                orders.remove(String.valueOf(thirdAuthIdList.getCompanyId()));
                String companyId = StringUtils.join(orders, ",");
                thirdAuthList.put(String.valueOf(thirdAuthIdList.getThirdAuthId()),companyId);
            }
        }
        redisService.setCacheMap(CacheConstants.CacheType.SYS_COMPANY_THIRD_AUTH_KEY.getCode(),thirdAuthList);

    }

    @Override
    public List<SysCompanyThirdAuthMerge> selectThirdAuthId(Long companyId, List<SysThirdDto> thirdDtoList) {
        return baseMapper.selectThirdAuthId(companyId,thirdDtoList);
    }

    @Override
    public int deleteCompanyThirdAuthMerge(List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList) {
        return baseMapper.deleteCompanyThirdAuthMerge(companyThirdAuthMergeList);
    }


    @Override
    public List<SysCompanyThirdAuthMerge> getByCompanyIds(List<Long> idList) {
        return baseMapper.getByCompanyIds(idList);
    }

    @Override
    public List<SysCompanyThirdAuthMerge> getByCompanyId(Long companyId) {
        List<Long> idList=new ArrayList<>();
        idList.add(companyId);
        return baseMapper.getByCompanyIds(idList);
    }

    @Override
    @Async
    public void send2RestCloud(List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList,int type) {
        List<CompanyDeptPostIdDto> companyDeptPostIdDtoList=new ArrayList<>();
        List<CompanyDeptPostDto> companyDeptPostDtoList=new ArrayList<>();
        this.setCompanyDeptPostList(companyDeptPostIdDtoList,companyDeptPostDtoList,companyThirdAuthMergeList,type);
        sysCompanyManager.buildCompany(companyDeptPostIdDtoList,companyDeptPostDtoList, type, companyThirdAuthMergeList);
    }

    @Override
    public Map<Long,List<SysUserDto>> getUserListByCompanyId(Map<Long, List<SysDeptPo>> sysDeptPoMap){
        Map<Long,List<SysUserDto>> userMap=new HashMap<>();
        for (Long companyId : sysDeptPoMap.keySet()) {
            List<SysDeptPo> sysDeptPoList = sysDeptPoMap.get(companyId).stream().filter(sysDeptPo -> sysDeptPo.getLevel().equals(NumberUtil.Zero)).collect(Collectors.toList());
            List<Long> deptIdList = sysDeptPoList.stream().map(SysDeptPo::getId).toList();
            List<SysUserDto> listByDeptId = userManager.getListByDeptId(deptIdList);
            userMap.put(companyId,listByDeptId);
        }
        return userMap;
    }

    @Override
    public void setCompanyDeptPostList(List<CompanyDeptPostIdDto> companyDeptPostIdDtoList, List<CompanyDeptPostDto> companyDeptPostDtoList,List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList,int type) {
        Set<Long> companyIds = companyThirdAuthMergeList.stream().map(SysCompanyThirdAuthMerge::getCompanyId).collect(Collectors.toSet());
        List<SysCompanyDto> companyDtoList = sysCompanyManager.selectListByIds(companyIds);
        List<SysDeptPo> sysDeptPos = sysDeptMapper.selectList(Wrappers.<SysDeptPo>query().lambda().in(SysDeptPo::getCompanyId, companyIds));
        List<SysPostPo> sysPostPos = sysPostMapper.selectList(Wrappers.<SysPostPo>query().lambda().in(SysPostPo::getCompanyId, companyIds));
        Map<Long, List<SysDeptPo>> sysDeptPoMap = sysDeptPos.stream().collect(Collectors.groupingBy(SysDeptPo::getCompanyId));
        Map<Long, List<SysUserDto>> userMap = getUserListByCompanyId(sysDeptPoMap);
        Map<Long, List<SysPostPo>> sysPostPoMap = sysPostPos.stream().collect(Collectors.groupingBy(SysPostPo::getCompanyId));
        for (SysCompanyDto companyDto : companyDtoList) {
            CompanyDeptPostDto companyDeptPostDto=new CompanyDeptPostDto();
            CompanyDeptPostIdDto companyDeptPostIdDto=new CompanyDeptPostIdDto();
            List<SysDeptPo> sysDeptList = sysDeptPoMap.get(companyDto.getId());
            List<SysPostPo> sysPostList = sysPostPoMap.get(companyDto.getId());
            List<SysUserDto> userList=userMap.get(companyDto.getId());
            if(userList!=null){
                for (SysUserDto sysUserDto : userList) {
                    List<SysPostDto> userPosts = sysUserDto.getPosts().stream().filter(sysPostDto -> sysPostDto.getCompanyId().equals(companyDto.getId())).toList();
                    sysUserDto.setPosts(userPosts);
                }
            }
            if(type==OperateEnum.DELETE.getCode()){
                BeanUtils.copyProperties(companyDto,companyDeptPostIdDto);
                if(sysDeptList!=null){
                    List<Long> deptIds = sysDeptList.stream().map(SysDeptPo::getId).collect(Collectors.toList());
                    companyDeptPostIdDto.setDeptIdList(deptIds);
                }
                if(sysPostList!=null){
                    List<Long> postIds = sysPostList.stream().map(SysPostPo::getId).collect(Collectors.toList());
                    companyDeptPostIdDto.setPostIdList(postIds);
                }
                companyDeptPostIdDto.setUserDtoList(userList);
                companyDeptPostIdDtoList.add(companyDeptPostIdDto);
            }
            if(type==OperateEnum.ADD.getCode()){
                BeanUtils.copyProperties(companyDto,companyDeptPostDto);
                companyDeptPostDto.setDeptDtoList(sysDeptList);
                companyDeptPostDto.setPostDtoList(sysPostList);
                companyDeptPostDto.setUserDtoList(userList);
                companyDeptPostDtoList.add(companyDeptPostDto);
            }
        }
    }
}