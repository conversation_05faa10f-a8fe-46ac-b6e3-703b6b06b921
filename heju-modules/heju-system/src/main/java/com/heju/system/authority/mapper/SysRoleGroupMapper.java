package com.heju.system.authority.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.query.SysRoleGroupQuery;

/**
 * 角色组管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysRoleGroupMapper extends BaseMapper<SysRoleGroupQuery, SysRoleGroupDto, SysRoleGroupPo> {
}