package com.heju.system.organize.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.TreeController;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.query.SysDeptQuery;
import com.heju.system.organize.service.ISysDeptService;
import com.heju.system.organize.service.ISysOrganizeService;
import com.heju.system.organize.service.ISysPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 部门管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
public class SysDeptController extends TreeController<SysDeptQuery, SysDeptDto, ISysDeptService> {

    @Autowired
    private ISysOrganizeService organizeService;

    @Autowired
    private ISysPostService postService;

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "部门";
    }

    /**
     * 新增部门 | 内部调用
     */
    @InnerAuth
    @PostMapping("/inner/add")
    public R<SysDeptDto> addInner(@RequestBody SysDeptDto dept) {
        return baseService.addInner(dept) > 0 ? R.ok(dept) : R.fail();
    }

    /**
     * 查询部门列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_DEPT_LIST)
    public AjaxResult list(SysDeptQuery dept) {
        return super.list(dept);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @GetMapping("/list/exclude")
    @RequiresPermissions(Auth.SYS_DEPT_LIST)
    public AjaxResult listExNodes(SysDeptQuery dept) {
        return super.listExNodes(dept);
    }

    /**
     * 查询部门详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_DEPT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 查询部门详细(内部)
     */
    @InnerAuth
    @GetMapping(value = "/getInfoInner/{id}")
    @RequiresPermissions(Auth.SYS_DEPT_SINGLE)
    public AjaxResult getInfoInner(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 查询部门关联的角色Id集
     */
    @GetMapping(value = "/auth/{id}")
    @RequiresPermissions(Auth.SYS_DEPT_AUTH)
    public AjaxResult getRoleAuth(@PathVariable Long id) {
        return success(organizeService.selectDeptRoleMerge(id));
    }

    /**
     * 部门导出
     */
    @Override
    @PostMapping("/export")
    @RequiresPermissions(Auth.SYS_DEPT_EXPORT)
    @Log(title = "部门管理", businessType = BusinessType.EXPORT)
    public void export(HttpServletResponse response, SysDeptQuery dept) {
        super.export(response, dept);
    }

    /**
     * 部门新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_DEPT_ADD)
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysDeptDto dept) {
        return super.add(dept);
    }

    /**
     * 部门修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_DEPT_EDIT)
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysDeptDto dept) {
        return super.edit(dept);
    }

    /**
     * 查询部门关联的角色Id集
     */
    @PutMapping(value = "/auth")
    @RequiresPermissions(Auth.SYS_DEPT_AUTH)
    public AjaxResult editRoleAuth(@RequestBody SysDeptDto dept) {
        organizeService.editDeptRoleMerge(dept.getId(), dept.getRoleIds());
        return success();
    }

    /**
     * 部门修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_DEPT_EDIT, Auth.SYS_DEPT_ES}, logical = Logical.OR)
    @Log(title = "部门管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysDeptDto dept) {
        return super.editStatus(dept);
    }

    /**
     * 部门批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_DEPT_DEL)
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        RHandleEmpty(idList);
        RHandle(BaseConstants.Operate.DELETE, idList);
        RHandleEmpty(idList);
        RHandleTreeChild(idList);
        return toAjax(baseService.deleteByIds(idList));
    }

    /**
     * 获取部门选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysDeptDto dept) {
        if (baseService.checkDeptCodeUnique(dept.getId(), dept.getCode()))
            warn(StrUtil.format("{}{}{}失败，部门编码已存在", operate.getInfo(), getNodeName(), dept.getName()));
        else if (baseService.checkNameUnique(dept.getId(), dept.getParentId(), dept.getName()))
            warn(StrUtil.format("{}{}{}失败，部门名称已存在", operate.getInfo(), getNodeName(), dept.getName()));
    }

    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        if (!postService.selectListByDeptIds(idList).isEmpty())
            warn(StrUtil.format("{}{}失败，部门存在绑定岗位", operate.getInfo(), getNodeName()));
    }

}
