package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.system.entity.domain.po.SysEntitySaicChangeRecordPo;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.query.SysEntitySaicChangeRecordQuery;
import com.heju.system.entity.domain.model.SysEntitySaicChangeRecordConverter;
import com.heju.system.entity.mapper.SysEntitySaicChangeRecordMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntitySaicChangeRecordManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体工商变更记录管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntitySaicChangeRecordManager extends BaseManagerImpl<SysEntitySaicChangeRecordQuery, SysEntitySaicChangeRecordDto, SysEntitySaicChangeRecordPo, SysEntitySaicChangeRecordMapper, SysEntitySaicChangeRecordConverter> implements ISysEntitySaicChangeRecordManager {
    @Override
    public List<SysEntitySaicChangeRecordPo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntitySaicChangeRecordPo> lambdaRecord = new LambdaQueryWrapper<>();
        lambdaRecord.eq(SysEntitySaicChangeRecordPo::getEntityId,id);
        return baseMapper.selectList(lambdaRecord);
    }
}