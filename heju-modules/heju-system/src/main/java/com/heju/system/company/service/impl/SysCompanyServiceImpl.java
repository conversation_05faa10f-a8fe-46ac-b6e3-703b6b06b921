package com.heju.system.company.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.pagehelper.PageInfo;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.web.page.TableDataInfo;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.domain.query.SysCompanyQuery;
import com.heju.system.company.mapper.SysCompanyMapper;
import com.heju.system.company.service.ISysCompanyService;
import com.heju.system.company.manager.ISysCompanyManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.third.domain.dto.*;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.system.third.service.ISysThirdAuthService;
import com.heju.system.utils.OperateEnum;
import com.heju.system.utils.OrganizeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.heju.common.core.utils.page.PageUtil.startPage;
import static com.heju.common.core.web.result.AjaxResult.success;

/**
 * 子公司管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysCompanyServiceImpl extends BaseServiceImpl<SysCompanyQuery, SysCompanyDto, ISysCompanyManager> implements ISysCompanyService {

    @Autowired
    public RedisTemplate redisTemplate;

    @Autowired
    private ISysThirdAuthManager sysThirdAuthManager;


    @Autowired
    private ISysThirdAuthService sysThirdAuthService;

    @Autowired
    private SysCompanyMapper companyMapper;

    /**
     * 缓存主键命名定义
     */
    @Override
    protected String getCacheKey() {
        return CacheConstants.CacheType.SYS_COMPANY_KEY.getCode();
    }

    /**
     * 查询子公司对象列表 | 数据权限
     *
     * @param company 子公司对象
     * @return 子公司对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysCompanyMapper"})
    public List<SysCompanyDto> selectListScope(SysCompanyQuery company) {
        return baseManager.selectList(company);
    }

    @Override
    @DSTransactional
    public int insert(SysCompanyDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        redisTemplate.opsForHash().put(CacheConstants.CacheType.SYS_COMPANY_TENANT_KEY.getCode(), dto.getId().toString(), SecurityUtils.getSourceName());
        return row;
    }

    @Override
    @DSTransactional
    public int update(SysCompanyDto dto) {
        SysCompanyDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        List<Long> thirdList = Arrays.stream(dto.getThirdIds()).toList();
        if (originDto.getThirds() != null && !originDto.getThirds().isEmpty()) {
            List<SysThirdDto> sysThirdDtoStream = originDto.getThirds().stream().filter(sysThirdDto -> !thirdList.contains(sysThirdDto.getId())).toList();
            if (!sysThirdDtoStream.isEmpty()) {
                //判断是否有第三方认证关联
                List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = sysThirdAuthManager.selectThirdAuthId(dto.getId(), sysThirdDtoStream);
                if (!companyThirdAuthMergeList.isEmpty()) {
                    //删除第三方认证关联关系
                    sysThirdAuthManager.deleteCompanyThirdAuthMerge(companyThirdAuthMergeList);
                    Map<String, String> companyThirdAuthRedis = sysThirdAuthService.getCompanyThirdAuthRedis();
                    for (SysCompanyThirdAuthMerge companyThirdAuthMerge : companyThirdAuthMergeList) {
                        Long thirdAuthId = companyThirdAuthMerge.getThirdAuthId();
                        String companyIds = companyThirdAuthRedis.get(String.valueOf(thirdAuthId));
                        if (!StringUtils.isEmpty(companyIds)) {
                            companyIds = StringUtils.join(Arrays.stream(companyIds.split(",")).filter(companyId ->
                                    companyId.equals(String.valueOf(dto.getId()))).toList(), ",");
                            sysThirdAuthService.setCompanyThirdAuthRedis(thirdAuthId, companyIds);
                        }
                    }
                    //组装该公司组织架构数据,发送数据给谷云
                    baseManager.getDeptPostById(dto, companyThirdAuthMergeList);
                }
            }
        }
        List<Long> companyIds = new ArrayList<>();
        companyIds.add(dto.getId());
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = sysThirdAuthManager.getByCompanyIds(companyIds);
        if (!companyThirdAuthMergeList.isEmpty()) {
            baseManager.buildOrganizeOne(OperateEnum.EDIT.getCode(), OrganizeEnum.COMPANY.getCode(), companyThirdAuthMergeList, JSON.toJSONString(dto));
        }
        return row;
    }

    @Override
    public boolean checkCompanyCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkCompanyCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }

    @Override
    public AjaxResult listAuth(Long thirdId) {
        return null;
    }


    public Map<String, SysCompanyDto> getCompanyRedisList() {
        return redisService.redisTemplate.opsForHash().entries(getCacheKey());
    }

    @Override
    public Map<String, Object> getCompanyTenantRedisList() {
        return redisService.redisTemplate.opsForHash().entries(CacheConstants.CacheType.SYS_COMPANY_TENANT_KEY.getCode());
    }

    @Override
    public void buildOrganizeOne(int operateType, int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList, String organizeInfo) {
        baseManager.buildOrganizeOne(operateType, organizeType, companyThirdAuthMergeList, organizeInfo);
    }


    @Override
    public void buildDept(List<SysDeptDto> originList) {
        baseManager.buildDept(originList);
    }

    @Override
    public void buildPost(List<SysPostDto> originList) {
        baseManager.buildPost(originList);
    }

    @Override
    public void buildUser(List<SysUserDto> originList) {
        baseManager.buildUser(originList);
    }

    @Override
    public SysCompanyDto[] drop(String tenantId) {
        SysCompanyDto[] sysCompanyDtos = companyMapper.selectCompany(tenantId);
        return sysCompanyDtos;
    }

    /**
     * 查询所有公司
     * @return 公司信息List
     */
    public List<SysCompanyDto> findAllCompanyIdAndName() {
        return companyMapper.findAllCompanyIdAndName();
    }
}