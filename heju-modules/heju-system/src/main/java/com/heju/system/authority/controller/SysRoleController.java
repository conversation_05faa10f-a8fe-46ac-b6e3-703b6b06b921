package com.heju.system.authority.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.TreeUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
//import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleEntityDto;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.api.authority.domain.query.SysRoleQuery;
//import com.heju.system.api.organize.domain.dto.SysUserDto;
//import com.heju.system.authority.domain.merge.SysRoleEntityFieldMerge;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.authority.domain.vo.SysAuthTree;
import com.heju.system.authority.service.ISysAuthService;
import com.heju.system.authority.service.ISysRoleService;
//import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.service.ISysEntityService;
import com.heju.system.organize.service.ISysOrganizeService;
//import org.aspectj.weaver.loadtime.Aj;
//import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;

/**
 * 角色管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/role")
public class SysRoleController extends BaseController<SysRoleQuery, SysRoleDto, ISysRoleService> {

    @Resource
    private ISysAuthService authService;

    @Resource
    private ISysOrganizeService organizeService;
    @Resource
    private ISysEntityService sysEntityService;

    @Resource
    private ISysRoleService roleService;

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "角色";
    }

    /**
     * 查询角色列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ROLE_LIST)
    public AjaxResult list(SysRoleQuery role) {
        return super.list(role);
    }

    /**
     * 查询角色详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_ROLE_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }


    /**
     * 查询角色详细(内部)
     */
    @InnerAuth
    @GetMapping(value = "/getInfoInner/{id}")
    @RequiresPermissions(Auth.SYS_ROLE_SINGLE)
    public AjaxResult getInfoInner(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 获取角色功能权限 | 叶子节点
     */
    @GetMapping("/auth/{id}")
    @RequiresPermissions(Auth.SYS_ROLE_AUTH)
    public AjaxResult getRoleAuth(@PathVariable Long id) {
        List<SysAuthTree> leafNodes = TreeUtil.getLeafNodes(TreeUtil.buildTree(authService.selectRoleAuth(id)));
        return success(leafNodes.stream().map(SysAuthTree::getId).toArray(Long[]::new));
    }

    /**
     * 获取角色组织权限
     */
    @GetMapping("/organize/{id}")
    @RequiresPermissions(Auth.SYS_ROLE_AUTH)
    public AjaxResult getRoleOrganize(@PathVariable Long id) {
        return success(organizeService.selectRoleOrganizeMerge(id));
    }

    /**
     * 角色导出
     */
    @Override
    @PostMapping("/export")
    @RequiresPermissions(Auth.SYS_ROLE_EXPORT)
    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    public void export(HttpServletResponse response, SysRoleQuery role) {
        super.export(response, role);
    }

    /**
     * 角色新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_ROLE_ADD)
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysRoleDto role) {
        return super.add(role);
    }

    /**
     * 角色新增 | 内部调用
     */
    @InnerAuth
    @PostMapping("/addInner")
    public AjaxResult addInner(@RequestBody SysRoleDto role) {
        return super.add(role);
    }

    /**
     * 角色修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_ROLE_EDIT)
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysRoleDto role) {
        role.initOperate(BaseConstants.Operate.EDIT);
        AEHandle(role.getOperate(), role);
        return toAjax(roleService.update(role));
    }

    /**
     * 角色修改功能权限
     */
    @PutMapping("/auth")
    @RequiresPermissions(Auth.SYS_ROLE_AUTH)
    @Log(title = "角色管理", businessType = BusinessType.AUTH)
    public AjaxResult editAuth(@RequestBody SysRoleDto role) {
        authService.editRoleAuth(role.getId(), role.getAuthIds());
        return success();
    }

    /**
     * 角色修改组织权限
     */
    @PutMapping("/organize")
    @RequiresPermissions(Auth.SYS_ROLE_AUTH)
    @Log(title = "角色管理", businessType = BusinessType.AUTH)
    public AjaxResult editOrganize(@RequestBody SysRoleDto role) {
        return success(baseService.updateDataScope(role));
    }

    /**
     * 角色修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_ROLE_EDIT, Auth.SYS_ROLE_ES}, logical = Logical.OR)
    @Log(title = "角色管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysRoleDto role) {
        return super.editStatus(role);
    }

    /**
     * 角色批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_ROLE_DEL)
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取角色选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysRoleDto role) {
        if (baseService.checkRoleCodeUnique(role.getId(), role.getCode()))
            warn(StrUtil.format("{}{}{}失败，角色编码已存在", operate.getInfo(), getNodeName(), role.getName()));
        else if (baseService.checkNameUnique(role.getId(), role.getName()))
            warn(StrUtil.format("{}{}{}失败，角色名称已存在", operate.getInfo(), getNodeName(), role.getName()));
        else if (baseService.checkRoleKeyUnique(role.getId(), role.getRoleKey()))
            warn(StrUtil.format("{}{}{}失败，角色权限已存在", operate.getInfo(), getNodeName(), role.getName()));
        // 修改禁止操作权限范围
        if (operate.isEdit())
            role.setDataScope(null);
    }

    /**
     * 角色添加实体字段
     */
    @PostMapping("/addEntityField")
    public AjaxResult roleBatchesAddEntity(@RequestBody SysRoleEntityDto sysRoleEntityDto) {
        baseService.roleBatchesAddEntity(sysRoleEntityDto);
        return success();
    }

    /**
     * 查询角色拥有的实体字段列表
     */
    @GetMapping("/roleHasEntity")
    public AjaxResult selectRoleHasEntity(Long id){
        List<SysEntityFieldPo> sysEntityFieldPoList = sysEntityService.selectRoleHasEntity(id);
        return  AjaxResult.success(sysEntityFieldPoList);
    }



    /**
     * 根据登录用户id查询登录人所有的角色的实体字段
     */
    @GetMapping("/selectUserRoleEntity")
    public AjaxResult selectUserRoleEntity(){
        List<SysEntityFieldPo> list =sysEntityService.selectUserRoleAllEntityFields();
        return AjaxResult.success(list);
    }

    /**
     * 根据roleKey获取角色Id
     */
    @InnerAuth
    @GetMapping("/getIdsByRoleKey")
    public R<SysUserDto> getRoleIdsByRoleKey(@RequestParam(value = "roleKey") String roleKey){
        return R.ok(roleService.getRoleIdsByRoleKey(roleKey));
    }


    /**
     * 获取角色组列表
     * @return
     */

    @GetMapping("/getRoleGroupList")
    public AjaxResult list(){
        List<SysRolePo> roleGroupList = roleService.getRoleGroupList();
        return AjaxResult.success(roleGroupList);
    }

    /**
     * 获取角色组织树
     */
    @GetMapping(value = "/getRoleGroupScope")
    public AjaxResult getRoleGroupScope() {
        return success(TreeUtil.buildTree(roleService.selectRoleGroupScope()));
    }

}
