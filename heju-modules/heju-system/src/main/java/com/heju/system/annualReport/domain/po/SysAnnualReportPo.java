package com.heju.system.annualReport.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;

import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 工商年报 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_annual_report", excludeProperty = { NAME })
public class SysAnnualReportPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体名称 */
    @Excel(name = "实体名称")
    protected Long entityId;

    /** 工商年报 0-工商年报 */
    @Excel(name = "工商年报 0-工商年报")
    protected Integer annualReportType;

    /** 报表年份 */
    @Excel(name = "报表年份")
    protected String reportYear;

    /** 能否申报 0-是，1-否 */
    @Excel(name = "能否申报 0-是，1-否")
    protected Integer isReport;

    /** 上传凭证 */
    @Excel(name = "上传凭证")
    protected String voucher;

}