package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.po.SysEntitySaicEmployeePo;
import com.heju.system.entity.domain.query.SysEntitySaicEmployeeQuery;

/**
 * 实体工商董事会成员管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntitySaicEmployeeMapper extends BaseMapper<SysEntitySaicEmployeeQuery, SysEntitySaicEmployeeDto, SysEntitySaicEmployeePo> {
}