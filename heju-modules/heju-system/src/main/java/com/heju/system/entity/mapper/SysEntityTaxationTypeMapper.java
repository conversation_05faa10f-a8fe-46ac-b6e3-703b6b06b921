package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import com.heju.system.entity.domain.query.SysEntityTaxationTypeQuery;

/**
 * 实体税务税费种认定管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityTaxationTypeMapper extends BaseMapper<SysEntityTaxationTypeQuery, SysEntityTaxationTypeDto, SysEntityTaxationTypePo> {
}