package com.heju.system.forms.field.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;

import java.util.List;

/**
 * 字段管理管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysFieldService extends IBaseService<SysFieldQuery, SysFieldDto> {

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    int insert(SysFieldDto dto);

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    int update(SysFieldDto dto);

    /**
     * 校验参数编码是否唯一
     *
     * @param Id      参数Id
     * @param apiName API名称
     * @param sheetId 表单id
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkConfigCodeUnique(Long Id, String apiName,Long sheetId);

    List<SysFieldPo> selectQuote(List<Long> ids);

    List<SysFieldPo> selectRelation(List<Long> ids);

    List<SysFieldDto> relationList(SysFieldQuery field);

    AjaxResult option(SysFieldQuery field);

    AjaxResult searchQuote(SysFieldQuery query);

    List<SysFieldDto> selectBySheetIds(List<Long> sheetIds);

    List<SysFieldDto> selectByApiNames(List<String> apiName,Long sheetId);

    List<SysFieldDto> selectQuoteByIds(List<Long> ids);

    List<SysFieldDto> selectReferencingByIds(List<Long> ids);

    List<SysFieldDto> selectReferencedByIds(List<Long> ids);

}