package com.heju.system.phone.mapper;

import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.po.SysPhoneNumberInfoPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 手机号管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysPhoneNumberInfoMapper extends BaseMapper<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto, SysPhoneNumberInfoPo> {

//    @Select("<script>" +
//            "SELECT 'sim_one' AS sim_type, p.sim_one AS phone_number " +
//            "FROM sys_phone_number_info p " +
//            "WHERE p.del_flag = 0 " +
//            "<if test='query.userId != null'>" +
//            "AND (" +
//            "p.custody = #{query.userId} " +
//            "OR EXISTS (" +
//            "SELECT 1 FROM sys_pho_permission_record r " +
//            "WHERE r.permissioner_id = #{query.userId} " +
//            "AND r.phone_number = p.sim_one " +
//            "AND NOW() BETWEEN r.start_time AND r.end_time " +
//            "AND (r.remaining_times > 0 OR r.remaining_times = -1 OR r.remaining_times IS NULL)" +
//            ")" +
//            ")" +
//            "</if>" +
//            "UNION ALL " +
//
//            "SELECT 'sim_two' AS sim_type, p.sim_two AS phone_number " +
//            "FROM sys_phone_number_info p " +
//            "WHERE p.del_flag = 0 " +
//            "<if test='query.userId != null'>" +
//            "AND (" +
//            "p.custody = #{query.userId} " +
//            "OR EXISTS (" +
//            "SELECT 1 FROM sys_pho_permission_record r " +
//            "WHERE r.permissioner_id = #{query.userId} " +
//            "AND r.phone_number = p.sim_two " +
//            "AND NOW() BETWEEN r.start_time AND r.end_time " +
//            "AND (r.remaining_times > 0 OR r.remaining_times = -1 OR r.remaining_times IS NULL)" +
//            ")" +
//            ")" +
//            "</if>" +
//            "</script>")
    @Select("<script>" +
            "SELECT 'sim_one' AS sim_type, p.sim_one AS phone_number " +
            "FROM sys_phone_number_info p " +
            "WHERE p.del_flag = 0 " +
            "<if test='query.userId != null'>" +
            "AND (" +
            "p.custody = #{query.userId} " +
            "OR EXISTS (" +
            "SELECT 1 FROM sys_pho_permission_record r " +
            "WHERE r.permissioner_id = #{query.userId} " +
            "AND r.phone_number = p.sim_one " +
            "AND (" +
            "   (r.start_time IS NULL AND r.end_time IS NULL AND (r.remaining_times > 0 OR r.remaining_times = -1)) " +
            "   OR " +
            "   (r.start_time IS NOT NULL AND r.end_time IS NOT NULL AND NOW() BETWEEN r.start_time AND r.end_time AND (r.remaining_times > 0 OR r.remaining_times = -1)) " +
            ")" +
            ")" +
            ")" +
            "</if>" +
            "UNION ALL " +

            "SELECT 'sim_two' AS sim_type, p.sim_two AS phone_number " +
            "FROM sys_phone_number_info p " +
            "WHERE p.del_flag = 0 " +
            "<if test='query.userId != null'>" +
            "AND (" +
            "p.custody = #{query.userId} " +
            "OR EXISTS (" +
            "SELECT 1 FROM sys_pho_permission_record r " +
            "WHERE r.permissioner_id = #{query.userId} " +
            "AND r.phone_number = p.sim_two " +
            "AND (" +
            "   (r.start_time IS NULL AND r.end_time IS NULL AND (r.remaining_times > 0 OR r.remaining_times = -1)) " +
            "   OR " +
            "   (r.start_time IS NOT NULL AND r.end_time IS NOT NULL AND NOW() BETWEEN r.start_time AND r.end_time AND (r.remaining_times > 0 OR r.remaining_times = -1)) " +
            ")" +
            ")" +
            ")" +
            "</if>" +
            "</script>")
    List<Map<String, Object>> selectPhoneList(@Param("query") SysPhoneNumberInfoQuery query);
}
