package com.heju.system.entity.controller;

import com.github.pagehelper.PageInfo;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.CompanyAbnormalInformationPo;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;
import com.heju.system.entity.service.ISysEntityExceptionInfoService;
import com.heju.system.utils.PageResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;

/**
 * 企业经营异常信息管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/SysEntityExceptionInfo")
public class SysEntityExceptionInfoController extends BaseController<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto, ISysEntityExceptionInfoService> {

    @Resource
    ISysEntityExceptionInfoService sysEntityExceptionInfoService;



    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "企业经营异常信息" ;
    }

    /**
     * 查询企业经营异常信息列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ENTITY_EXCEPTION_INFO_LIST)
    public AjaxResult list(SysEntityExceptionInfoQuery entityExceptionInfo) {
        return super.list(entityExceptionInfo);
    }


    /**
     * 查询企业经营异常信息列表
     */
    @GetMapping("AList")
    public AjaxResult list1(SysEntityExceptionInfoDto sysEntityExceptionInfoDto){
        PageResult list= sysEntityExceptionInfoService.AList(sysEntityExceptionInfoDto);
        return AjaxResult.success(list);

    }



    /**
     * 企业经营异常信息批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_ENTITY_EXCEPTION_INFO_DEL)
    @Log(title = "企业经营异常信息管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取企业经营异常信息选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


    /**
     * 刷新企业异常信息
     */
    @RequiresPermissions(Auth.SYS_ENTITY_EXCEPTION_INFO_REFRESH)
    @GetMapping("/refresh")
    public AjaxResult refresh(  @RequestParam(value = "entityIds",required = false) Long[] entityIds) throws ParseException {
        if (entityIds==null || entityIds.length==0){
            return AjaxResult.success("参数不能为空");
        }
        List<SysEntityExceptionInfoDto> list =  sysEntityExceptionInfoService.refresh(entityIds);
        return AjaxResult.success(list);
    }



    /**
     * 查询企业经营的历史异常信息
     * @param companyName
     * @return
     */
    @GetMapping("/getHistoryRecord")
    @RequiresPermissions(Auth.SYS_ENTITY_EXCEPTION_INFO_HISTORY)
    public AjaxResult getHistoryRecord(String companyName ,@RequestParam(value = "page", defaultValue = "1")Integer page, @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize){
       //最新 PageInfo<CompanyAbnormalInformationPo> pageInfo = sysEntityExceptionInfoService.getHistoryRecordList(companyName, pageNum, pageSize);
//        List<CompanyAbnormalInformationPo> list = sysEntityExceptionInfoService.getHistoryRecord(companyName);
        PageResult list= sysEntityExceptionInfoService.getHistoryRecord(companyName,page,pageSize);
        return AjaxResult.success(list);


    }




}
