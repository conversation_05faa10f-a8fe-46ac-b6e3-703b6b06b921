package com.heju.system.entity.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.query.SysEntitySaicBranchQuery;
import com.heju.system.entity.manager.ISysEntitySaicBranchManager;
import com.heju.system.entity.service.ISysEntitySaicBranchService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体工商分支机构管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntitySaicBranchServiceImpl extends BaseServiceImpl<SysEntitySaicBranchQuery, SysEntitySaicBranchDto, ISysEntitySaicBranchManager> implements ISysEntitySaicBranchService {

    /**
     * 查询实体工商分支机构对象列表 | 数据权限
     *
     * @param entitySaicBranch 实体工商分支机构对象
     * @return 实体工商分支机构对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntitySaicBranchMapper"})
    public List<SysEntitySaicBranchDto> selectListScope(SysEntitySaicBranchQuery entitySaicBranch) {
        return baseManager.selectList(entitySaicBranch);
    }

}