package com.heju.system.entity.domain.po;

import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityPo extends BaseEntity {

    private String creditNo;

    private String code;

    private String legalPersonName;

    private String registerCapital;

    private String registerCapitalCurrency;

    private String regType;

    private String businessScope;

    private String address;

    private String businessStatus;

    private String businessTerm;

    private String belongOrg;

    private String registerNo;

    private LocalDate startDate;

    private Integer partnerTotal;

    private Integer branchTotal;

    private Integer employeeTotal;

    private List<SysEntitySaicBranchDto> branchPoList;

    private List<SysEntitySaicChangeRecordDto> changeRecordPoList;

    private List<SysEntitySaicEmployeeDto> employeePoList;

    private List<SysEntitySaicPartnerDto> partnerPoList;
}
