package com.heju.system.authority.service.impl;

import com.heju.common.cache.constant.CacheConstants;
import com.heju.system.authority.domain.TelephoneCode;
import com.heju.system.authority.service.SysPhoneCodeService;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class SysPhoneCodeServiceImpl implements SysPhoneCodeService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 获取手机号与验证码
     * @return 手机号验证码
     */
    @Override
    public List<TelephoneCode> getPhoneAndCode() {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        Map<String, String> telephoneCodes = hashOperations.entries(CacheConstants.CacheType.SYS_TELEPHONE_CODE_KEY.getCode());
        if (telephoneCodes.size()==0){
            return new ArrayList<>();
        }
        List<TelephoneCode> TelephoneCodePairPoList = new ArrayList<>();
        for (Map.Entry<String, String> entry : telephoneCodes.entrySet()) {
            TelephoneCode pair = new TelephoneCode();
            pair.setPhoneNumber(entry.getKey());
            StringBuilder code = new StringBuilder(String.valueOf(entry.getValue()));
            for (int i = code.length(); i < 6; i++) {
                code.insert(0, "0");
            }
            pair.setVerificationCode(code.toString());
            TelephoneCodePairPoList.add(pair);
        }
        return TelephoneCodePairPoList;
    }
}
