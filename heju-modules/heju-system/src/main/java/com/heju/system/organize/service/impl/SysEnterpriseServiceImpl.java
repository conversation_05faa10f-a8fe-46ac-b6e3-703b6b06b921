package com.heju.system.organize.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import com.heju.system.api.organize.domain.query.SysEnterpriseQuery;
import com.heju.system.organize.manager.ISysEnterpriseManager;
import com.heju.system.organize.service.ISysEnterpriseService;
import org.springframework.stereotype.Service;

/**
 * 企业管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEnterpriseServiceImpl extends BaseServiceImpl<SysEnterpriseQuery, SysEnterpriseDto, ISysEnterpriseManager> implements ISysEnterpriseService {

    /**
     * 根据名称查询状态正常企业对象
     *
     * @param name 名称
     * @return 企业对象
     */
    @Override
    public SysEnterpriseDto selectByName(String name) {
        return baseManager.selectByName(name);
    }

    @Override
    public Long getUserIdByTenant(String enterpriseName, String telephone, Long userId) {
        return baseManager.getUserIdByTenant(enterpriseName,telephone,userId);
    }

    @Override
    public Long getUserIdByTenantUnionId(String enterpriseName, String unionId) {
        return baseManager.getUserIdByTenantUnionId(enterpriseName,unionId);
    }

    @Override
    public String getIdByTelephone(String telephone) {
        return baseManager.getIdByTelephone(telephone);
    }

    @Override
    public int insertByTelephone(String id,String telephone) {
        return baseManager.insertByTelephone(id,telephone);
    }

    @Override
    public int insertTenantUser(Long tenantUserId, Long tenantId, String userId) {
        return baseManager.insertTenantUser(tenantUserId, tenantId, userId);
    }
}
