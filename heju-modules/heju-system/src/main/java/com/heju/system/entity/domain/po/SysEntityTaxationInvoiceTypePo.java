package com.heju.system.entity.domain.po;

import java.time.LocalDate;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.baomidou.mybatisplus.annotation.*;
import com.heju.common.core.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体税务票种认定 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_taxation_invoice_type", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY,CREATE_TIME, DEL_FLAG, UPDATE_TIME, REMARK, NAME })
public class SysEntityTaxationInvoiceTypePo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 发票种类 */
    @Excel(name = "发票种类")
    protected String invoiceType;

    /** 单份发票最高开票限额 */
    @Excel(name = "单份发票最高开票限额")
    protected String invoiceLimit;

    /** 每月最高购票数量 */
    @Excel(name = "每月最高购票数量")
    protected Integer monthInvoiceNumber;

    /** 每次最高购票数量 */
    @Excel(name = "每次最高购票数量")
    protected Integer everytimeInvoiceNumber;

    /** 持票最高数量 */
    @Excel(name = "持票最高数量")
    protected Integer holdInvoiceNumber;

    /** 离线开票时限 */
    @Excel(name = "离线开票时限")
    protected String offlineInvoiceTimeLimit;

    /** 离线开票累计限额 */
    @Excel(name = "离线开票累计限额")
    protected String offlineInvoiceAccumulatedLimit;

    /** 有效期起 */
    @Excel(name = "有效期起")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected LocalDate starttime;

    /** 有效期止 */
    @Excel(name = "有效期止")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected LocalDate endtime;

    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

}