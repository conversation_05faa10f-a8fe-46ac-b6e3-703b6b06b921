package com.heju.system.declaration.domain.query;

import com.heju.common.core.constant.system.TaxFilingsConstants;
import com.heju.system.declaration.domain.dto.TaxFilingsView;
import com.heju.system.declaration.domain.po.SysTaxFilingsPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.HashMap;
import java.util.Map;

/**
 * 税务申报 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTaxFilingsQuery extends SysTaxFilingsPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体名称 */
    private String entityName;

    /** 申报视图Map集合（Key：申报税种 value：税务子类型各时间类型信息）*/
    private Map<String, TaxFilingsView> taxTypes;


    /** 初始化代码块 */
    public SysTaxFilingsQuery(String year){
        this.taxTypes = new HashMap<>();
        initTaxTypes(year);
    }

    /** 初始化申报视图Map集合方法 */
    private void initTaxTypes(String year) {
        for (int i = 0; i <= Integer.parseInt(TaxFilingsConstants.TaxType.NUMBER.getCode()) - 1; i++) {
            TaxFilingsView taxFilingsView = new TaxFilingsView();
            taxFilingsView.initYearTax(year);
            taxTypes.put(i + "",taxFilingsView);
        }
    }

    /** 更新申报视图Map集合方法 */
    public void updateTaxTypes(String taxType,TaxFilingsView view){
        taxTypes.put(taxType,view);
    }
}