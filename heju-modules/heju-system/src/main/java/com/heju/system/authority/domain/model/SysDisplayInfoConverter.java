package com.heju.system.authority.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.system.authority.domain.po.SysDisplayInfoPo;
import com.heju.system.authority.domain.query.SysDisplayInfoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 显隐列 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysDisplayInfoConverter extends BaseConverter<SysDisplayInfoQuery, SysDisplayInfoDto, SysDisplayInfoPo> {
}
