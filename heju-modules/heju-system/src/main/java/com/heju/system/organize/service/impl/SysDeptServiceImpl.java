package com.heju.system.organize.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.datascope.annotation.DataScope;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.service.impl.TreeServiceImpl;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.query.SysDeptQuery;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.service.ISysCompanyService;
import com.heju.system.organize.manager.ISysDeptManager;
import com.heju.system.organize.service.ISysDeptService;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.system.utils.OperateEnum;
import com.heju.system.utils.OrganizeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 部门管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
@Isolate
public class SysDeptServiceImpl extends TreeServiceImpl<SysDeptQuery, SysDeptDto, ISysDeptManager> implements ISysDeptService {

    @Resource
    private ISysCompanyService companyService;

    @Resource
    private ISysThirdAuthManager thirdAuthManager;

    /**
     * 新增部门 | 内部调用
     *
     * @param dept 部门对象
     * @return 结果
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int addInner(SysDeptDto dept) {
        return super.insert(dept);
    }

    /**
     * 查询部门对象列表 | 数据权限 | 附加数据
     *
     * @param dept 部门对象
     * @return 部门对象集合
     */
    @Override
    @DataScope(deptAlias = "id", mapperScope = {"SysDeptMapper"})
    public List<SysDeptDto> selectListScope(SysDeptQuery dept) {
        return baseManager.selectList(dept);
    }

    /**
     * 校验部门编码是否唯一
     *
     * @param Id   部门Id
     * @param code 部门编码
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkDeptCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkDeptCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */

    @Override
    @DSTransactional
    public int insert(SysDeptDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        List<Long> companyIds = new ArrayList<>();
        companyIds.add(dto.getCompanyId());
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthManager.getByCompanyIds(companyIds);
        companyService.buildOrganizeOne(OperateEnum.ADD.getCode(), OrganizeEnum.DEPT.getCode(), companyThirdAuthMergeList, JSON.toJSONString(dto));
        return row;
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysDeptDto dto) {
        SysDeptDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        if (dto.getCompanyId().equals(originDto.getCompanyId())) {
            List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthManager.getByCompanyId(dto.getCompanyId());
            companyService.buildOrganizeOne(OperateEnum.EDIT.getCode(), OrganizeEnum.DEPT.getCode(), companyThirdAuthMergeList, JSON.toJSONString(dto));
        }
        return row;
    }

    /**
     * 根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    @Override
    @DSTransactional
    public int deleteByIds(Collection<? extends Serializable> idList) {
        List<SysDeptDto> originList = selectListByIds(idList);
        startBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, originList, null);
        int rows = baseManager.deleteByIds(idList);
        endBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, rows, originList, null);
        companyService.buildDept(originList);
        return rows;
    }


}
