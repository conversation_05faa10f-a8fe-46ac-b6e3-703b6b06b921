package com.heju.system.notice.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.po.SysMessagePo;
import com.heju.system.notice.domain.query.SysMessageQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 消息通知 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysMessageConverter extends BaseConverter<SysMessageQuery, SysMessageDto, SysMessagePo> {
}
