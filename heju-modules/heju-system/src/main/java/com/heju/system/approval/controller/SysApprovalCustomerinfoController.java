package com.heju.system.approval.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.web.entity.controller.BaseController;

import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import com.heju.system.approval.service.ISysApprovalCustomerinfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 客户信息审核管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerinfo")
public class SysApprovalCustomerinfoController extends BaseController<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, ISysApprovalCustomerinfoService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "客户信息审核" ;
    }

    /**
     * 查询客户信息审核列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_LIST)
    public AjaxResult list(SysApprovalCustomerinfoQuery approvalCustomerinfo) {
        startPage();
        List<SysApprovalCustomerinfoDto> list = baseService.selectListScope(approvalCustomerinfo);
        return getDataTable(list);
    }

    /**
     * 查询客户信息审核详细
     */
    @Override
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {return super.getInfo(id);}

    /**
     * 客户信息审核新增
     */
    @Override
    @PostMapping
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_ADD)
    @Log(title = "客户信息审核管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysApprovalCustomerinfoDto approvalCustomerinfo) {
        approvalCustomerinfo.initOperate(BaseConstants.Operate.ADD);
        AEHandle(approvalCustomerinfo.getOperate(), approvalCustomerinfo);
        return toAjax(baseService.insert(approvalCustomerinfo));
    }

    /**
     * 客户信息审核修改
     */
    @Override
    @PutMapping
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_EDIT)
    @Log(title = "客户信息审核管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysApprovalCustomerinfoDto approvalCustomerinfo) {
        approvalCustomerinfo.initOperate(BaseConstants.Operate.EDIT);
        AEHandle(approvalCustomerinfo.getOperate(), approvalCustomerinfo);
        return toAjax(baseService.update(approvalCustomerinfo));
    }

    /**
     * 客户信息审核批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_DEL)
    @Log(title = "客户信息审核管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        RHandleEmpty(idList);
        RHandle(BaseConstants.Operate.DELETE, idList);
        return toAjax(baseService.deleteByIds(idList));
    }

    /**
     * 获取客户信息审核选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 通过客户信息审核
     * @param approvalCustomerinfo 数据传输对象
     * @return 将审批状态 改为 1, 修改后 的值 写入 字段中文名
     */
    @PostMapping("/pass")
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_PASS)
    @Log(title = "客户信息审核管理", businessType = BusinessType.PASS)
    public AjaxResult pass(@RequestBody SysApprovalCustomerinfoDto approvalCustomerinfo) {
        return baseService.pass(approvalCustomerinfo) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 驳回客户信息审核
     * @param approvalCustomerinfo 数据传输对象
     * @return 将审批状态 改为 2 , 拒绝原因写入 remark
     */
    @PostMapping("/reject")
//    @RequiresPermissions(Auth.SYS_APPROVAL_CUSTOMERINFO_REJECT)
    @Log(title = "客户信息审核管理", businessType = BusinessType.NO_PASS)
    public AjaxResult reject(@RequestBody SysApprovalCustomerinfoDto approvalCustomerinfo) {
        return baseService.reject(approvalCustomerinfo) > 0 ? AjaxResult.success() : AjaxResult.error();
    }
}
