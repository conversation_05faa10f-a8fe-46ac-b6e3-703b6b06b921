package com.heju.system.entity.domain.query;

import com.heju.system.entity.domain.po.SysEntitySaicEmployeePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体工商董事会成员 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntitySaicEmployeeQuery extends SysEntitySaicEmployeePo {

    @Serial
    private static final long serialVersionUID = 1L;
}