package com.heju.system.report.manager.impl;

import com.heju.system.report.domain.po.SysFinanceReportPo;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.query.SysFinanceReportQuery;
import com.heju.system.report.domain.model.SysFinanceReportConverter;
import com.heju.system.report.mapper.SysFinanceReportMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.report.manager.ISysFinanceReportManager;
import org.springframework.stereotype.Component;

/**
 * 财税报表管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFinanceReportManager extends BaseManagerImpl<SysFinanceReportQuery, SysFinanceReportDto, SysFinanceReportPo, SysFinanceReportMapper, SysFinanceReportConverter> implements ISysFinanceReportManager {
}