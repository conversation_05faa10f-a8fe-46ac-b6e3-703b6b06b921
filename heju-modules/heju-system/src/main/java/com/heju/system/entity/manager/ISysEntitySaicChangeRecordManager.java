package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.po.SysEntitySaicChangeRecordPo;
import com.heju.system.entity.domain.query.SysEntitySaicChangeRecordQuery;

import java.util.List;

/**
 * 实体工商变更记录管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntitySaicChangeRecordManager extends IBaseManager<SysEntitySaicChangeRecordQuery, SysEntitySaicChangeRecordDto> {

    List<SysEntitySaicChangeRecordPo> getByEntityId(Long id);
}