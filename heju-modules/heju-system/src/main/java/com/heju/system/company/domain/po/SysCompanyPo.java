package com.heju.system.company.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 子公司 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_company")
public class SysCompanyPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 公司编码 */
    @Excel(name = "公司编码")
    protected String code;

    /** 统一信用代码 */
    @Excel(name = "统一信用代码")
    protected String creditNo;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}