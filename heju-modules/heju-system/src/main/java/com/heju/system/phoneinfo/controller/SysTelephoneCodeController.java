package com.heju.system.phoneinfo.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.query.SysTelephoneCodeQuery;
import com.heju.system.phoneinfo.service.ISysTelephoneCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
/**
 * 转发短信管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/telephoneCode")
public class SysTelephoneCodeController extends BaseController<SysTelephoneCodeQuery, SysTelephoneCodeDto, ISysTelephoneCodeService> {

    @Autowired
    private ISysTelephoneCodeService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "转发短信" ;
    }

    /**
     * 查询转发短信列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_TELEPHONE_CODE_LIST)
    public AjaxResult list(SysTelephoneCodeQuery telephoneCode) {
        return super.list(telephoneCode);
    }

    /**
     * 查询转发短信列表
     */
    @GetMapping("/phoneMsgList")
    public AjaxResult phoneMsgList(SysTelephoneCodeQuery telephoneCode) {
        startPage();
        List<SysTelephoneCodeDto> list = service.phoneMsgList(telephoneCode);
        return getDataTable(list);
    }


    /**
     * 查询转发短信详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_TELEPHONE_CODE_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 转发短信新增
     */
    @PostMapping
//    @Log(title = "转发短信管理")
    public AjaxResult add(@RequestBody Map<String, Object> body) {
        // Map转 SysTelephoneCodeDto 对象
        SysTelephoneCodeDto telephoneCode = new SysTelephoneCodeDto();
        String receiveStr = (String) body.get("text");
        if (StringUtils.hasText(receiveStr)) {
            String[] parts = receiveStr.split("\\|", 3);
            if (parts.length >= 1) telephoneCode.setSource(parts[0]);
            if (parts.length >= 2) telephoneCode.setMsgContent(parts[1]);
            if (parts.length >= 3) {
                String receiveValue = parts[2];
                // 移除 "SIMX_" 格式的前缀（X为数字）
                receiveValue = receiveValue.replaceFirst("^SIM\\d+_", "");
                telephoneCode.setReceive(receiveValue);
            }
        }
        telephoneCode.initOperate(BaseConstants.Operate.ADD);
        AEHandle(telephoneCode.getOperate(), telephoneCode);
        return toAjax(baseService.insert(telephoneCode));
    }

    /**
     * 获取转发短信选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

}
