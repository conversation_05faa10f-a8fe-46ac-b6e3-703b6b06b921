package com.heju.system.entity.service.impl;

import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.system.entity.domain.query.SysEntityTaxationInvoiceTypeQuery;
import com.heju.system.entity.service.ISysEntityTaxationInvoiceTypeService;
import com.heju.system.entity.manager.ISysEntityTaxationInvoiceTypeManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体税务票种认定管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityTaxationInvoiceTypeServiceImpl extends BaseServiceImpl<SysEntityTaxationInvoiceTypeQuery, SysEntityTaxationInvoiceTypeDto, ISysEntityTaxationInvoiceTypeManager> implements ISysEntityTaxationInvoiceTypeService {

    /**
     * 查询实体税务票种认定对象列表 | 数据权限
     *
     * @param entityTaxationInvoiceType 实体税务票种认定对象
     * @return 实体税务票种认定对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntityTaxationInvoiceTypeMapper"})
    public List<SysEntityTaxationInvoiceTypeDto> selectListScope(SysEntityTaxationInvoiceTypeQuery entityTaxationInvoiceType) {
        return baseManager.selectList(entityTaxationInvoiceType);
    }

}