package com.heju.system.report.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.po.SysBillReportPo;
import com.heju.system.report.domain.query.SysBillReportQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 发票管理详情 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysBillReportConverter extends BaseConverter<SysBillReportQuery, SysBillReportDto, SysBillReportPo> {
}
