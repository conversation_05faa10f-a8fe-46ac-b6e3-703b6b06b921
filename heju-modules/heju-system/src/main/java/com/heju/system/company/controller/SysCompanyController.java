package com.heju.system.company.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.query.SysCompanyQuery;
import com.heju.system.company.service.ISysCompanyService;
import com.heju.system.third.service.ISysThirdAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 子公司管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/company")
public class SysCompanyController extends BaseController<SysCompanyQuery, SysCompanyDto, ISysCompanyService> {

    @Autowired
    private ISysThirdAuthService thirdAuthService;

    @Autowired
    private ISysCompanyService companyService;

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "子公司";
    }

    /**
     * 查询子公司列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_COMPANY_LIST)
    public AjaxResult list(SysCompanyQuery company) {
        return super.list(company);
    }

    /**
     * 查询子公司详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_COMPANY_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 子公司新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_COMPANY_ADD)
    @Log(title = "子公司管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysCompanyDto company) {
        return super.add(company);
    }

    /**
     * 子公司修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_COMPANY_EDIT)
    @Log(title = "子公司管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysCompanyDto company) {
        company.setEnterpriseId(SecurityUtils.getEnterpriseId());
        return super.edit(company);
    }

    /**
     * 子公司修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_COMPANY_EDIT, Auth.SYS_COMPANY_ES}, logical = Logical.OR)
    @Log(title = "子公司管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysCompanyDto company) {
        company.setEnterpriseId(SecurityUtils.getEnterpriseId());
        return super.editStatus(company);
    }

    /**
     * 子公司批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_COMPANY_DEL)
    @Log(title = "子公司管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取子公司选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


    /**
     * 查询公司-三方认证信息列表
     */
    @GetMapping(value = "listAuth/{thirdId}")
    @RequiresPermissions(Auth.SYS_COMPANY_LIST)
    public AjaxResult listAuth(@PathVariable Long thirdId) {
        return baseService.listAuth(thirdId);
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysCompanyDto company) {
        if (baseService.checkCompanyCodeUnique(company.getId(), company.getCode()))
            warn(StrUtil.format("{}{}{}失败，公司编码已存在", operate.getInfo(), getNodeName(), company.getName()));
        else if (baseService.checkNameUnique(company.getId(), company.getName()))
            warn(StrUtil.format("{}{}{}失败，公司名称已存在", operate.getInfo(), getNodeName(), company.getName()));
    }

    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        if (thirdAuthService.getByCompanyIds(idList).size() > 0) {
            warn(StrUtil.format("{}{}失败，公司已第三方认证信息，请先解除绑定", operate.getInfo(), getNodeName()));
        }
    }

    /**
     * 获取公司下拉框列表
     *
     * @param tenantId 租户id
     * @return 公司列表
     */
    @GetMapping("/drop/{tenantId}")
    @RequiresPermissions(Auth.SYS_COMPANY_LIST)
    public SysCompanyDto[] drop(@PathVariable String tenantId) {
        return companyService.drop(tenantId);
    }
}
