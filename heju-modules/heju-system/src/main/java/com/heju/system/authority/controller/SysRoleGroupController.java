package com.heju.system.authority.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.query.SysRoleGroupQuery;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.system.authority.service.ISysRoleGroupService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 角色组管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/roleGroup")
public class SysRoleGroupController extends BaseController<SysRoleGroupQuery, SysRoleGroupDto, ISysRoleGroupService> {


    @Resource
    private ISysRoleGroupService sysRoleGroupService;


    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "角色组" ;
    }

    /**
     * 查询角色组列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ROLE_GROUP_LIST)
    public AjaxResult list(SysRoleGroupQuery roleGroup) {
        return super.list(roleGroup);
    }

    /**
     * 查询角色组详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_ROLE_GROUP_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 角色组新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_ROLE_GROUP_ADD)
    @Log(title = "角色组管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysRoleGroupDto roleGroup) {
        return super.add(roleGroup);
    }

    /**
     * 角色组修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_ROLE_GROUP_EDIT)
    @Log(title = "角色组管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysRoleGroupDto roleGroup) {
        return super.edit(roleGroup);
    }

    /**
     * 角色组修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_ROLE_GROUP_EDIT, Auth.SYS_ROLE_GROUP_ES}, logical = Logical.OR)
    @Log(title = "角色组管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysRoleGroupDto roleGroup) {
        return super.editStatus(roleGroup);
    }

    /**
     * 角色组批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_ROLE_GROUP_DEL)
    @Log(title = "角色组管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }


    /**
     * 根据登录用户查询角色组
     */
    @GetMapping("/getUserRoleGroup")
    public AjaxResult getUserRoleGroup(){
        List<SysRoleGroupPo> list = sysRoleGroupService.getUserRoleGroup();
        return AjaxResult.success(list);
    }

    /**
     * 查询用户列表
     */
    @GetMapping("/getUser")
    public AjaxResult getUser(){
        List<SysUserPo> userPoList = sysRoleGroupService.getUser();
        return AjaxResult.success(userPoList);
    }

    /**
     *查询角色组以及角色组中的角色
     */
    @GetMapping("/getUserRoleGroupAndRole")
    public AjaxResult getUserRoleGroupAndRole(){
        List<SysRoleGroupDto> sysRoleGroupDtoList  =  sysRoleGroupService.getUserRoleGroupAndRole();
        return AjaxResult.success(sysRoleGroupDtoList);
    }


    /**
     * 获取角色组选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    interface Auth {
        /** 系统 - 角色组管理 - 列表 */
        String SYS_ROLE_GROUP_LIST = "system:roleGroup:list";
        /** 系统 - 角色组管理 - 详情 */
        String SYS_ROLE_GROUP_SINGLE = "system:roleGroup:single";
        /** 系统 - 角色组管理 - 新增 */
        String SYS_ROLE_GROUP_ADD = "system:roleGroup:add";
        /** 系统 - 角色组管理 - 修改 */
        String SYS_ROLE_GROUP_EDIT = "system:roleGroup:edit";
        /** 系统 - 角色组管理 - 修改状态 */
        String SYS_ROLE_GROUP_ES = "system:roleGroup:es";
        /** 系统 - 角色组管理 - 删除 */
        String SYS_ROLE_GROUP_DEL = "system:roleGroup:delete";
    }
}
