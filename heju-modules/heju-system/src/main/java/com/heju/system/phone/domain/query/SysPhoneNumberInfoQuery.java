package com.heju.system.phone.domain.query;

import com.heju.system.phone.domain.po.SysPhoneNumberInfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 手机号 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysPhoneNumberInfoQuery extends SysPhoneNumberInfoPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 查询手机号
     */
    private String phoneNumber;

    /**
     * 授权id
     */
    private Long userId;
}
