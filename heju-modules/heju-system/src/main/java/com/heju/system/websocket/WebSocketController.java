package com.heju.system.websocket;

import com.heju.common.core.web.result.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * websocket请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/websocket")
public class WebSocketController {

    /**
     * 推送消息到指定用户
     */
    @GetMapping("/push/{tenantId}/{userId}/{message}")
    public AjaxResult pushToUser(@PathVariable("tenantId") Long tenantId, 
                                @PathVariable("userId") Long userId, 
                                @PathVariable("message") String message) {
        try {
            WebSocketServer.sendInfo(message, tenantId, userId);
            return AjaxResult.success("推送成功");
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 推送消息到所有用户
     */
    @GetMapping("/pushAll/{message}")
    public AjaxResult pushToAll(@PathVariable("message") String message) {
        try {
            WebSocketServer.sendInfo(message, null, null);
            return AjaxResult.success("推送成功");
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
