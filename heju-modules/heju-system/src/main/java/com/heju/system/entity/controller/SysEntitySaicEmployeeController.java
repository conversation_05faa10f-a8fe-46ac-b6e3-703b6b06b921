package com.heju.system.entity.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.query.SysEntitySaicEmployeeQuery;
import com.heju.system.entity.service.ISysEntitySaicEmployeeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 实体工商董事会成员管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/saicEmployee")
public class SysEntitySaicEmployeeController extends BaseController<SysEntitySaicEmployeeQuery, SysEntitySaicEmployeeDto, ISysEntitySaicEmployeeService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "实体工商董事会成员" ;
    }

    /**
     * 查询实体工商董事会成员列表
     */
    @Override
    @GetMapping("/list")
    public AjaxResult list(SysEntitySaicEmployeeQuery entitySaicEmployee) {
        return super.list(entitySaicEmployee);
    }

    /**
     * 查询实体工商董事会成员详细
     */
    @Override
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 实体工商董事会成员新增
     */
    @Override
    @PostMapping
    @Log(title = "实体工商董事会成员管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntitySaicEmployeeDto entitySaicEmployee) {
        return super.add(entitySaicEmployee);
    }

    /**
     * 实体工商董事会成员修改
     */
    @Override
    @PutMapping
    @Log(title = "实体工商董事会成员管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysEntitySaicEmployeeDto entitySaicEmployee) {
        return super.edit(entitySaicEmployee);
    }

    /**
     * 实体工商董事会成员批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @Log(title = "实体工商董事会成员管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取实体工商董事会成员选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

}
