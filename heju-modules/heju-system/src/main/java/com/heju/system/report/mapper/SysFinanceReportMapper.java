package com.heju.system.report.mapper;

import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysFinanceReportQuery;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.po.SysFinanceReportPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 财税报表管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysFinanceReportMapper extends BaseMapper<SysFinanceReportQuery, SysFinanceReportDto, SysFinanceReportPo> {
    @Select("select * from sys_finance_report where del_flag = '0'" +
            "and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)")
    List<SysFinanceReportDto> findAll(SysReportManagementQuery query);

    @Select("select * from sys_finance_report where del_flag = '0'" +
            "and (case when #{reporttypeType} is not null then finance_type = #{reporttypeType} else 1=1 end)" +
            "and (case when #{reporttimeType} is not null then reporttime_type = #{reporttimeType} else 1=1 end)" +
            "and (case when #{year} is not null then year = #{year} else 1=1 end)" +
            "and entity_id = #{entityId}")
    List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query);
}