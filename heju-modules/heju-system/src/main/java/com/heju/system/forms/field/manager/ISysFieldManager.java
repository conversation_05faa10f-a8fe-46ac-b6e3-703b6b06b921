package com.heju.system.forms.field.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;

import java.util.List;

/**
 * 字段管理管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysFieldManager extends IBaseManager<SysFieldQuery, SysFieldDto> {

    /**
     * 校验参数编码是否唯一
     *
     * @param Id   参数Id
     * @param apiName API名称
     * @param sheetId 表单id
     * @return 参数对象
     */
    SysFieldPo checkConfigCodeUnique(Long Id, String apiName,Long sheetId);

    void insertFieldToSql(SysFieldDto dto);

    void updateSheetColumn(SysFieldDto dto);

    List<SysFieldPo> selectQuote(List<Long> ids);

    List<SysFieldPo> selectRelation(List<Long> ids);

    void deleteSheetField(List<SysFieldDto> fieldDtoList,String sheetApiName);

    SysFieldDto selectPrimary(Long sheetId);

    List<Long> selectIdsByFieldName(String fieldName);

    List<SysFieldDto> selectBySheetIds(List<Long> sheetIds);

    List<SysFieldDto> selectByApiNames(List<String> apiName,Long sheetId);

    List<SysFieldDto> selectQuoteByApiNames(List<Long> ids);

    List<SysFieldDto> selectReferencingByIds(List<Long> ids);

    List<SysFieldDto> selectReferencedByIds(List<Long> ids);
}
