package com.heju.system.organize.mapper;

import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import com.heju.system.api.organize.domain.po.SysEnterprisePo;
import com.heju.system.api.organize.domain.query.SysEnterpriseQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 企业管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface SysEnterpriseMapper extends BaseMapper<SysEnterpriseQuery, SysEnterpriseDto, SysEnterprisePo> {

    @Select("select b.tenant_user_id from sys_base_user a left join sys_user_tenant_merge b on a.id=b.user_id " +
            "left join te_tenant c on c.id=b.tenant_id where a.telephone=#{telephone} and c.`name`=#{enterpriseName} and b.tenant_user_id = #{userId}")
    Long getUserIdByTenant(@Param("enterpriseName") String enterpriseName, @Param("telephone") String telephone,@Param("userId") Long userId);

    @Select("select b.tenant_user_id from sys_base_user a left join sys_user_tenant_merge b on a.id=b.user_id " +
            "left join te_tenant c on c.id=b.tenant_id where a.open_id=#{unionId} and c.`name`=#{enterpriseName}")
    Long getUserIdByTenantUnionId(@Param("enterpriseName") String enterpriseName, @Param("unionId") String unionId);

    @Select("select `id` from sys_base_user where telephone=#{telephone}")
    String getIdByTelephone(@Param("telephone") String telephone);

    @Insert("insert into sys_base_user (`id`,telephone) values (#{id},#{telephone})")
    int insertByTelephone(@Param("id") String id,@Param("telephone") String telephone);

    @Insert("insert into sys_user_tenant_merge (user_id,tenant_id,tenant_user_id) values (#{userId},#{tenantId},#{tenantUserId})")
    int insertTenantUser(@Param("tenantUserId")Long tenantUserId,@Param("tenantId") Long tenantId, @Param("userId")String userId);
}
