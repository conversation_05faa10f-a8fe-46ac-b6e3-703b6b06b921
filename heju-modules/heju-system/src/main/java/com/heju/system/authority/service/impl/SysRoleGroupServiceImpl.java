package com.heju.system.authority.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.api.authority.domain.query.SysRoleGroupQuery;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.system.authority.manager.ISysRoleGroupManager;
import com.heju.system.authority.mapper.SysRoleGroupMapper;
import com.heju.system.authority.mapper.SysRoleMapper;
import com.heju.system.authority.service.ISysRoleGroupService;
import com.heju.system.organize.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色组管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleGroupServiceImpl extends BaseServiceImpl<SysRoleGroupQuery, SysRoleGroupDto, ISysRoleGroupManager> implements ISysRoleGroupService {

    @Resource
    private SysRoleGroupMapper sysRoleGroupMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;


    /**
     * 查询角色组对象列表 | 数据权限
     *
     * @param roleGroup 角色组对象
     * @return 角色组对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysRoleGroupMapper"})
    public List<SysRoleGroupDto> selectListScope(SysRoleGroupQuery roleGroup) {
        if ("00".equals(SecurityUtils.getUserType())){
            return baseManager.selectList(roleGroup);
        }
        Long userId = SecurityUtils.getUserId();
        roleGroup.setUserId(userId);
        return baseManager.selectList(roleGroup);
    }


    /**
     * 查询登录用户存在的角色组
     * @return
     */
    @Override
    public List<SysRoleGroupPo> getUserRoleGroup() {
        if ("00".equals(SecurityUtils.getUserType())){
            return sysRoleGroupMapper.selectList(null);
        }
        Long userId = SecurityUtils.getUserId();
        QueryWrapper<SysRoleGroupPo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysRoleGroupPo::getUserId,userId);
        List<SysRoleGroupPo> sysRoleGroupPos = sysRoleGroupMapper.selectList(queryWrapper);
        return sysRoleGroupPos;
    }

    @Override
    public List<SysUserPo> getUser() {
        List<SysUserPo> userPoList = sysUserMapper.selectList(null);
        return userPoList;

    }

    @Override
    public List<SysRoleGroupDto>  getUserRoleGroupAndRole() {
        List<SysRoleGroupPo> roleGroupPoList = getUserRoleGroup();
        List<SysRoleGroupDto> roleGroupDtoList = new ArrayList<>();
        for (SysRoleGroupPo po : roleGroupPoList) {
            SysRoleGroupDto dto = new SysRoleGroupDto();
            BeanUtils.copyProperties(po, dto);
            roleGroupDtoList.add(dto);
        }

       return roleGroupDtoList.stream().peek(dto -> {
                    Long roleGroupId = dto.getId();
                    QueryWrapper<SysRolePo> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(SysRolePo::getRoleGroupId, roleGroupId);
                    List<SysRolePo> sysRolePoList = sysRoleMapper.selectList(queryWrapper);
                    dto.setSysRolePoList(sysRolePoList);
                })
                .collect(Collectors.toList());


    }
}