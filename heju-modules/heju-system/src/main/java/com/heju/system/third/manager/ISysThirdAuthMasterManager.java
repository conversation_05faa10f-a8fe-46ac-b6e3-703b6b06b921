package com.heju.system.third.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;

import java.util.List;
import java.util.Map;

/**
 * 第三方认证管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysThirdAuthMasterManager extends IBaseManager<SysThirdAuthQuery, SysThirdAuthDto> {

    /**
     * 校验第三方认证信息编码是否唯一
     *
     * @param Id   第三方认证信息Id
     * @param code 第三方认证信息编码
     * @return 第三方认证信息对象
     */
    SysThirdAuthDto checkThirdAuthCodeUnique(Long Id, String code);

    void send2RestCloud(Map<String,List<Long>> companyIdListMap, int type,Long thirdAuthId);

}