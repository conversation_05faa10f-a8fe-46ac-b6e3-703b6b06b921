package com.heju.system.monitor.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.api.log.domain.dto.SysLoginLogDto;
import com.heju.system.api.log.domain.query.SysLoginLogQuery;

/**
 * 访问日志管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysLoginLogManager extends IBaseManager<SysLoginLogQuery, SysLoginLogDto> {

    /**
     * 清空系统登录日志
     */
    void cleanLoginLog();
}
