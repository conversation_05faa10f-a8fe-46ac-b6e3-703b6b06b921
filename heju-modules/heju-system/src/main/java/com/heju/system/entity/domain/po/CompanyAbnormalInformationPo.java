package com.heju.system.entity.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "sys_entity_exception_info_history_records")
public class CompanyAbnormalInformationPo {
//    private String orgName;
//    private String companyName;
//    private String iReason;
//    private String oDate;
//    private String oReason;
//    private String iDate;
//    private Long entityId;
    private String orgName;
    private String companyName;
    private String iReason;
    private Date oDate;  // 修改为 Date 类型
    private String oReason;
    private Date iDate;  // 修改为 Date 类型
    private Long entityId;
}
