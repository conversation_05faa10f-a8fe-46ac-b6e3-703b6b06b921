package com.heju.system.declaration.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 税务申报 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_tax_filings", excludeProperty = { NAME })
public class SysTaxFilingsPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

    /** 申报编码 */
    @Excel(name = "申报编码")
    protected String code;

    /** 税务申报类型(0 增值税,1 消费税,2 关税,3 所得税) */
    @Excel(name = "税务申报类型(0 增值税,1 消费税,2 关税,3 所得税)")
    protected String taxType;

    /** 报表时间类型(0月度,1季度,2年度) */
    @Excel(name = "报表时间类型(0月度,1季度,2年度)")
    protected String reporttimeType;

    /** 年份 */
    @Excel(name = "年份")
    protected String year;

    /** 月份 */
    @Excel(name = "月份")
    protected String month;

    /** 季度(0春季,1夏季,2秋季,3冬季) */
    @Excel(name = "季度(0春季,1夏季,2秋季,3冬季)")
    protected String season;

    /** 报表地址 */
    @Excel(name = "报表地址")
    protected String reportAddress;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    protected String reason;

    /** 申报状态(0已申报 1审批通过 2审批驳回) */
    @Excel(name = "申报状态(0已申报 1审批通过 2审批驳回)")
    protected String declareStatus;

    /** 无法申报原因 */
    @Excel(name = "无法申报原因")
    protected String declareRemark;
}