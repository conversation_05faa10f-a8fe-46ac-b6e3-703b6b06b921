package com.heju.system.seal.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.merge.SysFieldRoleMerge;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.manager.ISysFieldManager;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import com.heju.system.forms.optionValue.manager.ISysOptionValueManager;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.manager.ISysSheetManager;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import com.heju.system.forms.universal.mapper.UniversalMapper;
import com.heju.system.forms.universal.service.UniversalService;
import com.heju.system.seal.domain.query.SealQuery;
import com.heju.system.seal.mapper.SysSealMapper;
import com.heju.system.seal.service.ISysSealService;
import com.heju.system.utils.FieldTypeConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SysSealServiceImpl implements ISysSealService {

    @Resource
    private ISysSheetManager sysSheetManager;

    @Resource
    private ISysFieldManager sysFieldManager;

    @Resource
    private ISysOptionValueManager optionValueManager;

    @Resource
    private ISysCascadeManager cascadeManager;

    @Resource
    private SysSealMapper mapper;

    @Resource
    private UniversalService universalService;

    @Resource
    private UniversalMapper universalMapper;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    /**
     * 字段列表查询
     * @param sheetApiName
     * @return
     */
    @Override
    public List<SysFieldDto> listEntity(String sheetApiName) {
//        SysSheetDto sysEntitySheetDto = sysSheetManager.selectOne("sys_base_entity");
//        Long entitySheetId = sysEntitySheetDto.getId();
//        SysFieldQuery sysFieldQuery = new SysFieldQuery();
//        sysFieldQuery.setSheetId(entitySheetId);
//        List<SysFieldDto> sysEntityFieldDtos = sysFieldManager.selectList(sysFieldQuery);
//        //实体字段信息
//        Set<String> targetApiNames = Set.of("credit_no", "name", "code");
//        List<SysFieldDto> list = sysEntityFieldDtos.stream()
//                .filter(field -> targetApiNames.contains(field.getApiName()))
//                .toList();
        //查表id
        SysSheetDto sysSheetDto = sysSheetManager.selectOne(sheetApiName);
        Long sheetId = sysSheetDto.getId();
        //查表字段信息
        SysFieldQuery sysFieldQuery = new SysFieldQuery();
        sysFieldQuery.setSheetId(sheetId);
        List<SysFieldDto> sysFieldDtos = sysFieldManager.selectList(sysFieldQuery);
        //TODO 查询 sys_entity   get credit_no name code
        if (sysFieldDtos == null || sysFieldDtos.isEmpty()) {
            return Collections.emptyList();
        }
        for (SysFieldDto sysFieldDto : sysFieldDtos) {
            switch (sysFieldDto.getFieldType()) {
                case FieldTypeConstants.SELECT_SINGLE, FieldTypeConstants.SELECT_MULTI -> {
                    SysOptionValueQuery optionValueQuery = new SysOptionValueQuery();
                    optionValueQuery.setOptionId(sysFieldDto.getOptionId());
                    List<SysOptionValueDto> sysOptionValueDtos = optionValueManager.selectList(optionValueQuery);
                    sysFieldDto.setOptionValueList(sysOptionValueDtos);
                }
                case FieldTypeConstants.CASCADE -> {
                    List<Tree<String>> trees = cascadeManager.selectCascadeRelation(sysFieldDto.getCascadeId());
                    sysFieldDto.setCasecadeTreeList(trees);
                }
            }
        }
        return sysFieldDtos;
    }

    @Override
    public AjaxResult list(UniversalQuery query) {
        // 查询表单 id
        SysSheetDto sysSheetDto = sysSheetManager.selectOne(query.getSheetApiName());
        if (sysSheetDto == null) {
            return AjaxResult.error("错误的业务表单");
        }
        // 查询当前角色可见字段
        List<Long> sheetId = new ArrayList<>();
        sheetId.add(sysSheetDto.getId());
        List<SysFieldDto> sysFieldDtos = universalService.getFieldByRoleIds(sheetId);
        //根据字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectSheetMultiList = new ArrayList<>();
        List<SysFieldDto> selectsheetSingleList = new ArrayList<>();
        List<SysFieldDto> cascadeList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE))
                .orElse(Collections.emptyList()));
        List<SysFieldDto> dateList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.DATE))
                .orElse(Collections.emptyList()));
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectsheetSingleList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_MULTI))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSheetMultiList.add(sysFieldDto);
                    } else {
                        selectMultiList.add(sysFieldDto);
                    }
                });
        Set<Long> optionIds = new HashSet<>();
        if (!selectSingleList.isEmpty()) {
            optionIds.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectMultiList.isEmpty()) {
            optionIds.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Set<Long> sheetOptionIds = new HashSet<>();
        if (!selectsheetSingleList.isEmpty()) {
            sheetOptionIds.addAll(selectsheetSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectSheetMultiList.isEmpty()) {
            sheetOptionIds.addAll(selectSheetMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        // 查选项 映射为 id -> name
        Map<Long, String> optionValueMap = new HashMap<>();
        Map<Long, Map<Long, String>> sheetValueMap = new HashMap<>();
        LambdaQueryWrapper<SysOptionValuePo> wrapper = Wrappers.lambdaQuery();
        if (!optionIds.isEmpty()) {
            wrapper.in(SysOptionValuePo::getOptionId, optionIds);
            List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(wrapper);
            optionValueMap = optionValueList.stream()
                    .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if (!sheetOptionIds.isEmpty()) {
            List<SysSheetDto> sheetDtos = sysSheetManager.selectListByIds(sheetOptionIds);
            for (SysSheetDto sheetDto : sheetDtos) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        // 执行查询
        query.setPage((query.getPage() - 1) * query.getPageSize());
        query.setOrderSort(StrUtil.toUnderlineCase(query.getOrderSort()));
        List<Map<String, Object>> list = mapper.selectListWithQuote(query.getSheetApiName(), query);
        // 处理返回结果
        for (Map<String, Object> stringObjectMap : list) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> relatedItems = (List<Map<String, Object>>) stringObjectMap.get("relatedItems");
            if (relatedItems != null && !relatedItems.isEmpty()) {
                for (Map<String, Object> relatedItem : relatedItems) {
                    for (SysFieldDto sysFieldDto : selectSingleList) {
                        Long fieldValue = Long.parseLong(relatedItem.get(sysFieldDto.getApiName()).toString());
                        relatedItem.put(sysFieldDto.getApiName(), optionValueMap.get(fieldValue));
                    }
                    for (SysFieldDto sysFieldDto : selectMultiList) {
                        String fieldValue = relatedItem.get(sysFieldDto.getApiName()).toString();
                        List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                        List<String> multiValue = new ArrayList<>();
                        for (String s : split) {
                            Long key = Long.parseLong(s);
                            multiValue.add(optionValueMap.get(key));
                        }
                        relatedItem.put(sysFieldDto.getApiName(), String.join(",", multiValue));
                    }
                    for (SysFieldDto fieldDto : selectsheetSingleList) {
                        Long fieldValue = Long.parseLong(relatedItem.get(fieldDto.getApiName()).toString());
                        relatedItem.put(fieldDto.getApiName(),sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                    }
                    for (SysFieldDto fieldDto : selectSheetMultiList) {
                        String fieldValue = relatedItem.get(fieldDto.getApiName()).toString();
                        List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                        List<String> multiValue=new ArrayList<>();
                        for (String s : split) {
                            Long a=Long.parseLong(s);
                            multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(a));
                        }
                        relatedItem.put(fieldDto.getApiName(),String.join(",",multiValue));
                    }
                    for (SysFieldDto sysFieldDto : cascadeList) {
                        String fieldValue = relatedItem.get(sysFieldDto.getApiName()).toString();
                        List<String> split = Arrays.stream(fieldValue.split("-")).toList();
                        LambdaQueryWrapper<SysOptionValuePo> queryWrapper = Wrappers.lambdaQuery();
                        queryWrapper.in(SysOptionValuePo::getId, split);
                        List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(queryWrapper);
                        Map<Long, String> cascadeMap = sysOptionValuePos.stream()
                                .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                        List<String> cascadeValue = new ArrayList<>();
                        for (String s : split) {
                            Long id = Long.valueOf(s);
                            cascadeValue.add(cascadeMap.get(id));
                        }
                        relatedItem.put(sysFieldDto.getApiName(), String.join("-", cascadeValue));
                    }
                    // 将日期类型 转 字符串 ，防止序列化 变成 时间戳
                    for (SysFieldDto sysFieldDto : dateList) {
                        String fieldValue = relatedItem.get(sysFieldDto.getApiName()).toString();
                        relatedItem.put(sysFieldDto.getApiName(), fieldValue);
                    }
                }
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        if (!list.isEmpty()) { resultMap.put("list", list); }
        resultMap.put("total", mapper.countList(query.getSheetApiName()));
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }


    @Override
    public AjaxResult manageList(SealQuery query) {
        // 传入 id, apiName
        String apiName = query.getApiName();
        SysSheetDto sysSheetDto = sysSheetManager.selectOne(apiName);
        if (sysSheetDto == null) { return AjaxResult.error("错误的业务表单"); }
        List<Long> sheetIds = new ArrayList<>();
        sheetIds.add(sysSheetDto.getId());
        // 查询角色可见字段
        List<SysFieldDto> fieldByRoleIds = universalService.getFieldByRoleIds(sheetIds);
        // 过滤 文件类型 字段
        fieldByRoleIds = fieldByRoleIds.stream().filter(sysFieldDto -> !sysFieldDto.getFieldType().equals(FieldTypeConstants.FILE)).toList();
        // 按字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = fieldByRoleIds.stream().collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        List<SysFieldDto> cascadeList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE)).orElse(Collections.emptyList()));
        List<SysFieldDto> dateList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.DATE)).orElse(Collections.emptyList()));
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectsheetSingleList = new ArrayList<>();
        List<SysFieldDto> selectSheetMultiList = new ArrayList<>();
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectsheetSingleList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_MULTI))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSheetMultiList.add(sysFieldDto);
                    } else {
                        selectMultiList.add(sysFieldDto);
                    }
                });
        // 去重
        Set<Long> optionIds = new HashSet<>();
        if (!selectSingleList.isEmpty()) {
            optionIds.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectMultiList.isEmpty()) {
            optionIds.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Set<Long> sheetOptionIds = new HashSet<>();
        if (!selectsheetSingleList.isEmpty()) {
            sheetOptionIds.addAll(selectsheetSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectSheetMultiList.isEmpty()) {
            sheetOptionIds.addAll(selectSheetMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Map<Long, String> optionValueMap = new HashMap<>();
        Map<Long, Map<Long, String>> sheetValueMap = new HashMap<>();
        if (!optionIds.isEmpty()) {
            LambdaQueryWrapper<SysOptionValuePo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(SysOptionValuePo::getOptionId, optionIds);
            List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(wrapper);
            optionValueMap = optionValueList.stream()
                    .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        // 拼接 sql
        String selectSql = String.join(",", fieldByRoleIds.stream().map(SysFieldDto::getApiName)
                .filter(Objects::nonNull)
                .map(name -> query.getApiName() + "." + name)
                .toList());
        // 执行查询
        query.setPage((query.getPage() - 1) * query.getPageSize());
        List<Map<String, Object>> manageList = mapper.selectManageList(query.getApiName(), selectSql, query);
        // 处理返回结果
        if (manageList != null && !manageList.isEmpty()) {
            for (Map<String, Object> stringObjectMap : manageList) {
                for (SysFieldDto fieldDto : selectSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(), fieldValue);
                }
                for (SysFieldDto fieldDto : selectMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> list = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue = new ArrayList<>();
                    for (String s : list) {
                        Long key = Long.parseLong(s);
                        multiValue.add(optionValueMap.get(key));
                    }
                    stringObjectMap.put(fieldDto.getApiName(), String.join(",", multiValue));
                }
                for (SysFieldDto fieldDto : selectsheetSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(), sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectSheetMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> list = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue = new ArrayList<>();
                    for (String s : list) {
                        Long key = Long.parseLong(s);
                        multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(key));
                    }
                    stringObjectMap.put(fieldDto.getApiName(), String.join(",", multiValue));
                }
                for (SysFieldDto fieldDto : cascadeList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split("-")).toList();
                    LambdaQueryWrapper<SysOptionValuePo> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(SysOptionValuePo::getOptionId, split);
                    List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(queryWrapper);
                    Map<Long, String> cascadeMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                    List<String> cascadeValue = new ArrayList<>();
                    for (String s : split) {
                        Long key = Long.valueOf(s);
                        cascadeValue.add(cascadeMap.get(key));
                    }
                    stringObjectMap.put(fieldDto.getApiName(), String.join("-", cascadeValue));
                }
                for (SysFieldDto fieldDto : dateList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    stringObjectMap.put(fieldDto.getApiName(), fieldValue);
                }
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        if (manageList != null && !manageList.isEmpty()) { resultMap.put("manageList", manageList); }
        resultMap.put("total", mapper.countManageList(query.getApiName()));
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    /**
     * 新增
     * @param commonJson
     * @return
     */
    @Override
    public AjaxResult add(String commonJson) {
        JSONObject jsonObject = JSON.parseObject(commonJson);
        return normalAdd(jsonObject);
    }

    public AjaxResult normalAdd(JSONObject jsonObject){
        // 输出：insert into user_info(
        StringBuilder insertSql= new StringBuilder("insert into " + jsonObject.get("sheetApiName").toString() + "(");
        //insert into user_info(name, age, email) values (?, ?, ?)
        StringBuilder valueSql= new StringBuilder(") values (");
        jsonObject.remove("sheetApiName");
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            insertSql.append(key).append(",");
            Object value = entry.getValue();
            valueSql.append("'").append(value).append("',");
        }
        insertSql = new StringBuilder(insertSql.substring(0, insertSql.length() - 1));
        valueSql = new StringBuilder(valueSql.substring(0, valueSql.length() - 1) + ")");
        if(mapper.insert(insertSql+ valueSql.toString())>0){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult edit(String commonJson) {
        JSONObject jsonObject = JSON.parseObject(commonJson);
        Long id=Long.parseLong(jsonObject.get("id").toString());
        String sheetApiName=jsonObject.get("sheetApiName").toString();
        jsonObject.remove("sheetApiName");
        jsonObject.remove("id");
        if(mapper.update(jsonObject,id,sheetApiName)>0){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult batchRemove(UniversalQuery query) {
        if(mapper.delete(query.getSheetApiName(),query.getIdList())>0){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    /**
     * 根据表单api名称获取可见字段列表
     * @param sheetId 表单对象
     * @return 字段列表
     */
//    public List<SysFieldDto> getFieldByRoleIds(Long sheetId){
//        //查询角色可见字段
//        List<SysFieldDto> sysFieldDtos;
//        if(SecurityUtils.getUser().isAdmin()){
//            SysFieldQuery fieldQuery=new SysFieldQuery();
//            fieldQuery.setSheetId(sheetId);
//            sysFieldDtos=fieldService.selectList(fieldQuery);
//        }else {
//            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
//            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
//            lqw.eq(SysFieldRoleMerge::getSheetId, sheetId);
//            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
//            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
//            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
//            sysFieldDtos= fieldService.selectListByIds(fieldIds);
//        }
//        return sysFieldDtos;
//    }

    @Override
    public AjaxResult manageFieldList(SealQuery query) {
        List<Long> sheetIds = new ArrayList<>();
        sheetIds.add(sysSheetManager.selectOne(query.getApiName()).getId());
        List<SysFieldDto> fieldByRoleIds = universalService.getFieldByRoleIds(sheetIds);
        return AjaxResult.success(fieldByRoleIds);
    }
}
