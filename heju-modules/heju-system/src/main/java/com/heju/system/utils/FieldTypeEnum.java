package com.heju.system.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字段类型枚举
 */
@Getter
@AllArgsConstructor
public enum FieldTypeEnum {

    VARCHAR("1", "文本"),

    SELECT_SINGLE("2", "单选"),

    SELECT_MULTI("3", "多选"),

    TEXT("4","文本域"),

    INTEGER("5","整数"),

    CURRENCY("6","货币"),

    DATE("7","日期"),

    RELATION("8","关联关系"),

    QUOTE("9","引用类型"),

    TELEPHONE("10","电话"),

    E_MAIL("11","邮箱"),

    WEBSITE("12","网站"),

    PICTURE("13","图片"),

    FILE("14","文件"),

    CASCADE("15","级联类型");

    private final String code;

    private final String info;
}
