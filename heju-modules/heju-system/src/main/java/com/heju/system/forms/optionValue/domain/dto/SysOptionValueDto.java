package com.heju.system.forms.optionValue.domain.dto;

import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 选项值 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysOptionValueDto extends SysOptionValuePo {

    @Serial
    private static final long serialVersionUID = 1L;

}