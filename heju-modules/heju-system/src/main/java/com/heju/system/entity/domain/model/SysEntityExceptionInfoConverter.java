package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 企业经营异常信息 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntityExceptionInfoConverter extends BaseConverter<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto, SysEntityExceptionInfoPo> {
}
