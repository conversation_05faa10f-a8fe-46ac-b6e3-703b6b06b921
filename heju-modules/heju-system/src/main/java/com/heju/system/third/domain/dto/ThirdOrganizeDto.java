package com.heju.system.third.domain.dto;

import com.heju.system.company.domain.dto.CompanyDeptPostDto;
import com.heju.system.company.domain.dto.CompanyDeptPostIdDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdOrganizeDto extends SysThirdAuthDto{

    private List<CompanyDeptPostDto> addCompanyList;

    private List<CompanyDeptPostIdDto> delCompanyList;

}
