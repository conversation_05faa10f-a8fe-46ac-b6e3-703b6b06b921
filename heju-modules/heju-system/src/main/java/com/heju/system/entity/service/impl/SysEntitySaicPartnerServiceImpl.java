package com.heju.system.entity.service.impl;

import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.system.entity.domain.query.SysEntitySaicPartnerQuery;
import com.heju.system.entity.service.ISysEntitySaicPartnerService;
import com.heju.system.entity.manager.ISysEntitySaicPartnerManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体工商股东管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntitySaicPartnerServiceImpl extends BaseServiceImpl<SysEntitySaicPartnerQuery, SysEntitySaicPartnerDto, ISysEntitySaicPartnerManager> implements ISysEntitySaicPartnerService {

    /**
     * 查询实体工商股东对象列表 | 数据权限
     *
     * @param entitySaicPartner 实体工商股东对象
     * @return 实体工商股东对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntitySaicPartnerMapper"})
    public List<SysEntitySaicPartnerDto> selectListScope(SysEntitySaicPartnerQuery entitySaicPartner) {
        return baseManager.selectList(entitySaicPartner);
    }

}