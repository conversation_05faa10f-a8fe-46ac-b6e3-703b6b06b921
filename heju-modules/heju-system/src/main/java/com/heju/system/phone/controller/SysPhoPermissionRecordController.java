package com.heju.system.phone.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.system.phone.service.ISysPhoPermissionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 手机号授权管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/phone/record")
public class SysPhoPermissionRecordController extends BaseController<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto, ISysPhoPermissionRecordService> {

    @Autowired
    private ISysPhoPermissionRecordService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "手机号授权" ;
    }

    /**
     * 查询手机号授权列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_PHO_PERMISSION_RECORD_LIST)
    public AjaxResult list(SysPhoPermissionRecordQuery phoPermissionRecord) {
        return super.list(phoPermissionRecord);
    }


    /**
     * 查询手机授权记录列表
     */
    @GetMapping("/overTime/list")
    public AjaxResult overTime(SysPhoPermissionRecordQuery phoPermissionRecord) {
        startPage();
        List<SysPhoPermissionRecordDto> list = service.overTimeList(phoPermissionRecord);
        return getDataTable(list);
    }

    /**
     * 查询手机号授权详细
     */
    @Override
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(Auth.SYS_PHO_PERMISSION_RECORD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 手机号授权新增
     */
    @Override
    @PostMapping
//    @RequiresPermissions(Auth.SYS_PHO_PERMISSION_RECORD_ADD)
    @Log(title = "手机号授权管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysPhoPermissionRecordDto phoPermissionRecord) {
        return super.add(phoPermissionRecord);
    }

    /**
     * 手机号授权修改
     */
    @Override
    @PutMapping
//    @RequiresPermissions(Auth.SYS_PHO_PERMISSION_RECORD_EDIT)
    @Log(title = "手机号授权管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysPhoPermissionRecordDto phoPermissionRecord) {
        return super.edit(phoPermissionRecord);
    }

    /**
     * 手机号授权修改状态
     */
    @Override
    @PutMapping("/status")
//    @RequiresPermissions(value = {Auth.SYS_PHO_PERMISSION_RECORD_EDIT, Auth.SYS_PHO_PERMISSION_RECORD_ES}, logical = Logical.OR)
    @Log(title = "手机号授权管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysPhoPermissionRecordDto phoPermissionRecord) {
        return super.editStatus(phoPermissionRecord);
    }

    /**
     * 手机号授权批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
//    @RequiresPermissions(Auth.SYS_PHO_PERMISSION_RECORD_DEL)
    @Log(title = "手机号授权管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取手机号授权选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 手机号授权 批量新增、修改接口
     */
    @PostMapping("/batch/operation")
    public AjaxResult batchOperation(@Validated({V_A.class}) @RequestBody List<SysPhoPermissionRecordDto> permissionRecords) {
        return toAjax(service.batchOperation(permissionRecords));
    }

}
