package com.heju.system.entity.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 企业经营异常信息 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_exception_info", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK, NAME })
public class SysEntityExceptionInfoPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;



    protected Long entityId;

    /** 经营状态（0：正常    1：异常） */
    @Excel(name = "经营状态", readConverterExp = "0=：正常,1=：异常")
    protected Integer businessStatus;

    /** 异常原因 */
    @Excel(name = "异常原因")
    protected String iReason;

    /** 异常时间 */
    @Excel(name = "异常时间")
    protected String iDate;

}