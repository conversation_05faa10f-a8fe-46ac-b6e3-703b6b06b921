package com.heju.system.report.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.query.SysTaxReportQuery;
import com.heju.system.report.service.ISysTaxReportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 税务申报管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tax")
public class SysTaxReportController extends BaseController<SysTaxReportQuery, SysTaxReportDto, ISysTaxReportService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "税务申报" ;
    }

    /**
     * 查询税务申报列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_TAX_REPORT_LIST)
    public AjaxResult list(SysTaxReportQuery taxReport) {
        return super.list(taxReport);
    }

    /**
     * 查询税务申报详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_TAX_REPORT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 税务申报新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_TAX_REPORT_ADD)
    @Log(title = "税务申报管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysTaxReportDto taxReport) {
        return super.add(taxReport);
    }

    /**
     * 税务申报修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_TAX_REPORT_EDIT)
    @Log(title = "税务申报管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysTaxReportDto taxReport) {
        return super.edit(taxReport);
    }

    /**
     * 税务申报修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_TAX_REPORT_EDIT, Auth.SYS_TAX_REPORT_ES}, logical = Logical.OR)
    @Log(title = "税务申报管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysTaxReportDto taxReport) {
        return super.editStatus(taxReport);
    }

    /**
     * 税务申报批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_TAX_REPORT_DEL)
    @Log(title = "税务申报管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取税务申报选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


}
