package com.heju.system.entity.service;


import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.entity.domain.po.SysEntity;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityQuery;

import java.text.ParseException;
import java.util.List;

/**
 * 实体信息管理管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysEntityService {

    AjaxResult list(SysEntityQuery entityQuery);

    AjaxResult getInfo(SysEntityQuery entityQuery);

    AjaxResult add(SysEntityQuery entityQuery) throws ParseException;

    AjaxResult batchRemove(List<Long> idList);

    AjaxResult edit(String entityJson);

    AjaxResult option();

    void importData(List<SysEntity> entityList, String UUID);

    SysEntityPo[] drop();

    List<SysEntityFieldPo> selectRoleHasEntity(Long roleId);

    List<SysEntityFieldPo> selectUserRoleAllEntityFields();
}

