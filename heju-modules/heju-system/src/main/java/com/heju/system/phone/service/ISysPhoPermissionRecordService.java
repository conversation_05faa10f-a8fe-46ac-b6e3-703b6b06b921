package com.heju.system.phone.service;

import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;

/**
 * 手机号授权管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysPhoPermissionRecordService extends IBaseService<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto> {
    /**
     * 批量操作 (新增、修改)
     */
    int batchOperation(List<SysPhoPermissionRecordDto> permissionRecords);

    /**
     * 授权记录
     */
    List<SysPhoPermissionRecordDto> overTimeList(SysPhoPermissionRecordQuery permissionRecord);

}
