package com.heju.system.approval.domain.po;

import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 客户信息审核 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_approval_customerinfo", excludeProperty = {  NAME })
public class SysApprovalCustomerinfoPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 字段中文名 */
    @Excel(name = "字段id")
    protected Long fieldId;

    /** 实体名称 */
    @Excel(name = "业务表id")
    protected Long businessId;

    /** 修改前 */
    @Excel(name = "修改前")
    protected String beforeUpdate;

    /** 修改后 */
    @Excel(name = "修改后")
    protected String afterUpdate;

    /** 字段所属模块 */
    @Excel(name = "表单表id")
    protected Long sheetId;

}