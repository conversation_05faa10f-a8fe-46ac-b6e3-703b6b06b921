package com.heju.system.forms.sheet.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.po.SysSheetPo;
import com.heju.system.forms.sheet.domain.query.SysSheetQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 表单管理 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysSheetConverter extends BaseConverter<SysSheetQuery, SysSheetDto, SysSheetPo> {
}
