package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationInvoiceTypePo;
import com.heju.system.entity.domain.query.SysEntityTaxationInvoiceTypeQuery;

import java.util.List;

/**
 * 实体税务票种认定管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntityTaxationInvoiceTypeManager extends IBaseManager<SysEntityTaxationInvoiceTypeQuery, SysEntityTaxationInvoiceTypeDto> {

    List<SysEntityTaxationInvoiceTypePo> getByEntityId(Long id);

    int updateByExamine(SysEntityExamineDto entityExamine);
}