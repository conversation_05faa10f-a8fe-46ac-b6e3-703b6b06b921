package com.heju.system.annualReport.mapper;

import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import org.apache.ibatis.annotations.Select;

/**
 * 工商年报管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysAnnualReportMapper extends BaseMapper<SysAnnualReportQuery, SysAnnualReportDto, SysAnnualReportPo> {
    @Select("<script>" +
            "select * from sys_annual_report where del_flag = '0'" +
            "and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)" +
            "and (case when #{reportYear} is not null then report_year = #{reportYear} else 1=1 end)" +
            "</script>")
    SysAnnualReportPo selectByEntityAndYear(SysAnnualReportDto annualReport);
}