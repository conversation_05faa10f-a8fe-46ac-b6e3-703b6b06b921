package com.heju.system.entity.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.query.SysEntityTaxationTypeQuery;
import com.heju.system.entity.service.ISysEntityTaxationTypeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 实体税务税费种认定管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/taxationType")
public class SysEntityTaxationTypeController extends BaseController<SysEntityTaxationTypeQuery, SysEntityTaxationTypeDto, ISysEntityTaxationTypeService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "实体税务税费种认定" ;
    }

    /**
     * 查询实体税务税费种认定列表
     */
    @Override
    @GetMapping("/list")
    public AjaxResult list(SysEntityTaxationTypeQuery entityTaxationType) {
        return super.list(entityTaxationType);
    }

    /**
     * 查询实体税务税费种认定详细
     */
    @Override
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 实体税务税费种认定新增
     */
    @Override
    @PostMapping
    @Log(title = "实体税务税费种认定管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntityTaxationTypeDto entityTaxationType) {
        return super.add(entityTaxationType);
    }

    /**
     * 实体税务税费种认定修改
     */
    @Override
    @PutMapping
    @Log(title = "实体税务税费种认定管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysEntityTaxationTypeDto entityTaxationType) {
        return super.edit(entityTaxationType);
    }

    /**
     * 实体税务税费种认定批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @Log(title = "实体税务税费种认定管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取实体税务税费种认定选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }
}
