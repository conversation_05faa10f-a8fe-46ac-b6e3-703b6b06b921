package com.heju.system.entity.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntityTaxationUserDto;
import com.heju.system.entity.domain.query.SysEntityTaxationUserQuery;
import com.heju.system.entity.service.ISysEntityTaxationUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 实体税务人员信息管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/taxationUse")
public class SysEntityTaxationUserController extends BaseController<SysEntityTaxationUserQuery, SysEntityTaxationUserDto, ISysEntityTaxationUserService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "实体税务人员信息" ;
    }

    /**
     * 查询实体税务人员信息列表
     */
    @Override
    @GetMapping("/list")
    public AjaxResult list(SysEntityTaxationUserQuery entityTaxationUser) {
        return super.list(entityTaxationUser);
    }

    /**
     * 查询实体税务人员信息详细
     */
    @Override
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 实体税务人员信息新增
     */
    @Override
    @PostMapping
    @Log(title = "实体税务人员信息管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntityTaxationUserDto entityTaxationUser) {
        return super.add(entityTaxationUser);
    }

    /**
     * 实体税务人员信息修改
     */
    @Override
    @PutMapping
    @Log(title = "实体税务人员信息管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysEntityTaxationUserDto entityTaxationUser) {
        return super.edit(entityTaxationUser);
    }

    /**
     * 实体税务税费种认定批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @Log(title = "实体税务人员信息删除", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取实体税务人员信息选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }
}
