package com.heju.system.entity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.heju.common.core.constant.basic.MessageConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.utils.core.pool.NumberPool;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.entity.domain.EntityTypeConstant;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityQuery;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.entity.service.ISysEntityFieldService;
import com.heju.system.entity.service.ISysTaxManagementService;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.mapper.SysMessageEntityChangeMapper;
import com.heju.system.notice.mapper.SysMessageMapper;
import com.heju.system.organize.service.ISysUserService;
import com.heju.system.utils.MessageUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SysTaxManagementServiceImpl implements ISysTaxManagementService {

    @Resource
    private SysEntityMapper sysEntityMapper;
    @Resource
    private ISysEntityFieldService sysEntityFieldService;
    @Resource
    private ISysUserService userService;
    @Resource
    private SysMessageEntityChangeMapper messageEntityChangeMapper;
    @Resource
    private SysMessageMapper messageMapper;

    @Override
    public AjaxResult getInfo(SysEntityQuery entityQuery) {
        Long id = entityQuery.getId();
        Map<String, Object> taxInfo = sysEntityMapper.getInfo(id);
        return AjaxResult.success(taxInfo);
    }

    @Override
    public AjaxResult edit(String entityJson) {
        JSONObject jsonObject = JSON.parseObject(entityJson);
        String entityType = jsonObject.get(EntityTypeConstant.ENTITY_TYPE).toString();
        Long id = Long.parseLong(jsonObject.get("id").toString());
        //变更前实体信息
        SysEntityPo sysEntityPo = sysEntityMapper.selectById(id);
        JSONObject beforeEntity = JSON.parseObject(JSON.toJSONString(sysEntityPo));
        List<SysEntityFieldDto> sysEntityFieldDtos = sysEntityFieldService.selectList(null);
        Map<String, List<SysEntityFieldDto>> collect = sysEntityFieldDtos.stream().collect(Collectors.groupingBy(SysEntityFieldPo::getFieldBelong));
        int flag = 0;
        String changeField = "";
        String before = "";
        String after = "";
        if (entityType.equals(EntityTypeConstant.TAXATION)) {
            List<SysEntityFieldDto> sysEntityFieldTaxation = collect.get("2");
            if (collect.get("3") != null && collect.get("3").size() > 0) {
                sysEntityFieldTaxation.addAll(collect.get("3"));
            }
            if (collect.get("5") != null && collect.get("5").size() > 0) {
                sysEntityFieldTaxation.addAll(collect.get("5"));
            }
            JSONObject taxationJson = new JSONObject();
            for (SysEntityFieldDto sysEntityFieldDto : sysEntityFieldTaxation) {
                if (sysEntityFieldDto.getIsChange() == NumberPool.One && jsonObject.get(sysEntityFieldDto.getFieldName()) != null) {
                    changeField = changeField + " " + sysEntityFieldDto.getFieldComment();
                    before = before + " " + sysEntityFieldDto.getFieldComment() + ":" +  beforeEntity.get(sysEntityFieldDto.getFieldName());
                    after = after + " " + sysEntityFieldDto.getFieldComment() + ":" + jsonObject.get(sysEntityFieldDto.getFieldName());
                    taxationJson.put(sysEntityFieldDto.getFieldName(), jsonObject.get(sysEntityFieldDto.getFieldName()));
                }
            }
            flag = sysEntityMapper.updateTaxation(taxationJson, id);
        }
        //发送站内信
        List<SysUserDto> user = userService.selectListByReception(MessageConstants.Reception.YES.getCode());
        if (!user.isEmpty()){
            SysMessageEntityChangeDto entityChange = MessageUtil.packDetails(id, changeField, before, after);
            messageEntityChangeMapper.insertEntityChange(entityChange);
            List<SysMessageDto> sysMessages = MessageUtil.packingMessage(sysEntityPo.getName(),
                    MessageConstants.type.ENTITY_CHANGES.getInfo(), MessageConstants.type.ENTITY_CHANGES.getCode(),
                    before, after, SecurityContextHolder.getUserId(), user,entityChange.getId());
            messageMapper.sendMessage(sysMessages);
        }
        return flag > 0 ? AjaxResult.success() : AjaxResult.error();
    }
}
