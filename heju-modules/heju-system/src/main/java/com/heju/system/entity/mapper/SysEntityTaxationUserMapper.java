package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntityTaxationUserDto;
import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import com.heju.system.entity.domain.query.SysEntityTaxationUserQuery;

/**
 * 实体税务人员信息管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityTaxationUserMapper extends BaseMapper<SysEntityTaxationUserQuery, SysEntityTaxationUserDto, SysEntityTaxationUserPo> {
}