package com.heju.system.entity.manager.impl;


import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.model.SysEntityExceptionInfoConverter;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;
import com.heju.system.entity.manager.ISysEntityExceptionInfoManager;
import com.heju.system.entity.mapper.SysEntityExceptionInfoBMapper;
import org.springframework.stereotype.Component;

/**
 * 企业经营异常信息管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntityExceptionInfoManager extends BaseManagerImpl<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto, SysEntityExceptionInfoPo, SysEntityExceptionInfoBMapper, SysEntityExceptionInfoConverter> implements ISysEntityExceptionInfoManager {
}