package com.heju.system.forms.optionValue.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 选项值 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysOptionValueConverter extends BaseConverter<SysOptionValueQuery, SysOptionValueDto, SysOptionValuePo> {
}
