package com.heju.system.entity.service.impl;

import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.query.SysEntitySaicChangeRecordQuery;
import com.heju.system.entity.service.ISysEntitySaicChangeRecordService;
import com.heju.system.entity.manager.ISysEntitySaicChangeRecordManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体工商变更记录管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntitySaicChangeRecordServiceImpl extends BaseServiceImpl<SysEntitySaicChangeRecordQuery, SysEntitySaicChangeRecordDto, ISysEntitySaicChangeRecordManager> implements ISysEntitySaicChangeRecordService {

    /**
     * 查询实体工商变更记录对象列表 | 数据权限
     *
     * @param entitySaicChangeRecord 实体工商变更记录对象
     * @return 实体工商变更记录对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntitySaicChangeRecordMapper"})
    public List<SysEntitySaicChangeRecordDto> selectListScope(SysEntitySaicChangeRecordQuery entitySaicChangeRecord) {
        return baseManager.selectList(entitySaicChangeRecord);
    }

}