package com.heju.system.authority.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.system.authority.domain.query.SysDisplayInfoQuery;
import com.heju.system.authority.service.ISysDisplayInfoService;
import com.heju.system.authority.manager.ISysDisplayInfoManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 显隐列管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysDisplayInfoServiceImpl extends BaseServiceImpl<SysDisplayInfoQuery, SysDisplayInfoDto, ISysDisplayInfoManager> implements ISysDisplayInfoService {

    /**
     * 查询显隐列对象列表 | 数据权限
     *
     * @param displayInfo 显隐列对象
     * @return 显隐列对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysDisplayInfoMapper"})
    public List<SysDisplayInfoDto> selectListScope(SysDisplayInfoQuery displayInfo) {
        return baseManager.selectList(displayInfo);
    }

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @DSTransactional
    public int addAndModify(SysDisplayInfoDto dto) {
        SysDisplayInfoQuery query = new SysDisplayInfoQuery();
        if (dto.getApiCode() == null) {
            throw new IllegalArgumentException("apiCode不能为空");
        }
        query.setUserId(dto.getUserId());
        query.setApiCode(dto.getApiCode());
        List<SysDisplayInfoDto> dtoList = baseManager.selectList(query);
        if (dtoList != null && !dtoList.isEmpty()) {
            for (SysDisplayInfoDto infoDto : dtoList) {
                dto.setId(infoDto.getId());
            }
        }
        dto.setUserId(SecurityUtils.getUserId());
        int row;
        if (dtoList.isEmpty()) {
            row = baseManager.insert(dto);
        } else {
            row = baseManager.update(dto);
        }
        return row;
    }

}
