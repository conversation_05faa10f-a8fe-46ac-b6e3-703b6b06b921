package com.heju.system.company.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.domain.query.SysCompanyQuery;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 子公司管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysCompanyService extends IBaseService<SysCompanyQuery, SysCompanyDto> {

    /**
     * 校验公司编码是否唯一
     *
     * @param Id   公司Id
     * @param code 公司编码
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkCompanyCodeUnique(Long Id, String code);

    AjaxResult listAuth(Long thirdId);

    /**
     * 获取redis公司list
     *
     * @return 公司list
     */
    Map<String, SysCompanyDto> getCompanyRedisList();

    /**
     * 获取redis公司-租户关联
     *
     * @return 公司-租户关联map
     */
    Map<String, Object> getCompanyTenantRedisList();

    /**
     * 单条信息同步第三方
     *
     * @param operateType               1-新增；2-修改；3-删除
     * @param organizeType              1-公司；2-部门；3-岗位；4-用户
     * @param companyThirdAuthMergeList 第三方认证信息-公司关联list
     * @param organizeInfo              同步的数据
     */
    void buildOrganizeOne(int operateType, int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList, String organizeInfo);

    void buildDept(List<SysDeptDto> originList);

    void buildPost(List<SysPostDto> originList);

    void buildUser(List<SysUserDto> originList);

    SysCompanyDto[] drop(String tenantId);
}