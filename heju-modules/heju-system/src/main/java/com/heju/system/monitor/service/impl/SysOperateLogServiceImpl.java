package com.heju.system.monitor.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.log.domain.dto.SysOperateLogDto;
import com.heju.system.api.log.domain.query.SysOperateLogQuery;
import com.heju.system.monitor.manager.ISysOperateLogManager;
import com.heju.system.monitor.service.ISysOperateLogService;
import org.springframework.stereotype.Service;

/**
 * 操作日志管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysOperateLogServiceImpl extends BaseServiceImpl<SysOperateLogQuery, SysOperateLogDto, ISysOperateLogManager> implements ISysOperateLogService {

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperateLog() {
        baseManager.cleanOperateLog();
    }
}
