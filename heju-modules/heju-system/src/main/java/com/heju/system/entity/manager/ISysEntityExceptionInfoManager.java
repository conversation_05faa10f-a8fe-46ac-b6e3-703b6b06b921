package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;

/**
 * 企业经营异常信息管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntityExceptionInfoManager extends IBaseManager<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto> {
}