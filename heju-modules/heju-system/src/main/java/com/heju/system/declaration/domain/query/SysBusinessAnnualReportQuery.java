package com.heju.system.declaration.domain.query;

import com.heju.system.declaration.domain.po.SysBusinessAnnualReportPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工商年报 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysBusinessAnnualReportQuery extends SysBusinessAnnualReportPo {

    @Serial
    private static final long serialVersionUID = 1L;
}