package com.heju.system.entity.domain.po;

import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体工商分支机构 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_saic_branch", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK })
public class SysEntitySaicBranchPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 公司id */
    @Excel(name = "公司id")
    protected Long entityId;

}