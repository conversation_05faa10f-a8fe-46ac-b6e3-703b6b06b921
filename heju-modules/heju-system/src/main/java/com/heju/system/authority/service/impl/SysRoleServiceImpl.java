package com.heju.system.authority.service.impl;

import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.system.AuthorityConstants;
import com.heju.common.core.utils.core.CollUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.datascope.annotation.DataScope;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleEntityDto;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.api.authority.domain.query.SysRoleQuery;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.authority.domain.merge.SysRoleEntityFieldMerge;
import com.heju.system.authority.domain.merge.SysRoleMenuMerge;
import com.heju.system.authority.domain.vo.SysRoleGroupScopeTree;
import com.heju.system.authority.manager.ISysRoleManager;
import com.heju.system.authority.mapper.SysRoleMapper;
import com.heju.system.authority.mapper.merge.SysRoleEntityFieldMergeMapper;
import com.heju.system.authority.mapper.merge.SysRoleMenuMergeMapper;
import com.heju.system.authority.service.ISysAuthService;
import com.heju.system.authority.service.ISysRoleService;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.organize.domain.vo.SysOrganizeTree;
import com.heju.system.organize.service.ISysOrganizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.heju.common.core.constant.basic.SecurityConstants.CREATE_BY;

/**
 * 角色管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl extends BaseServiceImpl<SysRoleQuery, SysRoleDto, ISysRoleManager> implements ISysRoleService {

    @Resource
    private ISysAuthService authService;

    @Resource
    private ISysOrganizeService organizeService;

    @Resource
    private SysRoleEntityFieldMergeMapper sysRoleEntityFieldMergeMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;


    /**
     * 查询角色对象列表 | 数据权限 | 附加数据
     *
     * @param role 角色对象
     * @return 角色对象集合
     */
    @Override
    @DataScope(userAlias = CREATE_BY, mapperScope = {"SysRoleMapper"})
    public List<SysRoleDto> selectListScope(SysRoleQuery role) {
        return baseManager.selectList(role);
    }

    /**
     * 新增角色对象
     *
     * @param role 角色对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysRoleDto role) {
        int row = baseManager.insert(role);
        if (row > 0) {
            authService.addRoleAuth(role.getId(), role.getAuthIds());
            organizeService.addRoleOrganizeMerge(role.getId(),
                    StrUtil.equals(role.getDataScope(), AuthorityConstants.DataScope.CUSTOM.getCode())
                            ? role.getOrganizeIds()
                            : new Long[]{});
            SysRoleEntityDto sysRoleEntityDto = new SysRoleEntityDto();
            sysRoleEntityDto.setEntityFieldId(role.getEntityIds());
            sysRoleEntityDto.setRoleId(role.getId());
            roleBatchesAddEntity(sysRoleEntityDto);
        }
        return row;
    }


    /**
     * 修改角色组织权限
     *
     * @param role 角色对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int updateDataScope(SysRoleDto role) {
        int row = baseManager.updateDataScope(role.getId(), role.getRoleKey(), role.getDataScope());
        if (row > 0) {
            organizeService.editRoleOrganizeMerge(role.getId(),
                    StrUtil.equals(role.getDataScope(), AuthorityConstants.DataScope.CUSTOM.getCode())
                            ? role.getOrganizeIds()
                            : new Long[]{});
        }
        return row;
    }

    /**
     * 校验角色编码是否唯一
     *
     * @param Id   角色Id
     * @param code 角色编码
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkRoleCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkRoleCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }

    /**
     * 校验角色权限是否唯一
     *
     * @param Id      角色Id
     * @param roleKey 角色权限
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkRoleKeyUnique(Long Id, String roleKey) {
        return ObjectUtil.isNotNull(baseManager.checkRoleKeyUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, roleKey));
    }


    /**
     * 角色批量新增实体字段
     */
    @Override
    @DSTransactional
    public void roleBatchesAddEntity(SysRoleEntityDto sysRoleEntityDto) {
        Long roleId = sysRoleEntityDto.getRoleId();
        QueryWrapper<SysRoleEntityFieldMerge> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRoleEntityFieldMerge::getRoleId, roleId);
        sysRoleEntityFieldMergeMapper.delete(queryWrapper);
        List<Long> entityFieldIds = sysRoleEntityDto.getEntityFieldId();
        List<SysRoleEntityFieldMerge> list = new ArrayList<>();
        for (Long fieldId : entityFieldIds) {
            SysRoleEntityFieldMerge sysRoleEntityFieldMerge1 = new SysRoleEntityFieldMerge();
            sysRoleEntityFieldMerge1.setRoleId(sysRoleEntityDto.getRoleId());
            sysRoleEntityFieldMerge1.setEntityFieldId(fieldId);
            list.add(sysRoleEntityFieldMerge1);
        }
        sysRoleEntityFieldMergeMapper.insertBatch(list);
    }

    @Override
    public SysUserDto getRoleIdsByRoleKey(String roleKey) {
        SysRoleQuery sysRoleQuery = new SysRoleQuery();
        sysRoleQuery.setRoleKey(roleKey);
        List<SysRoleDto> sysRoleDtos = baseManager.selectList(sysRoleQuery);
        Long[] array = sysRoleDtos.stream()
                .flatMap(roleDto -> Arrays.stream(new Long[]{roleDto.getId()}))
                .distinct()
                .toArray(Long[]::new);
        SysUserDto userDto = new SysUserDto();
        userDto.setRoleIds(array);
        return userDto;
    }

    /**
     * 查询角色组集合列表
     * @return
     */
    @Override
    public List<SysRolePo> getRoleGroupList() {
        QueryWrapper<SysRolePo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRolePo::getIsRoleGroup,0);
        List<SysRolePo> sysRolePos = sysRoleMapper.selectList(queryWrapper);
        return sysRolePos;
    }

    /**
     * 查询角色组织树
     * @return
     */
    @Override
    public List<SysRoleGroupScopeTree> selectRoleGroupScope() {
        // 查询所有角色和角色组
        List<SysRolePo> allRolesAndGroups = sysRoleMapper.selectList(null);
        return allRolesAndGroups.stream().map(SysRoleGroupScopeTree:: new).collect(Collectors.toList());
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysRoleDto dto) {
        SysRoleDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        authService.editRoleAuth(dto.getId(), dto.getAuthIds());
        SysRoleEntityDto sysRoleEntityDto = new SysRoleEntityDto();
        sysRoleEntityDto.setRoleId(dto.getId());
        sysRoleEntityDto.setEntityFieldId(dto.getEntityIds());
        roleBatchesAddEntity(sysRoleEntityDto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        return row;
    }
}
