package com.heju.system.forms.cascade.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 级联 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_cascade")
public class SysCascadePo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 根选项id */
    @Excel(name = "根选项id")
    protected Long mainOptionId;

    /** 1级级联选项id */
    @Excel(name = "1级级联选项id")
    protected Long cascade1OptionId;

    /** 2级级联选项id */
    @Excel(name = "2级级联选项id")
    protected Long cascade2OptionId;

    /** 3级级联选项id */
    @Excel(name = "3级级联选项id")
    protected Long cascade3OptionId;

    /** 4级级联选项id */
    @Excel(name = "4级级联选项id")
    protected Long cascade4OptionId;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}