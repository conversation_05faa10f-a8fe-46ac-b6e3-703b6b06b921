package com.heju.system.entity.mapper;

import com.github.pagehelper.Page;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 企业经营异常信息管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityExceptionInfoBMapper extends BaseMapper<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto, SysEntityExceptionInfoPo> {


    @Select("<script>" +
            "SELECT * FROM sys_entity_exception_info WHERE entity_id IN " +
            "<foreach collection=\"entityIdList\" item=\"entityId\" open=\"(\" separator=\",\" close=\")\">" +
            "#{entityId}" +
            "</foreach>" +
            "<if test=\"businessStatus!= null\">" +
            " AND business_status = #{businessStatus}" +
            "</if>" +
            "</script>")
    List<SysEntityExceptionInfoDto>selectP(@Param(value = "entityIdList") List<Long> entityIdList, @Param(value = "businessStatus")Integer businessStatus);
}