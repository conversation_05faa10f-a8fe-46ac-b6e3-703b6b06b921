package com.heju.system.entity.domain.query;

import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体税务税费种认定 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityTaxationTypeQuery extends SysEntityTaxationTypePo {

    @Serial
    private static final long serialVersionUID = 1L;
}