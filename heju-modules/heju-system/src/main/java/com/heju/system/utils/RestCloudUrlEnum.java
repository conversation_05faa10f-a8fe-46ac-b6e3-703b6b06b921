package com.heju.system.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 谷云同步接口枚举
 */
@Getter
@AllArgsConstructor
public enum RestCloudUrlEnum {

    ORGANIZE_ONE("http://10.12.11.214:8080/restcloud/esb01/esb_flow_240118001", "单条组织同步地址"),

    ORGANIZE_TOTAL("http://10.12.11.214:8080/restcloud/esb01/esb_flow_240118001", "多条组织同步地址");

    private final String code;

    private final String info;
}
