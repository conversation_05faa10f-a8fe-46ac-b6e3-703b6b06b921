package com.heju.system.forms.cascade.domain.merge;

import com.baomidou.mybatisplus.annotation.*;
import com.heju.common.core.web.entity.base.BasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 级联关系 持久化对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_cascade_relation")
public class SysCascadeRelationMerge extends BasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId
    protected Long id;

    /** 级联id */
    protected Long cascadeId;

    /** 父级选项值id */
    protected Long mainOptionValueId;

    /** 级联选项值id */
    protected Long cascadeOptionValueId;

    /** 层级 */
    protected Integer level;

}