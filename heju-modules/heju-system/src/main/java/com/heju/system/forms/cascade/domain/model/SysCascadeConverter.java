package com.heju.system.forms.cascade.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.po.SysCascadePo;
import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 级联 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysCascadeConverter extends BaseConverter<SysCascadeQuery, SysCascadeDto, SysCascadePo> {
}
