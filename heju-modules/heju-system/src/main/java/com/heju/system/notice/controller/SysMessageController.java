package com.heju.system.notice.controller;

import com.heju.common.core.constant.basic.MessageConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.query.SysMessageQuery;
import com.heju.system.notice.service.ISysMessageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 消息通知管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/message")
public class SysMessageController extends BaseController<SysMessageQuery, SysMessageDto, ISysMessageService> {

    @Resource
    private ISysMessageService messageService;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "消息通知" ;
    }

    /**
     * 查询消息通知列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_MESSAGE_LIST)
    public AjaxResult list(SysMessageQuery message) {
        return super.list(message);
    }

    /**
     * 查询未读消息通知列表
     */
    @GetMapping("/unreadList")
    @RequiresPermissions(Auth.SYS_MESSAGE_UNREAD)
    public AjaxResult unreadList(SysMessageQuery message) {
        message.setStatus(MessageConstants.Status.UNREAD.getCode());
        startPage();
        List<SysMessageDto> messageList = messageService.selectListScope(message);
        return getDataTable(messageList);
    }

    /**
     * 查询消息通知详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_MESSAGE_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 消息通知新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_MESSAGE_ADD)
    @Log(title = "消息通知管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysMessageDto message) {
        return super.add(message);
    }

    /**
     * 消息通知修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_MESSAGE_ES}, logical = Logical.OR)
    @Log(title = "消息通知管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysMessageDto message) {
        return super.editStatus(message);
    }

    /**
     * 消息通知批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_MESSAGE_DEL)
    @Log(title = "消息通知管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取消息通知选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    @GetMapping("/count")
    public AjaxResult MessageCount(){
        int count = messageService.getMessageCount();
        return AjaxResult.success(count);
    }

    interface Auth {
        /** 系统 - 消息通知管理 - 列表 */
        String SYS_MESSAGE_LIST = "system:message:list";
        /** 系统 - 消息通知管理 - 详情 */
        String SYS_MESSAGE_SINGLE = "system:message:single";
        /** 系统 - 消息通知管理 - 新增 */
        String SYS_MESSAGE_ADD = "system:message:add";
        /** 系统 - 消息通知管理 - 修改状态 */
        String SYS_MESSAGE_ES = "system:message:es";
        /** 系统 - 消息通知管理 - 删除 */
        String SYS_MESSAGE_DEL = "system:message:delete";
        /** 系统 - 消息通知管理 - 未读列表 */
        String SYS_MESSAGE_UNREAD = "system:message:unreadList";
    }
}
