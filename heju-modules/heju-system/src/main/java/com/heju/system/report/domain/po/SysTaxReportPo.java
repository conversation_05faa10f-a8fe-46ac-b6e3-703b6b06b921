package com.heju.system.report.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 税务申报 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_tax_report", excludeProperty = { NAME })
public class SysTaxReportPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体id */
    @Excel(name = "公司id")
    protected Long entityId;

    /** 实体名称 */
    @Excel(name = "实体名称")
    protected String entityName;

    /** 报表编码 */
    @Excel(name = "报表编码")
    protected String code;

    /** 税务申报报表类型(0 增值税及附加税申报表,1 企业所得税申报表,2 个税申报表,3 印花税申报表,4 财务报表申报表,5 社会保险费申报表,6 房产及土地使用税申报表) */
    @Excel(name = "税务申报报表类型(0 增值税及附加税申报表,1 企业所得税申报表,2 个税申报表,3 印花税申报表,4 财务报表申报表,5 社会保险费申报表,6 房产及土地使用税申报表)")
    protected String taxType;

    /** 报表时间类型(0月度,1季度,2年度) */
    @Excel(name = "报表时间类型(0月度,1季度,2年度)")
    protected String reporttimeType;

    /** 年份 */
    @Excel(name = "年份")
    protected String year;

    /** 月份 */
    @Excel(name = "月份")
    protected String month;

    /** 季度(0春季,1夏季,2秋季,3冬季) */
    @Excel(name = "季度(0春季,1夏季,2秋季,3冬季)")
    protected String season;

    /** 报表地址 */
    @Excel(name = "报表地址")
    protected String reportAddress;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}