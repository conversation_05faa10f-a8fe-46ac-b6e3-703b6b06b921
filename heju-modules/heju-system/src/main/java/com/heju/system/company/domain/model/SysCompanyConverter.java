package com.heju.system.company.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyPo;
import com.heju.system.company.domain.query.SysCompanyQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 子公司 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysCompanyConverter extends BaseConverter<SysCompanyQuery, SysCompanyDto, SysCompanyPo> {
}
