package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationInvoiceTypePo;
import com.heju.system.entity.domain.query.SysEntityTaxationInvoiceTypeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体税务票种认定 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntityTaxationInvoiceTypeConverter extends BaseConverter<SysEntityTaxationInvoiceTypeQuery, SysEntityTaxationInvoiceTypeDto, SysEntityTaxationInvoiceTypePo> {
}
