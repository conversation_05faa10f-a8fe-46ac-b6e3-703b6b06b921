package com.heju.system.entity.service;


import com.github.pagehelper.PageInfo;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.CompanyAbnormalInformationPo;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;
import com.heju.system.utils.PageResult;
import io.swagger.v3.oas.models.security.SecurityScheme;

import java.text.ParseException;
import java.util.List;

/**
 * 企业经营异常信息管理 服务层                 
 *
 * <AUTHOR>
 */
public interface ISysEntityExceptionInfoService extends IBaseService<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto> {
    void entityAbnormalInfo(SysEntityPo sysEntityPo) throws ParseException;

    PageResult getHistoryRecord(String companyName, Integer page,Integer pageSize);

    List<SysEntityExceptionInfoDto> refresh(Long[] ids) throws ParseException;


    PageResult  AList(SysEntityExceptionInfoDto sysEntityExceptionInfoDto);
}