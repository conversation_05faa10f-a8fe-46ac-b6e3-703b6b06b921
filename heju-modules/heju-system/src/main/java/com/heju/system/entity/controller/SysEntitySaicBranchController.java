package com.heju.system.entity.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.query.SysEntitySaicBranchQuery;
import com.heju.system.entity.service.ISysEntitySaicBranchService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 实体工商分支机构管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/saicBranch")
public class SysEntitySaicBranchController extends BaseController<SysEntitySaicBranchQuery, SysEntitySaicBranchDto, ISysEntitySaicBranchService> {

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "实体工商分支机构";
    }

    /**
     * 查询实体工商分支机构列表
     */
    @Override
    @GetMapping("/list")
    public AjaxResult list(SysEntitySaicBranchQuery entitySaicBranch) {
        return super.list(entitySaicBranch);
    }

    /**
     * 查询实体工商分支机构详细
     */
    @Override
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 实体工商分支机构新增
     */
    @Override
    @PostMapping
    @Log(title = "实体工商分支机构管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntitySaicBranchDto entitySaicBranch) {
        return super.add(entitySaicBranch);
    }

    /**
     * 实体工商分支机构修改
     */
    @Override
    @PutMapping
    @Log(title = "实体工商分支机构管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysEntitySaicBranchDto entitySaicBranch) {
        return super.edit(entitySaicBranch);
    }

    /**
     * 实体工商分支机构批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @Log(title = "实体工商分支机构管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取实体工商分支机构选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

}
