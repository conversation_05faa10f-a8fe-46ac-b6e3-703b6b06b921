package com.heju.system.notice.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.domain.query.SysMessageEntityChangeQuery;
import com.heju.system.notice.manager.ISysMessageEntityChangeManager;
import com.heju.system.notice.mapper.SysMessageEntityChangeMapper;
import com.heju.system.notice.service.ISysMessageEntityChangeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * 消息实体变更通知管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysMessageEntityChangeServiceImpl extends BaseServiceImpl<SysMessageEntityChangeQuery,SysMessageEntityChangeDto, ISysMessageEntityChangeManager> implements ISysMessageEntityChangeService {

    @Resource
    private SysMessageEntityChangeMapper messageEntityChangeMapper;

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysMessageEntityChangeDto selectById(Serializable id) {
        SysMessageEntityChangeDto dto = messageEntityChangeMapper.getById(id);
        return subCorrelates(dto);
    }
}
