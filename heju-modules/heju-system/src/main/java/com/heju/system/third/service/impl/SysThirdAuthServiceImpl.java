package com.heju.system.third.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.redis.service.RedisService;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.mapper.SysThirdAuthMapper;
import com.heju.system.third.service.ISysThirdAuthService;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;

import com.heju.system.utils.OperateEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 第三方认证管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysThirdAuthServiceImpl extends BaseServiceImpl<SysThirdAuthQuery, SysThirdAuthDto, ISysThirdAuthManager> implements ISysThirdAuthService {

    @Resource
    protected SysThirdAuthMapper sysThirdAuthMapper;

    @Resource
    protected RedisService redisService;

    @Resource
    protected ISysThirdAuthManager iSysThirdAuthManager;

    /**
     * 缓存主键命名定义
     */
    @Override
    protected String getCacheKey() {
        return CacheConstants.CacheType.SYS_THIRD_AUTH_KEY.getCode();
    }

    /**
     * 查询第三方认证对象列表 | 数据权限
     *
     * @param thirdAuth 第三方认证对象
     * @return 第三方认证对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysThirdAuthMapper"})
    public List<SysThirdAuthDto> selectListScope(SysThirdAuthQuery thirdAuth) {
        return baseManager.selectList(thirdAuth);
    }

    @Override
    public boolean checkThirdAuthCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkThirdAuthCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }

    @Override
    @Isolate
    public List<SysCompanyThirdAuthMergeDto> getTenantCpmList(Long thirdId) {
        List<SysCompanyThirdAuthMergeDto> tenantCpmList = sysThirdAuthMapper.getTenantCpmList(thirdId);
        Map<String, SysThirdAuthDto> thirdAuthList = getThirdAuthRedis();
        Iterator<Map.Entry<String, SysThirdAuthDto>> iterator = thirdAuthList.entrySet().iterator();
        //redis中所有的企业数据
        List<SysThirdAuthDto> sysThirdAuthList = new ArrayList<>();
        while (iterator.hasNext()) {
            Map.Entry<String, SysThirdAuthDto> next = iterator.next();
            sysThirdAuthList.add(next.getValue());
        }
        //第一轮筛选的数据   是当前第三方下的 并且 是超管的
        List<SysThirdAuthDto> sysThirdAuthListStream = sysThirdAuthList
                .stream()
                .filter(i -> i.getThirdId().equals(thirdId))
                .toList();
        Map<Long, SysThirdAuthDto> thirdAuthMap = sysThirdAuthListStream.stream()
                .collect(Collectors.toMap(SysThirdAuthDto::getId, thirdAuth -> thirdAuth));
        //过滤认证id相同的，赋值认证账号名称
        if (thirdAuthMap.size() != 0) {
            tenantCpmList.forEach(company -> {
                String name = null;
                if (!ObjectUtil.isNull(thirdAuthMap.get(company.getThirdAuthId()))) {
                    name = thirdAuthMap.get(company.getThirdAuthId()).getName();
                }
                if (!ObjectUtil.isNull(name)) {
                    company.setThirdAuthName(name);
                }
            });

        }
        return tenantCpmList;
    }

    @Override
    @DSTransactional
    @Isolate
    public int addTenantCpmAuth(SysCompanyThirdAuthMergeAddDto dto) {
        Long thirdAuthId = dto.getThirdAuthId();
        Long thirdId = dto.getThirdId();
        //thirdauthid companyid is not null
        //删除 thirdauthid和companyid没有匹配的   没有thirdauthid的()
        //新增
        //数据库当前认证id数据
        List<SysCompanyThirdAuthMergeDto> companyListBefore = sysThirdAuthMapper.getTenantCpmListByAuthId(dto);
        //前端传入的认证信息
        List<SysCompanyThirdAuthMergeDto> companyIdListAfter = dto.getCompanyThirdAuthList();
        //数据转换，list转map  key:companycomid  value:thirdauthid
        HashMap<Long, Long> companyBeforeMap = companyListBefore.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getCompanyId(), item.getThirdAuthId()),
                        HashMap::putAll);
        HashMap<Long, Long> companyAfterMap = companyIdListAfter.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getCompanyId(), item.getThirdAuthId()),
                        HashMap::putAll);
        //需要删除redis的thirdauthid集合

        //需删除的公司id,认证信息id
        List<SysCompanyThirdAuthMerge> delList = iSysThirdAuthManager.getDelList(companyBeforeMap, companyAfterMap);
        //需新增的公司id,认证信息id
        List<SysCompanyThirdAuthMerge> addList = iSysThirdAuthManager.getAddList(delList, companyAfterMap, thirdAuthId, thirdId);

        //删除数据库关联表
        if (delList.size() > 0) {
            sysThirdAuthMapper.delTenantCpmAuth(delList);
        }
        //新增数据库关联表
        if (addList.size() > 0) {
            sysThirdAuthMapper.addTenantCpmAuth(addList);
        }
        //更新redis
        //先新增
        //keySet() 返回所有 map 的 key 组成的 set 集合视图
        Set<Long> companyIdSet = companyAfterMap.keySet();
        String companyId = StringUtils.join(companyIdSet, ",");
        setCompanyThirdAuthRedis(thirdAuthId, companyId);
        //删除redis 找到原来的thirdauthid所在的redis ，删除那个记录
        if (delList.size() > 0) {
            List<SysCompanyThirdAuthMergeDto> delListByRedis = companyIdListAfter.stream()
                    .filter(i -> ObjectUtil.isNotNull(i.getThirdAuthId()) && !thirdAuthId.equals(i.getThirdAuthId()))
                    .collect(Collectors.toList());

            iSysThirdAuthManager.delRedis(delListByRedis);
        }
        //推送谷云
        if(delList.size()>0){
            baseManager.send2RestCloud(delList, OperateEnum.DELETE.getCode());
        }
        if(addList.size()>0){
            baseManager.send2RestCloud(addList, OperateEnum.ADD.getCode());
        }
        return 0;
    }

    @Override
    public List<SysThirdAuthDto> optionByThirdId(Long thirdId) {
        SysThirdAuthQuery sysThirdAuthQuery = new SysThirdAuthQuery();
        sysThirdAuthQuery.setThirdId(thirdId);
        return baseManager.selectList(sysThirdAuthQuery);
    }

    @Override
    public boolean checkByThirdId(List<Long> idList) {
        Map<String, SysThirdAuthDto> thirdAuthList = getThirdAuthRedis();
        ArrayList<SysThirdAuthDto> sysThirdAuthDtoList = new ArrayList<>(thirdAuthList.values());
        Set<Long> collect = sysThirdAuthDtoList.stream().map(SysThirdAuthDto::getThirdId).collect(Collectors.toSet());
        return idList.stream().anyMatch(collect::contains);
    }

    @Override
    public boolean checkCompanyById(List<Long> idList) {
        Map<String, String> thirdAuthList = getCompanyThirdAuthRedis();
        boolean flag = false;
        for (Long thirdAuthId : idList) {
            if (!StringUtils.isEmpty(thirdAuthList.get(String.valueOf(thirdAuthId)))) {
                flag = true;
            }
        }
        return flag;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, String> getCompanyThirdAuthRedis() {
        return redisService.redisTemplate.opsForHash().entries(CacheConstants.CacheType.SYS_COMPANY_THIRD_AUTH_KEY.getCode());
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, SysThirdAuthDto> getThirdAuthRedis() {
        return redisService.redisTemplate.opsForHash().entries(getCacheKey());
    }

    @Override
    public void setCompanyThirdAuthRedis(Long thirdAuthId, String companyId) {
        redisService.setCacheMapValue(CacheConstants.CacheType.SYS_COMPANY_THIRD_AUTH_KEY.getCode(), String.valueOf(thirdAuthId), companyId);
    }

    @Override
    public List<SysCompanyThirdAuthMerge> getByCompanyIds(List<Long> idList) {
        return baseManager.getByCompanyIds(idList);
    }
}