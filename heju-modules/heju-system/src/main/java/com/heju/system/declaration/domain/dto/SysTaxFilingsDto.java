package com.heju.system.declaration.domain.dto;

import com.heju.system.declaration.domain.po.SysTaxFilingsPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 税务申报 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTaxFilingsDto extends SysTaxFilingsPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private String entityName;

    private String declareBy;

}