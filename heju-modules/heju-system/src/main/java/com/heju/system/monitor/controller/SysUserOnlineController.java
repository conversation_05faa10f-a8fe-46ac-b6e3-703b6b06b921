package com.heju.system.monitor.controller;

import com.heju.common.redis.constant.RedisConstants;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.utils.core.ArrayUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.redis.service.RedisService;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.controller.BasisController;
import com.heju.system.api.model.LoginUser;
import com.heju.system.monitor.domain.SysUserOnline;
import com.heju.system.monitor.service.ISysUserOnlineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/online")
public class SysUserOnlineController extends BasisController {

    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private RedisService redisService;

    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ONLINE_LIST)
    public AjaxResult list(String ipaddr, String userName) {
        Collection<String> keys = redisService.keys(RedisConstants.CacheKey.LOGIN_TOKEN_KEY.getCode() + SecurityUtils.getEnterpriseId() + StrUtil.COLON + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<>();
        for (String key : keys) {
            LoginUser loginUser = redisService.getCacheMapValue(key, SecurityConstants.BaseSecurity.LOGIN_USER.getCode());
            if (StrUtil.isNotEmpty(ipaddr) && StrUtil.isNotEmpty(userName)) {
                if (StrUtil.equals(ipaddr, loginUser.getIpaddr()) && StrUtil.equals(userName, loginUser.getUserName())) {
                    userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, loginUser));
                }
            } else if (StrUtil.isNotEmpty(ipaddr)) {
                if (StrUtil.equals(ipaddr, loginUser.getIpaddr())) {
                    userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, loginUser));
                }
            } else if (StrUtil.isNotEmpty(userName)) {
                if (StrUtil.equals(userName, loginUser.getUserName())) {
                    userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, loginUser));
                }
            } else {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(loginUser));
            }
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));
        return getDataTable(userOnlineList);
    }

    /**
     * 强退用户
     */
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_ONLINE_FORCE_LOGOUT)
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    public AjaxResult forceLogout(@PathVariable List<String> idList) {
        if (ArrayUtil.isNotEmpty(idList))
            idList.forEach(id -> redisService.deleteObject(RedisConstants.CacheKey.LOGIN_TOKEN_KEY.getCode() + id));
        return success();
    }
}
