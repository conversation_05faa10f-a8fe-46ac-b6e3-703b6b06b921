package com.heju.system.third.domain.dto;

import com.heju.common.core.web.tenant.base.TBasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysCompanyThirdAuthMergeAddDto extends TBasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 第三方认证id */
    protected Long thirdAuthId;


    protected Long companyId;

    protected Long thirdId;

    protected int isAdmin;

    List<SysCompanyThirdAuthMergeDto> companyThirdAuthList;

}
