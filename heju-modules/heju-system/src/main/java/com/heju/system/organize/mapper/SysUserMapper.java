package com.heju.system.organize.mapper;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.system.api.organize.domain.query.SysUserQuery;
import com.heju.system.organize.domain.vo.SysUserCompany;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.io.Serializable;
import java.util.List;

/**
 * 用户管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysUserMapper extends BaseMapper<SysUserQuery, SysUserDto, SysUserPo> {

    @Select("select * from sys_user where phone = #{phone}")
    SysUserDto selectByPhone(String phone);

    @Select("<script>" +
            "select b.id,b.code,b.user_name,b. nick_name ,b. user_type ,b. phone ,b. email ,b. sex ,b. avatar ,b. profile ,b. password ," +
            "b. login_ip ,b. login_date ,b. sort ,b. status ,b. remark ,b. create_by ,b. create_time ,b. update_by ,b. update_time ,b. del_flag ," +
            "b. tenant_id  from sys_user_post_merge a ,sys_user b  where b. id =a.user_id and a.post_id in " +
            "<foreach collection= 'postIdList' open='(' close=')' separator=',' item='item' >" +
            " #{item}" +
            "</foreach>" +
            "</script>")
    List<SysUserDto> getUserByPost(@Param("postIdList") List<Long> postIdList);

    @Select("<script>" +
            "select b. id ,c.company_id from sys_user_post_merge a ,sys_user b,sys_post c  where b. id =a.user_id and a.post_id=c. id  and b. id  in " +
            "<foreach collection= 'userIdList' open='(' close=')' separator=',' item='item' >" +
            " #{item}" +
            "</foreach>" +
            "</script>")
    List<SysUserCompany> getCompanyById(@Param("userIdList") List<Long> userIdList);

    @Select("select * from sys_user where reception")
    List<SysUserDto> selectByReception(String reception);

    @Select("select id AS 'baseUserId',telephone,open_id,union_id from sys_base_user where open_id = #{openId}")
    SysUserDto selectBaseUserByOpenId(String openId);

    @Insert("insert into sys_user_tenant_merge (user_id, tenant_id, tenant_user_id) VALUES (#{baseUserId},#{tenantId},#{userId})")
    int addUserTenantMerge(Long baseUserId, Long tenantId, Long userId);

    @Insert("insert into sys_user (id, code, user_name, nick_name, login_date, reception, remark, create_by, update_by, update_time, tenant_id, no2hr_id, open_id) " +
            "VALUES (#{id},#{code},#{userName},#{nickName},#{loginDate},#{reception},#{remark},#{createBy},#{updateBy}," +
            "#{updateTime},#{tenantId},#{no2hrId},#{openId})")
    SysUserDto insertGetId(SysUserDto user);

    @Select("select * from sys_user where open_id = #{openId}")
    SysUserDto selectByOpenId(Serializable openId);
}
