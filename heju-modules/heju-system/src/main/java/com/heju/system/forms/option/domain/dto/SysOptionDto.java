package com.heju.system.forms.option.domain.dto;

import com.heju.system.forms.option.domain.po.SysOptionPo;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 选项 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysOptionDto extends SysOptionPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<SysOptionValueDto> optionValueList;

    private String optionValues;
}