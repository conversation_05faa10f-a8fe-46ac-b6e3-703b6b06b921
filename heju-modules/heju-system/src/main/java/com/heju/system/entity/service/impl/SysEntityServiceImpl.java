package com.heju.system.entity.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.cache.utils.DictUtil;
import com.heju.common.core.constant.basic.MessageConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.pool.NumberPool;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.redis.service.RedisService;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.dict.domain.dto.SysDictDataDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.authority.domain.merge.SysRoleEntityFieldMerge;
import com.heju.system.authority.mapper.merge.SysRoleEntityFieldMergeMapper;
import com.heju.system.entity.domain.EntityTypeConstant;
import com.heju.system.entity.domain.dto.CompanyBaseInfoApiDto;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.model.CompanyBaseInfoApiConverter;
import com.heju.system.entity.domain.po.SysEntity;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityQuery;
import com.heju.system.entity.manager.ISysEntitySaicBranchManager;
import com.heju.system.entity.manager.ISysEntitySaicChangeRecordManager;
import com.heju.system.entity.manager.ISysEntitySaicEmployeeManager;
import com.heju.system.entity.manager.ISysEntitySaicPartnerManager;
import com.heju.system.entity.mapper.SysEntityFieldMapper;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.entity.service.ISysEntityExceptionInfoService;
import com.heju.system.entity.service.ISysEntityFieldService;
import com.heju.system.entity.service.ISysEntityService;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.mapper.SysMessageEntityChangeMapper;
import com.heju.system.notice.mapper.SysMessageMapper;
import com.heju.system.api.authority.domain.po.SysOrganizeRoleMerge;
import com.heju.system.organize.mapper.merge.SysOrganizeRoleMergeMapper;
import com.heju.system.organize.service.ISysUserService;
import com.heju.system.utils.CompanyBaseInfoUtil;
import com.heju.system.utils.MessageUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 实体字段管理管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityServiceImpl implements ISysEntityService {

    @Resource
    private SysEntityMapper sysEntityMapper;
    @Resource
    private ISysEntitySaicBranchManager sysEntitySaicBranchManager;
    @Resource
    private ISysEntitySaicChangeRecordManager sysEntitySaicChangeRecordManager;
    @Resource
    private ISysEntitySaicEmployeeManager sysEntitySaicEmployeeManager;
    @Resource
    private ISysEntitySaicPartnerManager sysEntitySaicPartnerManager;

    @Resource
    private ISysEntityFieldService sysEntityFieldService;

    @Resource
    private SysEntityFieldMapper sysEntityFieldMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private SysMessageMapper messageMapper;

    @Resource
    private SysMessageEntityChangeMapper messageEntityChangeMapper;

    @Resource
    private ISysUserService userService;

    @Resource
    private SysRoleEntityFieldMergeMapper sysRoleEntityFieldMergeMapper;

    @Resource
    private SysOrganizeRoleMergeMapper sysOrganizeRoleMergeMapper;

    @Resource
    private ISysEntityExceptionInfoService sysEntityExceptionInfoService;


    @Override
    public AjaxResult list(SysEntityQuery entityQuery) {
        entityQuery.setPage((entityQuery.getPage() - 1) * entityQuery.getPageSize());
        entityQuery.setOrderSort(StrUtil.toUnderlineCase(entityQuery.getOrderSort()));
        List<Map<String, Object>> entityList = sysEntityMapper.list(entityQuery);
        for (Map<String, Object> stringObjectMap : entityList) {
            stringObjectMap.put("create_time", stringObjectMap.get("create_time").toString().replace("T", " "));
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("entityList", entityList);
        resultMap.put("total", sysEntityMapper.countList(entityQuery));
        resultMap.put("pageSize", entityQuery.getPageSize());
        return AjaxResult.success(resultMap);
    }

    @Override
    public AjaxResult getInfo(SysEntityQuery entityQuery) {
        Long id = entityQuery.getId();
        Map<String, Object> entityInfo = sysEntityMapper.getInfo(id);
        if (entityQuery.getEntityType().equals(EntityTypeConstant.SAIC)) {
            //查询实体工商分支机构
            entityInfo.put("saicBranchList", sysEntitySaicBranchManager.getByEntityId(id));
            //实体工商变更记录
            entityInfo.put("changeRecordList", sysEntitySaicChangeRecordManager.getByEntityId(id));
            //实体工商董事会成员
            entityInfo.put("saicEmployeeList", sysEntitySaicEmployeeManager.getByEntityId(id));
            //实体工商股东
            entityInfo.put("saicPartnerList", sysEntitySaicPartnerManager.getByEntityId(id));
        }
        return AjaxResult.success(entityInfo);
    }

    @Override
    @DSTransactional
    public AjaxResult add(SysEntityQuery entityQuery) throws ParseException {
        //查询阿里api获取工商信息
        CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(entityQuery.getCreditNo());

        if (companyBaseInfoApiDto.getName() == null) {
            return AjaxResult.error("统一社会信用代码错误，未找到该企业");
        }
        SysEntityPo sysEntityPo = CompanyBaseInfoApiConverter.companyBaseInfo2Entity(companyBaseInfoApiDto);
        sysEntityPo.setCode(entityQuery.getCode());
        sysEntityPo.setCreateBy(SecurityUtils.getUserId());
        List<SysDictDataDto> regType = DictUtil.getDictCache("reg_type");
        List<SysDictDataDto> businessStatus = DictUtil.getDictCache("business_status");
        for (SysDictDataDto sysDictDataDto : regType) {
            if (sysDictDataDto.getLabel().contains(sysEntityPo.getRegType())) {
                sysEntityPo.setRegType(sysDictDataDto.getValue());
                break;
            }
        }
        for (SysDictDataDto sysDictDataDto : businessStatus) {
            if (sysDictDataDto.getLabel().contains(sysEntityPo.getBusinessStatus())) {
                sysEntityPo.setBusinessStatus(sysDictDataDto.getValue());
                break;
            }
        }
        if (sysEntityMapper.insertEntity(sysEntityPo) > 0) {
            sysEntityMapper.insertEntityTaxation(sysEntityPo);
            if (sysEntityPo.getBranchPoList().size() > 0) {
                sysEntityPo.getBranchPoList().forEach(sysEntitySaicBranchPo ->
                        sysEntitySaicBranchPo.setEntityId(sysEntityPo.getId())
                );
                sysEntitySaicBranchManager.insertBatch(sysEntityPo.getBranchPoList());
            }
            if (sysEntityPo.getEmployeePoList().size() > 0) {
                sysEntityPo.getEmployeePoList().forEach(employeePo ->
                        employeePo.setEntityId(sysEntityPo.getId())
                );
                sysEntitySaicEmployeeManager.insertBatch(sysEntityPo.getEmployeePoList());
            }
            if (sysEntityPo.getChangeRecordPoList().size() > 0) {
                sysEntityPo.getChangeRecordPoList().forEach(changeRecordPo ->
                        changeRecordPo.setEntityId(sysEntityPo.getId())
                );
                sysEntitySaicChangeRecordManager.insertBatch(sysEntityPo.getChangeRecordPoList());
            }
            if (sysEntityPo.getPartnerPoList().size() > 0) {
                sysEntityPo.getPartnerPoList().forEach(sysEntitySaicPartnerPo ->
                        sysEntitySaicPartnerPo.setEntityId(sysEntityPo.getId())
                );
                sysEntitySaicPartnerManager.insertBatch(sysEntityPo.getPartnerPoList());
            }
            sysEntityExceptionInfoService.entityAbnormalInfo(sysEntityPo);
            return AjaxResult.success();
        }


        return AjaxResult.error();
    }





    @Override
    public AjaxResult batchRemove(List<Long> idList) {
        return sysEntityMapper.batchRemove(idList) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public AjaxResult edit(String entityJson) {
        JSONObject jsonObject = JSON.parseObject(entityJson);
        String entityType = jsonObject.get(EntityTypeConstant.ENTITY_TYPE).toString();
        Long id = Long.parseLong(jsonObject.get("id").toString());
        //变更前实体信息
        SysEntityPo sysEntityPo = selectById(id);
        JSONObject beforeEntity = JSON.parseObject(JSON.toJSONString(sysEntityPo));
        List<SysEntityFieldDto> sysEntityFieldDtos = sysEntityFieldService.selectList(null);
        Map<String, List<SysEntityFieldDto>> collect = sysEntityFieldDtos.stream().collect(Collectors.groupingBy(SysEntityFieldPo::getFieldBelong));
        int flag = 0;
        String changeField = "";
        String before = "";
        String after = "";
        if (entityType.equals(EntityTypeConstant.SAIC)) {
            List<SysEntityFieldDto> sysEntityFieldSAIC = collect.get("1");
            if (collect.get("4") != null && collect.get("4").size() > 0) {
                sysEntityFieldSAIC.addAll(collect.get("4"));
            }
            JSONObject saicJson = new JSONObject();
            for (SysEntityFieldDto sysEntityFieldDto : sysEntityFieldSAIC) {
                if (sysEntityFieldDto.getIsChange() == NumberPool.One && jsonObject.get(sysEntityFieldDto.getFieldName()) != null) {
                    changeField = changeField + " " + sysEntityFieldDto.getFieldComment();
                    before = before + " " + sysEntityFieldDto.getFieldComment() + ":" +  beforeEntity.get(sysEntityFieldDto.getFieldName());
                    after = after + " " + sysEntityFieldDto.getFieldComment() + ":" + jsonObject.get(sysEntityFieldDto.getFieldName());
                    saicJson.put(sysEntityFieldDto.getFieldName(), jsonObject.get(sysEntityFieldDto.getFieldName()));
                }
            }
            flag = sysEntityMapper.updateSAIC(saicJson, id);
        }
        if (entityType.equals(EntityTypeConstant.TAXATION)) {
            List<SysEntityFieldDto> sysEntityFieldTaxation = collect.get("2");
            if (collect.get("3") != null && collect.get("3").size() > 0) {
                sysEntityFieldTaxation.addAll(collect.get("3"));
            }
            if (collect.get("5") != null && collect.get("5").size() > 0) {
                sysEntityFieldTaxation.addAll(collect.get("5"));
            }
            JSONObject taxationJson = new JSONObject();
            for (SysEntityFieldDto sysEntityFieldDto : sysEntityFieldTaxation) {
                if (sysEntityFieldDto.getIsChange() == NumberPool.One && jsonObject.get(sysEntityFieldDto.getFieldName()) != null) {
                    changeField = changeField + " " + sysEntityFieldDto.getFieldComment();
                    before = before + " " + sysEntityFieldDto.getFieldComment() + ":" +  beforeEntity.get(sysEntityFieldDto.getFieldName());
                    after = after + " " + sysEntityFieldDto.getFieldComment() + ":" + jsonObject.get(sysEntityFieldDto.getFieldName());
                    taxationJson.put(sysEntityFieldDto.getFieldName(), jsonObject.get(sysEntityFieldDto.getFieldName()));
                }
            }
            flag = sysEntityMapper.updateTaxation(taxationJson, id);
        }
        //发送站内信
        List<SysUserDto> user = userService.selectListByReception(MessageConstants.Reception.YES.getCode());
        if (!user.isEmpty()){
            SysMessageEntityChangeDto entityChange = MessageUtil.packDetails(id, changeField, before, after);
            messageEntityChangeMapper.insertEntityChange(entityChange);
            List<SysMessageDto> sysMessages = MessageUtil.packingMessage(sysEntityPo.getName(),
                    MessageConstants.type.ENTITY_CHANGES.getInfo(), MessageConstants.type.ENTITY_CHANGES.getCode(),
                    before, after, SecurityContextHolder.getUserId(), user,entityChange.getId());
            messageMapper.sendMessage(sysMessages);
        }
        return flag > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public AjaxResult option() {
        List<Map<String, Object>> list = sysEntityMapper.option();
        return AjaxResult.success(list);
    }

    @Override
    @Async
    public void importData(List<SysEntity> entityList, String UUID) {
        List<String> idByCreditNo = sysEntityMapper.getIdByCreditNo(entityList);
        String entityStr;
        if (idByCreditNo.size() > 0) {
            entityStr = String.join(",", idByCreditNo);
            entityList = entityList.stream().filter(sysEntity -> !entityStr.contains(sysEntity.getCreditNo())).collect(Collectors.toList());

        } else {
            entityStr = "";
        }
        try {
            for (SysEntity sysEntity : entityList) {
                SysEntityQuery entityQuery = new SysEntityQuery();
                entityQuery.setName(sysEntity.getName());
                entityQuery.setCreditNo(sysEntity.getCreditNo());
                entityQuery.setCode(sysEntity.getCode());
                this.add(entityQuery);
            }
            if (StringUtils.isEmpty(entityStr)) {
                redisService.setCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), UUID, NumberUtil.One);
            } else {
                redisService.setCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), UUID, "由于实体管理中存在相同的统一社会信用代码，excel中" + entityStr + "无法导入");
            }
        } catch (Exception e) {
            redisService.setCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), UUID, NumberUtil.Two);
        }
    }

    @Override
    public SysEntityPo[] drop() {
        return sysEntityMapper.drop();
    }

    /**
     *查询角色拥有的实体字段
     * @param roleId
     * @return
     */
    @Override
    public List<SysEntityFieldPo> selectRoleHasEntity(Long roleId) {

        List<Long> roleIds = new ArrayList<>();
        roleIds.add(roleId);
        return processEntityFields(roleIds);
    }

    /**
     * 查询用户对应角色的实体字段（用户可以有多个角色）
     *
     * @return
     */
    @Override
    public List<SysEntityFieldPo> selectUserRoleAllEntityFields() {

        //管理员可查看所有字段
        if (SecurityUtils.getUserType().equals("00")) {
            return sysEntityFieldMapper.selectList(null);
        }
        //查询用户对应角色
        SysUserDto user = SecurityUtils.getUser();
        Long userId = user.getId();
        QueryWrapper<SysOrganizeRoleMerge> qw = new QueryWrapper<>();
        qw.lambda().eq(SysOrganizeRoleMerge::getUserId, userId);
        List<SysOrganizeRoleMerge> sysOrganizeRoleMerges = sysOrganizeRoleMergeMapper.selectList(qw);
        if (sysOrganizeRoleMerges == null || sysOrganizeRoleMerges.size() == 0) {
            return new ArrayList<>();
        }
        List<Long> roleIds = sysOrganizeRoleMerges.stream().map(SysOrganizeRoleMerge::getRoleId).collect(Collectors.toList());
        return processEntityFields(roleIds);
    }

    //根据实体字段id查询实体字段对象
    public List<SysEntityFieldPo> processEntityFields(List<Long> roleIds) {
        QueryWrapper<SysRoleEntityFieldMerge> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SysRoleEntityFieldMerge::getRoleId, roleIds);
        List<SysRoleEntityFieldMerge> sysRoleEntityFieldMerges = sysRoleEntityFieldMergeMapper.selectList(queryWrapper);
        if (sysRoleEntityFieldMerges == null || sysRoleEntityFieldMerges.size() == 0) {
            return new ArrayList<>();
        }
        List<Long> entityIds = sysRoleEntityFieldMerges.stream().map(SysRoleEntityFieldMerge::getEntityFieldId).collect(Collectors.toList());
        List<SysEntityFieldDto> sysEntityFieldDtos = sysEntityFieldService.selectListByIds(entityIds);
        List<SysEntityFieldPo> sysEntityFieldPos = new ArrayList<>();
        for (SysEntityFieldDto sysEntityFieldDto : sysEntityFieldDtos) {
            SysEntityFieldPo po = new SysEntityFieldPo();
            BeanUtils.copyProperties(sysEntityFieldDto, po);
            sysEntityFieldPos.add(po);
        }
        return sysEntityFieldPos;
    }


    public SysEntityPo selectById(Long entityId) {
        return sysEntityMapper.selectById(entityId);
    }

    public List<SysEntityPo> findAllEntityIdAndName(Object query) {
        return sysEntityMapper.findAllEntityIdAndName(query);
    }



}