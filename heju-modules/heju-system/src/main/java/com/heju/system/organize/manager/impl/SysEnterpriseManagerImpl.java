package com.heju.system.organize.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import com.heju.system.api.organize.domain.model.SysEnterpriseConverter;
import com.heju.system.api.organize.domain.po.SysEnterprisePo;
import com.heju.system.api.organize.domain.query.SysEnterpriseQuery;
import com.heju.system.organize.manager.ISysEnterpriseManager;
import com.heju.system.organize.mapper.SysEnterpriseMapper;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 企业管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEnterpriseManagerImpl extends BaseManagerImpl<SysEnterpriseQuery, SysEnterpriseDto, <PERSON>ys<PERSON>nterprisePo, <PERSON>ys<PERSON>nterpriseMapper, SysEnterpriseConverter> implements ISysEnterpriseManager {

    /**
     * 根据名称查询状态正常企业对象
     *
     * @param name 名称
     * @return 企业对象
     */
    @Override
    public SysEnterpriseDto selectByName(String name) {
        SysEnterprisePo enterprise = baseMapper.selectOne(
                Wrappers.<SysEnterprisePo>query().lambda()
                        .eq(SysEnterprisePo::getName, name)
                        .eq(SysEnterprisePo::getStatus, BaseConstants.Status.NORMAL.getCode()));
        return mapperDto(enterprise);
    }

    @Override
    public Long getUserIdByTenant(String enterpriseName, String telephone,Long userId) {
        return baseMapper.getUserIdByTenant(enterpriseName, telephone,userId);
    }

    @Override
    public Long getUserIdByTenantUnionId(String enterpriseName, String unionId) {
        return baseMapper.getUserIdByTenantUnionId(enterpriseName, unionId);
    }

    @Override
    public String getIdByTelephone(String telephone) {
        return baseMapper.getIdByTelephone(telephone);
    }

    @Override
    public int insertByTelephone(String id,String telephone) {
        return baseMapper.insertByTelephone(id,telephone);
    }

    @Override
    public int insertTenantUser(Long tenantUserId, Long tenantId, String userId) {
        return baseMapper.insertTenantUser(tenantUserId,tenantId,userId);
    }
}
