package com.heju.system.report.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import com.heju.system.report.service.ISysReportManagementService;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 报表管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Management")
public class SysReportManagementController extends BaseController<SysReportManagementQuery, SysReportManagementDto, ISysReportManagementService> {
    
    @Autowired
    ISysReportManagementService reportManagementService;


    @Override
    protected String getNodeName() {
        return "报表管理";
    }

    /**
     * 查询公司报表类型
     */
    @GetMapping("/list")
    public AjaxResult reportQuery(SysReportManagementQuery query) {
        Map<String, Object> stringObjectMap = reportManagementService.selectListPage(query);
        return AjaxResult.success(stringObjectMap);
    }

    @GetMapping("/query")
    public AjaxResult conditionQuery(SysReportManagementQuery query){
        List<SysReportManagementQuery> end = reportManagementService.selectByCondition(query);
        return AjaxResult.success(end);
    }

}
