package com.heju.system.entity.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.query.SysEntityExamineQuery;
import com.heju.system.entity.service.ISysEntityExamineService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 实体信息审核管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/examine")
public class SysEntityExamineController extends BaseController<SysEntityExamineQuery, SysEntityExamineDto, ISysEntityExamineService> {

    @Resource
    private ISysEntityExamineService service;

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "实体信息审核";
    }

    /**
     * 查询实体信息审核列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ENTITY_EXAMINE_LIST)
    public AjaxResult list(SysEntityExamineQuery entityExamine) {
        startPage();
        List<SysEntityExamineDto> list = service.selectListScope(entityExamine);
        return getDataTable(list);
    }

    /**
     * 查询实体信息审核详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_ENTITY_EXAMINE_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 实体信息审核新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_ENTITY_EXAMINE_ADD)
    @Log(title = "实体信息审核管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntityExamineDto entityExamine) {
        return super.add(entityExamine);
    }


    /**
     * 实体信息审核修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_ENTITY_EXAMINE_EDIT, Auth.SYS_ENTITY_EXAMINE_ES}, logical = Logical.OR)
    @Log(title = "实体信息审核管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysEntityExamineDto entityExamine) {
        return service.editStatus(entityExamine);
    }

}
