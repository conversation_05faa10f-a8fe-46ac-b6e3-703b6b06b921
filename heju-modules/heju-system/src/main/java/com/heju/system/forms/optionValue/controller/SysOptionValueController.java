package com.heju.system.forms.optionValue.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import com.heju.system.forms.optionValue.service.ISysOptionValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 选项值管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/option/value")
public class SysOptionValueController extends BaseController<SysOptionValueQuery, SysOptionValueDto, ISysOptionValueService> {

    @Autowired
    private ISysOptionValueService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "选项值" ;
    }

    /**
     * 查询选项值列表
     */
    @Override
    @GetMapping("/list")
    public AjaxResult list(SysOptionValueQuery optionValue) {
        List<SysOptionValueDto> list = baseService.selectListScope(optionValue);
        return getDataTable(list);
    }

    /**
     * 查询选项值详细
     */
    @Override
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 选项值新增
     */
    @Override
    @PostMapping
    @Log(title = "选项值管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysOptionValueDto optionValue) {
        return super.add(optionValue);
    }

    /**
     * 选项值修改
     */
    @Override
    @PutMapping
    @Log(title = "选项值管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysOptionValueDto optionValue) {
        return super.edit(optionValue);
    }

    /**
     * 选项值批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @Log(title = "选项值管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取选项值选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 通过选项查选项值
     */
    @GetMapping("/getValueList")
    public AjaxResult getValueList(SysOptionValueQuery optionValue) {
        return AjaxResult.success(service.getValueListByOptionId(optionValue));
    }


    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysOptionValueDto optionValue) {
        if (baseService.checkConfigCodeUnique(optionValue.getId(), optionValue.getName()))
            warn(StrUtil.format("{}{}失败,{}名称已存在", operate.getInfo(), getNodeName(), optionValue.getName()));
    }
}
