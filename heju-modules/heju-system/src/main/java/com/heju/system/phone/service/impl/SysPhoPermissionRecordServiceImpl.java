package com.heju.system.phone.service.impl;

import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.organize.manager.ISysUserManager;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.system.phone.service.ISysPhoPermissionRecordService;
import com.heju.system.phone.manager.ISysPhoPermissionRecordManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 手机号授权管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysPhoPermissionRecordServiceImpl extends BaseServiceImpl<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto, ISysPhoPermissionRecordManager> implements ISysPhoPermissionRecordService {

    @Autowired
    private ISysPhoPermissionRecordManager manager;

    @Autowired
    private ISysUserManager userManager;


    /**
     * 查询手机号授权对象列表 | 数据权限
     *
     * @param phoPermissionRecord 手机号授权对象
     * @return 手机号授权对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysPhoPermissionRecordMapper"})
    public List<SysPhoPermissionRecordDto> selectListScope(SysPhoPermissionRecordQuery phoPermissionRecord) {
        List<SysPhoPermissionRecordDto> recordDtos = baseManager.selectPermList(phoPermissionRecord);
        if (recordDtos == null || recordDtos.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> borrowUsers = recordDtos.stream().map(SysPhoPermissionRecordDto::getPermissionerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<SysUserDto> sysUserDtos = userManager.selectListByIds(borrowUsers);
        Map<Long, String> userMap = sysUserDtos.stream().collect(Collectors.toMap(
                SysUserDto::getId,
                SysUserDto::getNickName,
                (existing, replacement) -> existing
        ));
        for (SysPhoPermissionRecordDto dto : recordDtos) {
            if (dto.getPermissionerId() != null ) {
                dto.setNickName(userMap.get(dto.getPermissionerId()));
            }
        }
        return recordDtos;
    }

    /**
     * 手机授权记录列表查询
     * @param permissionRecord
     * @return
     */
    @Override
    public List<SysPhoPermissionRecordDto> overTimeList(SysPhoPermissionRecordQuery permissionRecord) {
        List<SysPhoPermissionRecordDto> permissionRecordDtos = manager.selectOverTimeList(permissionRecord);
        if (permissionRecordDtos == null || permissionRecordDtos.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> permUsers = permissionRecordDtos.stream().map(SysPhoPermissionRecordDto::getPermissionerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<SysUserDto> sysUserDtos = userManager.selectListByIds(permUsers);
        Map<Long, String> userMap = sysUserDtos.stream().collect(Collectors.toMap(
                SysUserDto::getId,
                SysUserDto::getNickName,
                (existing, replacement) -> existing
        ));
        for (SysPhoPermissionRecordDto dto : permissionRecordDtos) {
            if (dto.getPermissionerId() != null ) {
                dto.setNickName(userMap.get(dto.getPermissionerId()));
            }
        }
        return permissionRecordDtos;
    }


    /**
     * 批量新增、修改
     * @param permissionRecords
     * @return
     */
    @Override
    public int batchOperation(List<SysPhoPermissionRecordDto> permissionRecords) {
        if (permissionRecords == null || permissionRecords.isEmpty()) {
            throw new IllegalArgumentException("授权信息不能为空");
        }
        //分成两个List
        List<SysPhoPermissionRecordDto> toInsert = new ArrayList<>();
        List<SysPhoPermissionRecordDto> toUpdate = new ArrayList<>();
        // 使用 Set 来保存已添加的 borrowUserId
        Set<Long> permUserSet = new HashSet<>();
        for (SysPhoPermissionRecordDto record : permissionRecords) {
            if (record.getId() == null) {
                //字段校验
                if (record.getPermissionerId() == null || record.getPhoneNumber() == null) {
                    throw new IllegalArgumentException("授权人及授权手机号不能为空");
                }
                if (record.getEndTime().isBefore(record.getStartTime()) || record.getEndTime().equals(record.getStartTime())) {
                    throw new IllegalArgumentException("请正确填写开始、结束时间顺序");
                }
                if (permUserSet.contains(record.getPermissionerId())) {
                    throw new IllegalArgumentException("不允许插入重复授权记录");
                } else {
                    permUserSet.add(record.getPermissionerId());
                    toInsert.add(record);
                }
            } else {
                toUpdate.add(record);
            }
        }
        //受影响行
        int count = 0;
        //批量插入
        if (!toInsert.isEmpty()) {
            count += manager.insertBatch(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            count += manager.updateBatch(toUpdate);
        }
        return count;
    }
}
