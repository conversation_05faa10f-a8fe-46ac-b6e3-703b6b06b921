package com.heju.system.report.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.query.SysFinanceReportQuery;
import com.heju.system.report.service.ISysFinanceReportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 财税报表管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/finance")
public class SysFinanceReportController extends BaseController<SysFinanceReportQuery, SysFinanceReportDto, ISysFinanceReportService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "财税报表" ;
    }

    /**
     * 查询财税报表列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FINANCE_REPORT_LIST)
    public AjaxResult list(SysFinanceReportQuery financeReport) {
        return super.list(financeReport);
    }

    /**
     * 查询财税报表详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FINANCE_REPORT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 财税报表新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FINANCE_REPORT_ADD)
    @Log(title = "财税报表管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFinanceReportDto financeReport) {
        return super.add(financeReport);
    }

    /**
     * 财税报表修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FINANCE_REPORT_EDIT)
    @Log(title = "财税报表管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFinanceReportDto financeReport) {
        return super.edit(financeReport);
    }

    /**
     * 财税报表修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FINANCE_REPORT_EDIT, Auth.SYS_FINANCE_REPORT_ES}, logical = Logical.OR)
    @Log(title = "财税报表管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFinanceReportDto financeReport) {
        return super.editStatus(financeReport);
    }

    /**
     * 财税报表批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FINANCE_REPORT_DEL)
    @Log(title = "财税报表管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取财税报表选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


}
