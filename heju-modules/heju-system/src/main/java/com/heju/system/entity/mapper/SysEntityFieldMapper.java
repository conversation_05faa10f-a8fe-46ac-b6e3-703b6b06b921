package com.heju.system.entity.mapper;

import com.github.pagehelper.Page;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.query.SysEntityFieldQuery;

/**
 * 实体字段管理管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityFieldMapper extends BaseMapper<SysEntityFieldQuery, SysEntityFieldDto, SysEntityFieldPo> {

//    Page<SysEntityFieldPo> queryPage(String fieldComment, Long roleId, String fieldType, String fieldBelong);
}