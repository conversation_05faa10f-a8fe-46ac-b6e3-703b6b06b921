package com.heju.system.utils;

import com.alibaba.fastjson.JSONArray;
import com.heju.system.third.domain.dto.ThirdOrganizeDto;
import com.heju.system.third.domain.dto.ThirdOrganizeOneDto;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class ThirdOrganizeUtil {


    /**
     * 多条组织同步谷云
     * @param organizeDtoList 谷云传参
     */
    public static void sendThirdOrganize(List<ThirdOrganizeDto> organizeDtoList) {
//        JSONArray jsonArray = new JSONArray();
//        jsonArray.addAll(organizeDtoList);
//        String a=jsonArray.toJSONString();
//        System.out.println(a);
        System.out.println(organizeDtoList);
//        httpPost(RestCloudUrlEnum.ORGANIZE_TOTAL.getCode(), organizeDtoList);
    }


    /**
     * 单条谷云组织同步
     * @param organizeDtoList 谷云传参
     */
    public static void sendThirdOrganizeOne(List<ThirdOrganizeOneDto> organizeDtoList) {
//        JSONArray jsonArray = new JSONArray();
//        jsonArray.addAll(organizeDtoList);
//        String a=jsonArray.toJSONString();
        System.out.println(organizeDtoList);
//        String result=httpPost(RestCloudUrlEnum.ORGANIZE_ONE.getCode(), organizeDtoList);
    }

    public static String httpGet(String url){
        RestTemplate restTemplate=new RestTemplate();
        String result=restTemplate.exchange(url, HttpMethod.GET,null,String.class).getBody();
        return result;
    }

    public static String httpPost(String url, List<?> data){
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<List<?>> listHttpEntity = new HttpEntity<>(data, headers);
        RestTemplate restTemplate=new RestTemplate();
        return restTemplate.postForEntity(url, listHttpEntity, String.class).getBody();
    }

}
