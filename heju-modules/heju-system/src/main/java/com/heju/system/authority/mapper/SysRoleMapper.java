package com.heju.system.authority.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.api.authority.domain.query.SysRoleQuery;

/**
 * 岗位管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysRoleMapper extends BaseMapper<SysRoleQuery, SysRoleDto, SysRolePo> {
}
