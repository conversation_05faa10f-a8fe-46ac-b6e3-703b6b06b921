package com.heju.system.notice.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.notice.domain.dto.SysNoticeDto;
import com.heju.system.notice.domain.po.SysNoticePo;
import com.heju.system.notice.domain.query.SysNoticeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 通知公告 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysNoticeConverter extends BaseConverter<SysNoticeQuery, SysNoticeDto, SysNoticePo> {
}
