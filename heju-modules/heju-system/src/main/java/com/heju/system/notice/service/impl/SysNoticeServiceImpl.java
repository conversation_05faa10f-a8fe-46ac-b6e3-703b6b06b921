package com.heju.system.notice.service.impl;

import com.heju.common.datascope.annotation.DataScope;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.notice.domain.dto.SysNoticeDto;
import com.heju.system.notice.domain.query.SysNoticeQuery;
import com.heju.system.notice.manager.ISysNoticeManager;
import com.heju.system.notice.service.ISysNoticeService;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.heju.common.core.constant.basic.SecurityConstants.CREATE_BY;

/**
 * 通知公告管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl extends BaseServiceImpl<SysNoticeQuery, SysNoticeDto, ISysNoticeManager> implements ISysNoticeService {

    /**
     * 查询通知公告对象列表 | 数据权限
     *
     * @param notice 通知公告对象
     * @return 通知公告对象集合
     */
    @Override
    @DataScope(userAlias = CREATE_BY, mapperScope = {"SysNoticeMapper"})
    public List<SysNoticeDto> selectListScope(SysNoticeQuery notice) {
        return baseManager.selectList(notice);
    }
}