package com.heju.system.report.mapper;

import com.heju.system.report.domain.query.SysBillReportQuery;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.po.SysBillReportPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 发票管理详情管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysBillReportMapper extends BaseMapper<SysBillReportQuery, SysBillReportDto, SysBillReportPo> {
    @Select("select * from sys_bill_report where del_flag = '0'" +
            "and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)")
    List<SysBillReportDto> findAll(SysReportManagementQuery query);

    @Select("select * from sys_bill_report where del_flag = '0'" +
            "and (case when #{reporttypeType} is not null then bill_type = #{reporttypeType} else 1=1 end)" +
            "and (case when #{reporttimeType} is not null then reporttime_type = #{reporttimeType} else 1=1 end)" +
            "and (case when #{year} is not null then year = #{year} else 1=1 end)" +
            "and entity_id = #{entityId}")
    List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query);
}