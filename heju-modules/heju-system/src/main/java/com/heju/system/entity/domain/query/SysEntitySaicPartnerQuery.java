package com.heju.system.entity.domain.query;

import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体工商股东 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntitySaicPartnerQuery extends SysEntitySaicPartnerPo {

    @Serial
    private static final long serialVersionUID = 1L;
}