package com.heju.system.forms.optionValue.mapper;

import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;

/**
 * 选项值管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysOptionValueMapper extends BaseMapper<SysOptionValueQuery, SysOptionValueDto, SysOptionValuePo> {
}