package com.heju.system.forms.busApply.service.impl;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import com.heju.system.forms.busApply.domain.query.SysApplyRecordQuery;
import com.heju.system.forms.busApply.domain.query.UniversalApplyQuery;
import com.heju.system.forms.busApply.mapper.SysApplyRecordMapper;
import com.heju.system.forms.busApply.service.ISysApplyRecordService;
import com.heju.system.forms.busApply.manager.ISysApplyRecordManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.merge.SysFieldRoleMerge;
import com.heju.system.forms.field.mapper.merge.SysFieldRoleMergeMapper;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.service.ISysSheetService;
import com.heju.system.forms.universal.mapper.UniversalMapper;
import com.heju.system.organize.mapper.SysUserMapper;
import com.heju.system.utils.FieldTypeConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 行政申请管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysApplyRecordServiceImpl extends BaseServiceImpl<SysApplyRecordQuery, SysApplyRecordDto, ISysApplyRecordManager> implements ISysApplyRecordService {
    @Resource
    private ISysSheetService sheetService;

    @Resource
    private ISysFieldService fieldService;

    @Resource
    private SysFieldRoleMergeMapper fieldRoleMergeMapper;

    @Resource
    private SysApplyRecordMapper mapper;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    @Resource
    private UniversalMapper universalMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private ISysCascadeManager sysCascadeManager;

    /**
     * 查询行政申请对象列表 | 数据权限
     *
     * @param applyRecord 行政申请对象
     * @return 行政申请对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysApplyRecordMapper"})
    public List<SysApplyRecordDto> selectListScope(SysApplyRecordQuery applyRecord) {
        return baseManager.selectList(applyRecord);
    }

    /**
     * 查印章申请列表字段
     * 显示所有字段   (后通过状态设置是否有值)
     * @param query
     * @return
     */
    @Override
    public AjaxResult getSealApplyField(UniversalApplyQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if (sysSheetDto == null){
            return null;
        }
        List<Long> sheetIds = new ArrayList<>();
        sheetIds.add(sysSheetDto.getId());
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        return AjaxResult.success(sysFieldDtos);
    }

    @Override
    public AjaxResult getSealApplyList(UniversalApplyQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if (sysSheetDto == null){
            return AjaxResult.error("业务表单未找到");
        }
        Long sheetId = sysSheetDto.getId();
        List<Long> sheetIds = new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);

        String searchField = String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).map(name -> query.getSheetApiName() + "." + name).toList());
        String selectSql = "select " + searchField;
        String sheetApiName = query.getSheetApiName();
        query.setPage((query.getPage() - 1) * query.getPageSize());
        query.setOrderSort(StrUtil.toUnderlineCase(query.getOrderSort()));
        query.setApplyPersonId(SecurityUtils.getUser().getId());
        List<Map<String, Object>> sealApplyList = mapper.selectApplyList(selectSql, sheetApiName, query);

        //过滤出文件类型的字段
        List<String> apiNames = sysFieldDtos.stream().
                filter(dto -> FieldTypeConstants.FILE.equals(dto.getFieldType())).
                map(SysFieldDto::getApiName).
                filter(Objects::nonNull).collect(Collectors.toList());
        for (Map<String, Object> record : sealApplyList) {
            String applyStatus = (String) record.get("apply_status");
            if (!"0".equals(applyStatus)) {
                for (String apiName : apiNames) {
                    record.put(apiName,"");
                }
            }
        }

        //根据字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        //级联
        List<SysFieldDto> cascadeList = new ArrayList<>(
                Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE))
                        .orElse(Collections.emptyList())
        );
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectSingleSheetList = new ArrayList<>();
        List<SysFieldDto> selectMultiSheetList = new ArrayList<>();
        //循环单选，区分业务选项和其他类型选项
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSingleSheetList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        //循环多，区分业务选项和其他类型选项
        List<SysFieldDto> multiFIelds = groupFieldType.get(FieldTypeConstants.SELECT_MULTI);
        if (multiFIelds != null) {
            for (SysFieldDto sysFieldDto : multiFIelds) {
                if(Objects.equals(sysFieldDto.getOptionType(),3)){
                    selectMultiSheetList.add(sysFieldDto);
                }else{
                    selectMultiList.add(sysFieldDto);
                }
            }
        }
        Set<Long> optionIdList=new HashSet<>();
        if(selectSingleList.size()>0){
            optionIdList.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if(selectMultiList.size()>0){
            optionIdList.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }

        Set<Long> sheetIdList=new HashSet<>();
        if(selectSingleSheetList.size()>0){
            sheetIdList.addAll(selectSingleSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if(selectMultiSheetList.size()>0){
            sheetIdList.addAll(selectMultiSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Map<Long, String> optionValueMap=new HashMap<>();
        Map<Long,Map<Long, String>> sheetValueMap=new HashMap<>();
        if(optionIdList.size()>0){
            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
            lqw.in(SysOptionValuePo::getOptionId,optionIdList);
            List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
            optionValueMap = sysOptionValuePoList.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if(sheetIdList.size()>0){
            List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(sheetIdList);
            for (SysSheetDto sheetDto : sheetDtoList) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        if (sealApplyList != null && !sealApplyList.isEmpty())
            for (Map<String, Object> stringObjectMap : sealApplyList) {
                for (SysFieldDto fieldDto : selectSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(),optionValueMap.get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue=new ArrayList<>();
                    for (String s : split) {
                        Long a=Long.parseLong(s);
                        multiValue.add(optionValueMap.get(a));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join(",",multiValue));
                }
                for (SysFieldDto fieldDto : selectSingleSheetList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(),sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiSheetList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue=new ArrayList<>();
                    for (String s : split) {
                        Long a=Long.parseLong(s);
                        multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(a));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join(",",multiValue));
                }
                for (SysFieldDto fieldDto : cascadeList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> fieldValueList = Arrays.stream(fieldValue.split("-")).toList();
                    LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                    lqw = Wrappers.lambdaQuery();
                    lqw.in(SysOptionValuePo::getId,fieldValueList);
                    List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(lqw);
                    Map<Long, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                    List<String> optionValueList=new ArrayList<>();
                    for (String s : fieldValueList) {
                        Long id = Long.valueOf(s);
                        optionValueList.add(stringMap.get(id));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join("-",optionValueList));
                }
            }
        Map<String, Object> resultMap = new HashMap<>();
        if (sealApplyList != null && !sealApplyList.isEmpty()){
            resultMap.put("sealApplyList", sealApplyList);
        }
        resultMap.put("total", mapper.countApplyList(query));
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    /**
     * 印章申请新增字段
     */
    @Override
    public AjaxResult getAddField(UniversalApplyQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if (sysSheetDto == null){
            return null;
        }
        List<Long> sheetIds = new ArrayList<>();
        sheetIds.add(sysSheetDto.getId());
//        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        List<SysFieldDto> sysFieldDtos = fieldService.selectBySheetIds(sheetIds);
        List<SysFieldDto> sysFilterField = sysFieldDtos.stream().
                filter(dto -> !(FieldTypeConstants.FILE.equals(dto.getFieldType()) ||
                        FieldTypeConstants.QUOTE.equals(dto.getFieldType()) ||
                        "credit_no".equals(dto.getApiName()))).
                collect(Collectors.toList());
        return AjaxResult.success(sysFilterField);
    }

    @Override
    public AjaxResult getAddList(UniversalApplyQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if (sysSheetDto == null){
            return AjaxResult.error("业务表单未找到");
        }
        Long sheetId = sysSheetDto.getId();
        List<Long> sheetIds = new ArrayList<>();
        sheetIds.add(sheetId);
//        List<SysFieldDto> sysOriginFields = getFieldByRoleIds(she\etIds);
        List<SysFieldDto> sysOriginFields = fieldService.selectBySheetIds(sheetIds);
        List<SysFieldDto> sysFieldDtos = sysOriginFields.stream().
                filter(dto -> !(FieldTypeConstants.FILE.equals(dto.getFieldType()) ||
                        FieldTypeConstants.QUOTE.equals(dto.getFieldType()))).
                collect(Collectors.toList());

        String searchField = String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).map(name -> query.getSheetApiName() + "." + name).toList());
        String selectSql = "select " + searchField;
        String sheetApiName = query.getSheetApiName();
//        query.setPage((query.getPage() - 1) * query.getPageSize());
        query.setOrderSort(StrUtil.toUnderlineCase(query.getOrderSort()));
        query.setApplyPersonId(SecurityUtils.getUser().getId());
        List<Map<String, Object>> sealApplyList = mapper.selectAddList(selectSql, sheetApiName, query);
        //带着sheetApiName和当前登录人去申请表中查询  applyStatus为"2","0"记录
        //该用户在该印章下通过或审核中的印章id
        Set<Long> businessIds = mapper.selectApplied(query);
        //将sealApplyList中id为businessIds中的值过滤掉不返回
        List<Map<String, Object>> sealAddList = sealApplyList.stream().filter(record -> {
            Long id = (Long) record.get("id");
            return !businessIds.contains(id);
        }).collect(Collectors.toList());

        //根据字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        //级联
        List<SysFieldDto> cascadeList = new ArrayList<>(
                Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE))
                        .orElse(Collections.emptyList())
        );
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectSingleSheetList = new ArrayList<>();
        List<SysFieldDto> selectMultiSheetList = new ArrayList<>();
        //循环单选，区分业务选项和其他类型选项
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSingleSheetList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        //循环多，区分业务选项和其他类型选项
        List<SysFieldDto> multiFIelds = groupFieldType.get(FieldTypeConstants.SELECT_MULTI);
        if (multiFIelds != null) {
            for (SysFieldDto sysFieldDto : multiFIelds) {
                if(Objects.equals(sysFieldDto.getOptionType(),3)){
                    selectMultiSheetList.add(sysFieldDto);
                }else{
                    selectMultiList.add(sysFieldDto);
                }
            }
        }
        Set<Long> optionIdList=new HashSet<>();
        if(selectSingleList.size()>0){
            optionIdList.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if(selectMultiList.size()>0){
            optionIdList.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }

        Set<Long> sheetIdList=new HashSet<>();
        if(selectSingleSheetList.size()>0){
            sheetIdList.addAll(selectSingleSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if(selectMultiSheetList.size()>0){
            sheetIdList.addAll(selectMultiSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Map<Long, String> optionValueMap=new HashMap<>();
        Map<Long,Map<Long, String>> sheetValueMap=new HashMap<>();
        if(optionIdList.size()>0){
            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
            lqw.in(SysOptionValuePo::getOptionId,optionIdList);
            List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
            optionValueMap = sysOptionValuePoList.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if(sheetIdList.size()>0){
            List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(sheetIdList);
            for (SysSheetDto sheetDto : sheetDtoList) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        if (sealAddList != null && !sealAddList.isEmpty())
            for (Map<String, Object> stringObjectMap : sealAddList) {
                for (SysFieldDto fieldDto : selectSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(),optionValueMap.get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue=new ArrayList<>();
                    for (String s : split) {
                        Long a=Long.parseLong(s);
                        multiValue.add(optionValueMap.get(a));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join(",",multiValue));
                }
                for (SysFieldDto fieldDto : selectSingleSheetList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(),sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiSheetList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue=new ArrayList<>();
                    for (String s : split) {
                        Long a=Long.parseLong(s);
                        multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(a));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join(",",multiValue));
                }
                for (SysFieldDto fieldDto : cascadeList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> fieldValueList = Arrays.stream(fieldValue.split("-")).toList();
                    LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                    lqw = Wrappers.lambdaQuery();
                    lqw.in(SysOptionValuePo::getId,fieldValueList);
                    List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(lqw);
                    Map<Long, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                    List<String> optionValueList=new ArrayList<>();
                    for (String s : fieldValueList) {
                        Long id = Long.valueOf(s);
                        optionValueList.add(stringMap.get(id));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join("-",optionValueList));
                }
            }
        Map<String, Object> resultMap = new HashMap<>();
        if (sealAddList != null && !sealAddList.isEmpty()){
            resultMap.put("sealAddList", sealAddList);
        }
//        resultMap.put("total", universalMapper.countList(query.getSheetApiName()));
//        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    /**
     * 根据表单id获取可见字段列表
     * @param sheetIds 表单id集合
     * @return 字段列表
     */
    public List<SysFieldDto> getFieldByRoleIds(List<Long> sheetIds){
        //查询角色可见字段
        List<SysFieldDto> sysFieldPoList;
        if(SecurityUtils.getUser().isAdmin()){
            sysFieldPoList=fieldService.selectBySheetIds(sheetIds);
        }else {
            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
            lqw.in(SysFieldRoleMerge::getSheetId, sheetIds);
            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
            sysFieldPoList= fieldService.selectListByIds(fieldIds);
        }
        return sysFieldPoList;
    }

    public AjaxResult editApplyStatus(SysApplyRecordDto applyRecord) {
        UpdateWrapper<SysApplyRecordPo> updateWrapper = new UpdateWrapper<>();
        // 判空
        if (applyRecord.getId() != null) {
            updateWrapper.eq("id", applyRecord.getId());
        }
        if (Objects.nonNull(applyRecord.getApplyStatus())) {
            updateWrapper.set("apply_status", applyRecord.getApplyStatus());
        }
        if (Objects.nonNull(applyRecord.getRejectReason())) {
            updateWrapper.set("reject_reason", applyRecord.getRejectReason());
        }
        // 注意：第一个参数为null，MyBatis Plus会忽略实体对象，只根据UpdateWrapper来更新
        int rows = mapper.update(null, updateWrapper);
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    public AjaxResult universalList(UniversalApplyQuery query) {
        // 查询表单 id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if (sysSheetDto == null) {
            return AjaxResult.error("错误的业务表单");
        }
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(Collections.singletonList(sysSheetDto.getId()));
        //根据字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectSheetMultiList = new ArrayList<>();
        List<SysFieldDto> selectsheetSingleList = new ArrayList<>();
        List<SysFieldDto> cascadeList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE))
                .orElse(Collections.emptyList()));
        List<SysFieldDto> dateList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.DATE))
                .orElse(Collections.emptyList()));
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectsheetSingleList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_MULTI))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSheetMultiList.add(sysFieldDto);
                    } else {
                        selectMultiList.add(sysFieldDto);
                    }
                });
        Set<Long> optionIds = new HashSet<>();
        if (!selectSingleList.isEmpty()) {
            optionIds.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectMultiList.isEmpty()) {
            optionIds.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Set<Long> sheetOptionIds = new HashSet<>();
        if (!selectsheetSingleList.isEmpty()) {
            sheetOptionIds.addAll(selectsheetSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectSheetMultiList.isEmpty()) {
            sheetOptionIds.addAll(selectSheetMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        // 查选项 映射为 id -> name
        Map<Long, String> optionValueMap = new HashMap<>();
        Map<Long, Map<Long, String>> sheetValueMap = new HashMap<>();
        LambdaQueryWrapper<SysOptionValuePo> wrapper = Wrappers.lambdaQuery();
        if (!optionIds.isEmpty()) {
            wrapper.in(SysOptionValuePo::getOptionId, optionIds);
            List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(wrapper);
            optionValueMap = optionValueList.stream()
                    .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if (!sheetOptionIds.isEmpty()) {
            List<SysSheetDto> sheetDtos = sheetService.selectListByIds(sheetOptionIds);
            for (SysSheetDto sheetDto : sheetDtos) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        // 执行查询
        query.setPage((query.getPage() - 1) * query.getPageSize());
        query.setOrderSort(cn.hutool.core.util.StrUtil.toUnderlineCase(query.getOrderSort()));
        List<Map<String, Object>> list = mapper.selectListWithQuote(query.getSheetApiName(), query);
        // 处理返回结果
        for (Map<String, Object> stringObjectMap : list) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> relatedItems = (List<Map<String, Object>>) stringObjectMap.get("relatedItems");
            // 创建一个 relatedItems 的新列表，防止对原始数据的修改
            List<Map<String, Object>> newRelatedItems = new ArrayList<>();
            if (relatedItems != null && !relatedItems.isEmpty()) {
                for (Map<String, Object> relatedItem : relatedItems) {
                    // 对每个 relatedItem，也创建一个副本，防止修改原始 Map
                    Map<String, Object> processedItem = new HashMap<>(relatedItem);
                    for (SysFieldDto sysFieldDto : selectSingleList) {
                        Long fieldValue = Long.parseLong(processedItem.get(sysFieldDto.getApiName()).toString());
                        processedItem.put(sysFieldDto.getApiName(), optionValueMap.get(fieldValue));
                    }
                    for (SysFieldDto sysFieldDto : selectMultiList) {
                        String fieldValue = processedItem.get(sysFieldDto.getApiName()).toString();
                        List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                        List<String> multiValue = new ArrayList<>();
                        for (String s : split) {
                            Long key = Long.parseLong(s);
                            multiValue.add(optionValueMap.get(key));
                        }
                        processedItem.put(sysFieldDto.getApiName(), String.join(",", multiValue));
                    }
                    for (SysFieldDto fieldDto : selectsheetSingleList) {
                        Long fieldValue = Long.parseLong(processedItem.get(fieldDto.getApiName()).toString());
                        processedItem.put(fieldDto.getApiName(),sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                    }
                    for (SysFieldDto fieldDto : selectSheetMultiList) {
                        String fieldValue = processedItem.get(fieldDto.getApiName()).toString();
                        List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                        List<String> multiValue=new ArrayList<>();
                        for (String s : split) {
                            Long a=Long.parseLong(s);
                            multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(a));
                        }
                        processedItem.put(fieldDto.getApiName(),String.join(",",multiValue));
                    }
                    for (SysFieldDto sysFieldDto : cascadeList) {
                        String fieldValue = processedItem.get(sysFieldDto.getApiName()).toString();
                        List<String> split = Arrays.stream(fieldValue.split("-")).toList();
                        LambdaQueryWrapper<SysOptionValuePo> queryWrapper = Wrappers.lambdaQuery();
                        queryWrapper.in(SysOptionValuePo::getId, split);
                        List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(queryWrapper);
                        Map<Long, String> cascadeMap = sysOptionValuePos.stream()
                                .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                        List<String> cascadeValue = new ArrayList<>();
                        for (String s : split) {
                            Long id = Long.valueOf(s);
                            cascadeValue.add(cascadeMap.get(id));
                        }
                        processedItem.put(sysFieldDto.getApiName(), String.join("-", cascadeValue));
                    }
                    // 将日期类型 转 字符串 ，防止序列化 变成 时间戳
                    for (SysFieldDto sysFieldDto : dateList) {
                        String fieldValue = processedItem.get(sysFieldDto.getApiName()).toString();
                        processedItem.put(sysFieldDto.getApiName(), fieldValue);
                    }
                    newRelatedItems.add(processedItem);
                }
            }
            stringObjectMap.put("relatedItems", newRelatedItems);
        }
        Map<String, Object> resultMap = new HashMap<>();
        if (!list.isEmpty()) { resultMap.put("list", list); }
        resultMap.put("total", mapper.countList(query.getSheetApiName()));
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    public AjaxResult manageList(UniversalApplyQuery query) {
        // 传入 id, apiName
        String apiName = query.getSheetApiName();
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(apiName);
        if (sysSheetDto == null) { return AjaxResult.error("错误的业务表单"); }
        // 查询角色可见字段 并过滤 文件类型 字段
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(Collections.singletonList(sysSheetDto.getId()))
                .stream().filter(sysFieldDto -> !sysFieldDto.getFieldType().equals(FieldTypeConstants.FILE)).toList();
        // 按字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = fieldByRoleIds.stream().collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        List<SysFieldDto> cascadeList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE)).orElse(Collections.emptyList()));
        List<SysFieldDto> dateList = new ArrayList<>(Optional.ofNullable(groupFieldType.get(FieldTypeConstants.DATE)).orElse(Collections.emptyList()));
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectsheetSingleList = new ArrayList<>();
        List<SysFieldDto> selectSheetMultiList = new ArrayList<>();
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectsheetSingleList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_MULTI))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSheetMultiList.add(sysFieldDto);
                    } else {
                        selectMultiList.add(sysFieldDto);
                    }
                });
        // 去重
        Set<Long> optionIds = new HashSet<>();
        if (!selectSingleList.isEmpty()) {
            optionIds.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectMultiList.isEmpty()) {
            optionIds.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Set<Long> sheetOptionIds = new HashSet<>();
        if (!selectsheetSingleList.isEmpty()) {
            sheetOptionIds.addAll(selectsheetSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectSheetMultiList.isEmpty()) {
            sheetOptionIds.addAll(selectSheetMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Map<Long, String> optionValueMap = new HashMap<>();
        Map<Long, Map<Long, String>> sheetValueMap = new HashMap<>();
        if (!optionIds.isEmpty()) {
            LambdaQueryWrapper<SysOptionValuePo> wrapper = Wrappers.lambdaQuery();
            wrapper.in(SysOptionValuePo::getOptionId, optionIds);
            List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(wrapper);
            optionValueMap = optionValueList.stream()
                    .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if (!sheetOptionIds.isEmpty()) {
            List<SysSheetDto> sheetDtos = sheetService.selectListByIds(sheetOptionIds);
            for (SysSheetDto sheetDto : sheetDtos) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }

        // 拼接 sql
        String selectSql = String.join(",", fieldByRoleIds.stream().map(SysFieldDto::getApiName)
                .filter(Objects::nonNull)
                .map(name -> query.getSheetApiName() + "." + name)
                .toList());
        // 拼接 sys_apply_record 字段. 防止乱序
        String appFields = Stream.of( "business_id", "api_name", "apply_time", "apply_reason", "reject_reason", "sort", "apply_status", "remark", "create_by", "create_time")
                .map(name -> "app." +name).collect(Collectors.joining(","));
        String finalSelectSql = selectSql + "," + appFields;
        // 执行查询
        query.setPage((query.getPage() - 1) * query.getPageSize());
        List<Map<String, Object>> manageList = mapper.selectManageList(query.getSheetApiName(), finalSelectSql, query);
        // 处理返回结果
        // 检查 create_by 不为空，取出 create_by
        manageList.stream().filter(item -> item.get("create_by") != null).forEach(item -> {
            Long createBy = Long.parseLong(item.get("create_by").toString());
            SysUserPo sysUserPo = userMapper.selectById(createBy);
            item.put("create_by", sysUserPo != null ? sysUserPo.getNickName() : "");
        });

        if (!manageList.isEmpty()) {
            for (Map<String, Object> stringObjectMap : manageList) {
                for (SysFieldDto fieldDto : selectSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(), optionValueMap.get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> list = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue = new ArrayList<>();
                    for (String s : list) {
                        Long key = Long.parseLong(s);
                        multiValue.add(optionValueMap.get(key));
                    }
                    stringObjectMap.put(fieldDto.getApiName(), String.join(",", multiValue));
                }
                for (SysFieldDto fieldDto : selectsheetSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(), sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectSheetMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> list = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue = new ArrayList<>();
                    for (String s : list) {
                        Long key = Long.parseLong(s);
                        multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(key));
                    }
                    stringObjectMap.put(fieldDto.getApiName(), String.join(",", multiValue));
                }
                for (SysFieldDto fieldDto : cascadeList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split("-")).toList();
                    LambdaQueryWrapper<SysOptionValuePo> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(SysOptionValuePo::getId, split);
                    List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(queryWrapper);
                    Map<Long, String> cascadeMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                    List<String> cascadeValue = new ArrayList<>();
                    for (String s : split) {
                        Long key = Long.valueOf(s);
                        cascadeValue.add(cascadeMap.get(key));
                    }
                    stringObjectMap.put(fieldDto.getApiName(), String.join("-", cascadeValue));
                }
                for (SysFieldDto fieldDto : dateList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    stringObjectMap.put(fieldDto.getApiName(), fieldValue);
                }
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        if (!manageList.isEmpty()) { resultMap.put("list", manageList); }
        resultMap.put("total", mapper.countManageList(query.getSheetApiName()));
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    public AjaxResult manageFieldList(UniversalApplyQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(Collections.singletonList(sysSheetDto.getId()))
                .stream().filter(sysFieldDto -> !sysFieldDto.getFieldType().equals(FieldTypeConstants.FILE)).collect(Collectors.toList());
        return AjaxResult.success(fieldByRoleIds);
    }

    public List<SysFieldDto> switchFieldType(Long sheetId){
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        for (String key : groupFieldType.keySet()) {
            List<SysFieldDto> sysFieldList = groupFieldType.get(key);
            switch (key) {
                case FieldTypeConstants.SELECT_SINGLE , FieldTypeConstants.SELECT_MULTI-> {
                    List<SysFieldDto> optionList = Optional.ofNullable(sysFieldList)
                            .orElse(Collections.emptyList()).stream()
                            .filter(sysFieldDto -> !Objects.equals(sysFieldDto.getOptionType(), 3))
                            .toList();
                    if(optionList.size()>0){
                        Set<Long> optionIdList = optionList.stream().map(SysFieldDto::getOptionId).filter(Objects::nonNull).collect(Collectors.toSet());
                        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                        lqw.in(SysOptionValuePo::getOptionId, optionIdList);
                        List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
                        Map<Long, List<SysOptionValuePo>> collect = sysOptionValuePoList.stream()
                                .collect(Collectors.groupingBy(SysOptionValuePo::getOptionId));
                        for (SysFieldDto fieldDto : sysFieldList) {
                            fieldDto.setOptionValuePoList(collect.get(fieldDto.getOptionId()));
                        }
                    }
                }
                case FieldTypeConstants.CASCADE -> {
                    List<Long> casecadeIdList = sysFieldList.stream().map(SysFieldDto::getCascadeId).filter(Objects::nonNull).toList();
                    Map<Long, List<Tree<String>>> casecadeTreeMap=new HashMap<>();
                    for (Long casecadeId : casecadeIdList) {
                        List<Tree<String>> trees = sysCascadeManager.selectCascadeRelation(casecadeId);
                        casecadeTreeMap.put(casecadeId,trees);
                    }
                    for (SysFieldDto fieldDto : sysFieldList) {
                        fieldDto.setCasecadeTreeList(casecadeTreeMap.get(fieldDto.getCascadeId()));
                    }
                }
            }
        }
        return groupFieldType.values().stream().flatMap(List::stream).toList();
    }

}
