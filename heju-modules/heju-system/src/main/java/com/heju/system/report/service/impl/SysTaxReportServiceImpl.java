package com.heju.system.report.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.system.ReportConstants;
import com.heju.system.entity.service.impl.SysEntityServiceImpl;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import com.heju.system.report.domain.query.SysTaxReportQuery;
import com.heju.system.report.mapper.SysTaxReportMapper;
import com.heju.system.report.service.ISysTaxReportService;
import com.heju.system.report.manager.ISysTaxReportManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 税务申报管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysTaxReportServiceImpl extends BaseServiceImpl<SysTaxReportQuery, SysTaxReportDto, ISysTaxReportManager> implements ISysTaxReportService {

    @Autowired
    SysTaxReportMapper taxReportMapper;

    @Autowired
    SysEntityServiceImpl entityService;

    /**
     * 查询税务申报对象列表 | 数据权限
     *
     * @param taxReport 税务申报对象
     * @return 税务申报对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysTaxReportMapper"})
    public List<SysTaxReportDto> selectListScope(SysTaxReportQuery taxReport) {
        List<SysTaxReportDto> sysTaxReportDtos = baseManager.selectList(taxReport);
        for (SysTaxReportDto sysTaxReportDto : sysTaxReportDtos) {
            sysTaxReportDto.setEntityName(entityService.selectById(sysTaxReportDto.getEntityId()).getName());
        }
        return sysTaxReportDtos;
    }


    /**
     * 新增数据对象(重写)
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysTaxReportDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        dto.setEntityName(entityService.selectById(dto.getEntityId()).getName());
        String timeType = dto.getReporttimeType();
        if (timeType.equals(ReportConstants.TimeType.MONTH.getCode())){
            //月度
            String yMd = dto.getMonth().split("T")[0];
            String[] end = yMd.split("-");
            dto.setYear(end[0]);
            dto.setMonth(end[1]);
        }
        if (dto.getSeason() != null && timeType.equals(ReportConstants.TimeType.SEASON.getCode())){
            //季度
            String[] yearAndSeason = dto.getSeason().split("-");
            dto.setYear(yearAndSeason[0]);
            int season = Character.getNumericValue(yearAndSeason[1].toCharArray()[1])-1;
            dto.setSeason(String.valueOf(season));
        }
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        return row;
    }

    /**
     * 修改数据对象（重写）
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysTaxReportDto dto) {
        SysTaxReportDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        dto.setEntityName(entityService.selectById(dto.getEntityId()).getName());
        String timeType = dto.getReporttimeType();
        if (timeType.equals(ReportConstants.TimeType.SEASON.getCode())){
            //时间类型为季度
            if (dto.getSeason() != null){
                String[] yearAndSeason = dto.getSeason().split("-");
                dto.setYear(yearAndSeason[0]);
                int season = Character.getNumericValue(yearAndSeason[1].toCharArray()[1])-1;
                dto.setSeason(String.valueOf(season));
                dto.setMonth(null);
            }
        }else if (timeType.equals(ReportConstants.TimeType.MONTH.getCode())){
            //时间类型为月度
            String yMd = dto.getMonth().split("T")[0];
            String[] end = yMd.split("-");
            dto.setYear(end[0]);
            dto.setMonth(end[1]);
            dto.setSeason(null);
        }else if (timeType.equals(ReportConstants.TimeType.YEARS.getCode())){
            //时间类型为年度
            String year = dto.getYear().split("-")[0];
            dto.setYear(year);
            dto.setMonth(null);
            dto.setSeason(null);
        }

        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        return row;
    }

    public List<SysTaxReportDto> findAll(SysReportManagementQuery query) {
        return taxReportMapper.findAll(query);
    }

    /**
     * 根据Id查询单条数据对象（重写）
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysTaxReportDto selectById(Serializable id) {
        SysTaxReportDto dto = baseManager.selectById(id);
        dto.setEntityName(entityService.selectById(dto.getEntityId()).getName());
        return subCorrelates(dto);
    }
}