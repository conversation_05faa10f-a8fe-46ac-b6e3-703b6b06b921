package com.heju.system.report.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysReportManagementQuery;

import java.util.List;
import java.util.Map;

/**
 * 报表管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysReportManagementService extends IBaseService<SysReportManagementQuery, SysReportManagementDto> {
    List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query);

    Map<String, Object> selectListPage(SysReportManagementQuery query);
}
