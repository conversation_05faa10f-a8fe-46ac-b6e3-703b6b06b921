package com.heju.system.entity.domain.query;

import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 企业经营异常信息 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityExceptionInfoQuery extends SysEntityExceptionInfoPo {

    private SysEntityExceptionInfoDto sysEntityExceptionInfoDto;

    @Serial
    private static final long serialVersionUID = 1L;
}