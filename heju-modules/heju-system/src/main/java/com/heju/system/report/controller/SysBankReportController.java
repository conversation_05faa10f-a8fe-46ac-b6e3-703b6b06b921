package com.heju.system.report.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.query.SysBankReportQuery;
import com.heju.system.report.service.ISysBankReportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 银行报表管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bank")
public class SysBankReportController extends BaseController<SysBankReportQuery, SysBankReportDto, ISysBankReportService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "银行报表" ;
    }

    /**
     * 查询银行报表列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_BANK_REPORT_LIST)
    public AjaxResult list(SysBankReportQuery bankReport) {
        return super.list(bankReport);
    }

    /**
     * 查询银行报表详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_BANK_REPORT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 银行报表新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_BANK_REPORT_ADD)
    @Log(title = "银行报表管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysBankReportDto bankReport) {
        return super.add(bankReport);
    }

    /**
     * 银行报表修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_BANK_REPORT_EDIT)
    @Log(title = "银行报表管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysBankReportDto bankReport) {
        return super.edit(bankReport);
    }

    /**
     * 银行报表修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_BANK_REPORT_EDIT, Auth.SYS_BANK_REPORT_ES}, logical = Logical.OR)
    @Log(title = "银行报表管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysBankReportDto bankReport) {
        return super.editStatus(bankReport);
    }

    /**
     * 银行报表批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_BANK_REPORT_DEL)
    @Log(title = "银行报表管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {

        return super.batchRemove(idList);
    }

    /**
     * 获取银行报表选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }
}
