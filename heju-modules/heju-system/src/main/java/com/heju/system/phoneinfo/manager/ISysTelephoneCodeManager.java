package com.heju.system.phoneinfo.manager;


import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.query.SysTelephoneCodeQuery;

import java.util.List;

/**
 * 转发短信管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysTelephoneCodeManager extends IBaseManager<SysTelephoneCodeQuery, SysTelephoneCodeDto> {

    /**
     * 条件查询短信信息
     * @param query
     * @return
     */
    List<SysTelephoneCodeDto> selectPhoMsgList(SysTelephoneCodeQuery query);
}
