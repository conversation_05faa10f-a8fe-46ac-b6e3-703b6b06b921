package com.heju.system.forms.option.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.domain.po.SysOptionPo;
import com.heju.system.forms.option.domain.query.SysOptionQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 选项 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysOptionConverter extends BaseConverter<SysOptionQuery, SysOptionDto, SysOptionPo> {
}
