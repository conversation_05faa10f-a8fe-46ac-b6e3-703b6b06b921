package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.po.SysEntityExaminePo;
import com.heju.system.entity.domain.query.SysEntityExamineQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体信息审核 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntityExamineConverter extends BaseConverter<SysEntityExamineQuery, SysEntityExamineDto, SysEntityExaminePo> {
}
