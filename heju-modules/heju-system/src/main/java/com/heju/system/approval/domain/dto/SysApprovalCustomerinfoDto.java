package com.heju.system.approval.domain.dto;

import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 客户信息审核 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysApprovalCustomerinfoDto extends SysApprovalCustomerinfoPo {

    @Serial
    private static final long serialVersionUID = 1L;

    // 字段中文名
    private String fieldName;

    // 业务名称
    private String businessName;

    // 表单所属模块 sheet 表 api_name
    private String sheetName;

    // 动态表名
    private String dynamicTableName;

    // 申请人
    private String createName;

}