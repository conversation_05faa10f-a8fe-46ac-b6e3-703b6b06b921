package com.heju.system.approval.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.model.SysApprovalCustomerinfoConverter;
import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import com.heju.system.approval.manager.ISysApprovalCustomerinfoManager;
import com.heju.system.approval.mapper.SysApprovalCustomerinfoMapper;
import com.heju.system.forms.field.manager.impl.SysFieldManager;
import com.heju.system.forms.sheet.manager.impl.SysSheetManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 客户信息审核管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysApprovalCustomerinfoManager extends BaseManagerImpl<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, SysApprovalCustomerinfoPo, SysApprovalCustomerinfoMapper, SysApprovalCustomerinfoConverter> implements ISysApprovalCustomerinfoManager {

    @Autowired
    private SysFieldManager sysFieldManager;
    @Autowired
    private SysSheetManager sysSheetManager;
    
    public List<SysApprovalCustomerinfoDto> selectByQuery(SysApprovalCustomerinfoQuery query) {
        // 1. sql查
//        if (query.getFieldName() == null || query.getFieldName().isEmpty() && query.getSheetName() == null || query.getSheetName().isEmpty()) {
//            return new ArrayList<>();
//        }
//        return baseMapper.selectByQuery(query);

        // 2. 业务查
        LambdaQueryWrapper<SysApprovalCustomerinfoPo> queryWrapper = new LambdaQueryWrapper<>();
        String fieldName = query.getFieldName();
        String sheetName = query.getSheetName();
        if (fieldName != null && !fieldName.isEmpty()) {
            // 通过 fieldName 查 sys_field 对应的 id 可能重复
            List<Long> fieldIds = sysFieldManager.selectIdsByFieldName(fieldName);
            if (fieldIds != null && !fieldIds.isEmpty()) {
                queryWrapper.in(SysApprovalCustomerinfoPo::getFieldId, fieldIds);
            }
        }
        if (sheetName != null && !sheetName.isEmpty()) {
            // 通过 sheetName 查 sys_sheet 对应的 id 可能重复
            List<Long> sheetIds = sysSheetManager.selectIdsBySheetName(sheetName); // sheetName 就是 apiName
            if (sheetIds != null && !sheetIds.isEmpty()) {
                queryWrapper.in(SysApprovalCustomerinfoPo::getSheetId, sheetIds);
            }
        }
        // 通过 fieldId 和 sheetId 查 sys_approval_customerinfo
        List<SysApprovalCustomerinfoPo> list = baseMapper.selectList(queryWrapper);
        return subMerge(mapperDto(list));
    }

    @Override
    public List<SysApprovalCustomerinfoDto> selectBusinessName(String apiName, Set<Long> businessIds) {
        return baseMapper.selectBusinessName(apiName, businessIds);
    }

    @Override
    public SysApprovalCustomerinfoDto selectBusinessNameById(String apiName, Long businessId) {
        return baseMapper.selectBusinessNameById(apiName, businessId);
    }

}