package com.heju.system.notice.domain.query;

import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 消息实体变更通知 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMessageEntityChangeQuery extends SysMessageEntityChangePo {

    @Serial
    private static final long serialVersionUID = 1L;
}
