package com.heju.system.forms.option.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.domain.query.SysOptionQuery;

/**
 * 选项管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysOptionService extends IBaseService<SysOptionQuery, SysOptionDto> {

    /**
     * 校验参数编码是否唯一
     *
     * @param Id      参数Id
     * @param name    名称
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkConfigCodeUnique(Long Id, String name);

    AjaxResult option(SysOptionQuery query);

}