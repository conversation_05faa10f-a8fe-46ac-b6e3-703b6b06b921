package com.heju.system.report.manager.impl;

import com.heju.system.report.domain.po.SysTaxReportPo;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.query.SysTaxReportQuery;
import com.heju.system.report.domain.model.SysTaxReportConverter;
import com.heju.system.report.mapper.SysTaxReportMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.report.manager.ISysTaxReportManager;
import org.springframework.stereotype.Component;

/**
 * 税务申报管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysTaxReportManager extends BaseManagerImpl<SysTaxReportQuery, SysTaxReportDto, SysTaxReportPo, SysTaxReportMapper, SysTaxReportConverter> implements ISysTaxReportManager {
}