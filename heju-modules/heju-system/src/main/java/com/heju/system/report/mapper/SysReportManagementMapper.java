package com.heju.system.report.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.po.SysFinanceReportPo;
import com.heju.system.report.domain.po.SysReportManagementPo;
import com.heju.system.report.domain.query.SysFinanceReportQuery;
import com.heju.system.report.domain.query.SysReportManagementQuery;

@Isolate
public interface SysReportManagementMapper extends BaseMapper<SysReportManagementQuery, SysReportManagementDto, SysReportManagementPo> {
}
