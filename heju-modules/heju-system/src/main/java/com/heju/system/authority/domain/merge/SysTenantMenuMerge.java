package com.heju.system.authority.domain.merge;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.web.tenant.base.TBasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 租户-菜单关联 持久化对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_tenant_menu_merge")
public class SysTenantMenuMerge extends TBasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 菜单Id */
    private Long menuId;

    /** 租户Id */
    private  Long tenantId;

    public SysTenantMenuMerge(Long menuId) {
        this.menuId = menuId;
    }


}
