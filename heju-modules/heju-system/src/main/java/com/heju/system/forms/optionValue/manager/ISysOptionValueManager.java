package com.heju.system.forms.optionValue.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;

import java.util.List;
import java.util.Map;

/**
 * 选项值管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysOptionValueManager extends IBaseManager<SysOptionValueQuery, SysOptionValueDto> {

    Map<Long, List<SysOptionValuePo>> selectByOptionId(List<Long> optionId);

    SysOptionValueDto checkConfigCodeUnique(Long Id, String name);
}