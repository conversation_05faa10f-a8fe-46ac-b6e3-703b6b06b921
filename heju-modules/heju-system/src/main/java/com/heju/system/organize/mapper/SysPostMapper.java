package com.heju.system.organize.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.po.SysPostPo;
import com.heju.system.api.organize.domain.query.SysPostQuery;

/**
 * 岗位管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysPostMapper extends BaseMapper<SysPostQuery, SysPostDto, SysPostPo> {
}
