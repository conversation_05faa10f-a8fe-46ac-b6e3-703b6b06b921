package com.heju.system.third.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.company.domain.dto.CompanyDeptPostDto;
import com.heju.system.company.domain.dto.CompanyDeptPostIdDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.manager.ISysCompanyManager;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.model.SysThirdAuthConverter;
import com.heju.system.third.domain.po.SysThirdAuthPo;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.system.third.manager.ISysThirdAuthMasterManager;
import com.heju.system.third.mapper.SysThirdAuthMasterMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 第三方认证管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysThirdAuthMasterManager extends BaseManagerImpl<SysThirdAuthQuery, SysThirdAuthDto, SysThirdAuthPo, SysThirdAuthMasterMapper, SysThirdAuthConverter> implements ISysThirdAuthMasterManager {

    @Autowired
    private ISysCompanyManager sysCompanyManager;

    @Autowired
    private ISysThirdAuthManager sysThirdAuthManager;

    @Override
    public SysThirdAuthDto checkThirdAuthCodeUnique(Long Id, String code) {
        SysThirdAuthPo company = baseMapper.selectOne(
                Wrappers.<SysThirdAuthPo>query().lambda()
                        .ne(SysThirdAuthPo::getId, Id)
                        .eq(SysThirdAuthPo::getCode, code)
                        .last(SqlConstants.LIMIT_ONE));
        return mapperDto(company);
    }

    @Override
    @Async
    public void send2RestCloud(Map<String, List<Long>> companyIdListMap, int type, Long thirdAuthId) {
        List<CompanyDeptPostIdDto> companyDeptPostIdDtoList = new ArrayList<>();
        List<CompanyDeptPostDto> companyDeptPostDtoList = new ArrayList<>();
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = new ArrayList<>();
        for (String sourceName : companyIdListMap.keySet()) {
            SecurityContextHolder.setSourceName(sourceName);
            List<SysCompanyThirdAuthMerge> companyThirdAuthMerges = new ArrayList<>();
            for (Long companyId : companyIdListMap.get(sourceName)) {
                SysCompanyThirdAuthMerge sysCompanyThirdAuthMerge = new SysCompanyThirdAuthMerge();
                sysCompanyThirdAuthMerge.setThirdAuthId(thirdAuthId);
                sysCompanyThirdAuthMerge.setCompanyId(companyId);
                companyThirdAuthMerges.add(sysCompanyThirdAuthMerge);
            }
            List<CompanyDeptPostIdDto> companyDeptPostIdDtos = new ArrayList<>();
            List<CompanyDeptPostDto> companyDeptPostDtos = new ArrayList<>();
            sysThirdAuthManager.setCompanyDeptPostList(companyDeptPostIdDtos, companyDeptPostDtos, companyThirdAuthMergeList, type);
            companyThirdAuthMergeList.addAll(companyThirdAuthMerges);
            companyDeptPostIdDtoList.addAll(companyDeptPostIdDtos);
            companyDeptPostDtoList.addAll(companyDeptPostDtoList);
        }
        sysCompanyManager.buildCompany(companyDeptPostIdDtoList, companyDeptPostDtoList, type, companyThirdAuthMergeList);
    }
}