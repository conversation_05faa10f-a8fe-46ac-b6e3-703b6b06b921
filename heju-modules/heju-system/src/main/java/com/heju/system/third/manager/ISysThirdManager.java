package com.heju.system.third.manager;

import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.query.SysThirdQuery;
import com.heju.common.web.entity.manager.IBaseManager;

/**
 * 第三方模块管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysThirdManager extends IBaseManager<SysThirdQuery, SysThirdDto> {

    /**
     * 校验第三方编码是否唯一
     *
     * @param Id   第三方Id
     * @param code 第三方编码
     * @return 第三方对象
     */
    SysThirdDto checkThirdCodeUnique(Long Id, String code);
}