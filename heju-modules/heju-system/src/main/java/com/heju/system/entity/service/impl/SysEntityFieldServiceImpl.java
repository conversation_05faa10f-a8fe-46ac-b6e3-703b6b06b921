package com.heju.system.entity.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.authority.domain.merge.SysRoleEntityFieldMerge;
import com.heju.system.authority.mapper.SysRoleMapper;
import com.heju.system.authority.mapper.merge.SysRoleEntityFieldMergeMapper;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.query.SysEntityFieldQuery;
import com.heju.system.entity.manager.ISysEntityFieldManager;
import com.heju.system.entity.mapper.SysEntityFieldMapper;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.entity.service.ISysEntityFieldService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

/**
 * 实体字段管理管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityFieldServiceImpl extends BaseServiceImpl<SysEntityFieldQuery, SysEntityFieldDto, ISysEntityFieldManager> implements ISysEntityFieldService {

    @Resource
    private  SysEntityMapper sysEntityMapper;
    @Resource
    private SysEntityFieldMapper sysEntityFieldMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysRoleEntityFieldMergeMapper sysRoleEntityFieldMergeMapper;


    /**
     * 查询实体字段管理对象列表 | 数据权限
     *
     * @param entityField 实体字段管理对象
     * @return 实体字段管理对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntityFieldMapper"})
    public List<SysEntityFieldDto> selectListScope(SysEntityFieldQuery entityField) {
        return baseManager.selectList(entityField);
    }

    @Override
    public List<SysEntityFieldDto> listByBelong(SysEntityFieldQuery entityField) {
        List<SysEntityFieldDto> sysEntityFieldDtos = baseManager.selectList(entityField);
        sysEntityFieldDtos.sort(Comparator.comparing(SysEntityFieldDto::getSort));

        return sysEntityFieldDtos;
    }


    @Override
    public HashMap<String,List<SysEntityFieldPo>> list(Long roleGroupId) {

        List<SysEntityFieldPo> sysEntityFieldPos;
        if (roleGroupId==null ){
            sysEntityFieldPos = sysEntityFieldMapper.selectList(null);
        }
        else {
            SysRolePo sysRolePo = sysRoleMapper.selectById(roleGroupId);
            QueryWrapper<SysRoleEntityFieldMerge> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysRoleEntityFieldMerge::getRoleId , sysRolePo.getId());
            List<SysRoleEntityFieldMerge> sysRoleEntityFieldMerges = sysRoleEntityFieldMergeMapper.selectList(queryWrapper);
            List<Long> entityFieldIds = new ArrayList<>();
            for (SysRoleEntityFieldMerge sysRoleEntityFieldMerge : sysRoleEntityFieldMerges) {
                entityFieldIds.add(sysRoleEntityFieldMerge.getEntityFieldId());
            }
            if (entityFieldIds.size()==0){
                return null;
            }
            sysEntityFieldPos = sysEntityFieldMapper.selectBatchIds(entityFieldIds);
        }
        HashMap<String, List<SysEntityFieldPo>> map = new HashMap<>();
        if (sysEntityFieldPos.size()>0){

            for (SysEntityFieldPo sysEntityFieldPo : sysEntityFieldPos) {
                String fieldBelong = sysEntityFieldPo.getFieldBelong();
                if (!map.containsKey(fieldBelong)) {
                    map.put(fieldBelong, new ArrayList<>());
                }
                map.get(fieldBelong).add(sysEntityFieldPo);
            }
        }
        return map;
    }
    @Override
    protected void endHandle(OperateConstants.ServiceType operate, int row, SysEntityFieldDto originDto, SysEntityFieldDto newDto) {
        switch (operate) {
            case ADD -> {
                sysEntityMapper.addNewColumn(newDto);
            }
            case EDIT -> {
                newDto.setOldfieldName(originDto.getFieldName());
                sysEntityMapper.editNewColumn(newDto);
            }
            case DELETE -> {
                sysEntityMapper.deleteNewColumn(newDto);
            }
        }
    }

    @Override
    protected void endBatchHandle(OperateConstants.ServiceType operate, int rows, Collection<SysEntityFieldDto> originList, Collection<SysEntityFieldDto> newList) {
        switch (operate) {
            case BATCH_DELETE -> {
                for (SysEntityFieldDto sysEntityFieldDto : originList) {
                    sysEntityMapper.deleteNewColumn(sysEntityFieldDto);
                }
            }
        }
    }
}

