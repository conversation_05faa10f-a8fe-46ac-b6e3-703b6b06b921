package com.heju.system.notice.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 消息通知 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_message", excludeProperty = { UPDATE_BY, CREATE_BY, DEL_FLAG, UPDATE_TIME, REMARK, NAME })
public class SysMessagePo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 通知标题 */
    @Excel(name = "通知标题")
    protected String title;

    /** 通知内容 */
    @Excel(name = "通知内容")
    protected String content;

    /** 通知类型（1-实体变更通知） */
    @Excel(name = "通知类型", readConverterExp = "1=-实体变更通知")
    protected Integer type;

    /** 状态（0-未读，1-已读） */
    @Excel(name = "状态", readConverterExp = "0=-未读，1-已读")
    protected String status;

    /** 发送人 */
    @Excel(name = "发送人")
    protected Long sendUserId;

    /** 接收人 */
    @Excel(name = "接收人")
    protected Long receiveUserId;

    /** 实体更变消息详情id */
    @Excel(name = "实体更变消息详情id")
    protected Long changeMessageId;

    public void packageMessageContent(String typeName, String before, String after){
        this.content = typeName + "\r"+ "字段信息变更前【" + before + "】\r" + "字段信息变更后【" + after + "】";
    }

}