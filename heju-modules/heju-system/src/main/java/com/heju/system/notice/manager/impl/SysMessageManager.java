package com.heju.system.notice.manager.impl;

import com.heju.system.notice.domain.po.SysMessagePo;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.query.SysMessageQuery;
import com.heju.system.notice.domain.model.SysMessageConverter;
import com.heju.system.notice.mapper.SysMessageMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.notice.manager.ISysMessageManager;
import org.springframework.stereotype.Component;

/**
 * 消息通知管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysMessageManager extends BaseManagerImpl<SysMessageQuery, SysMessageDto, SysMessagePo, SysMessageMapper, SysMessageConverter> implements ISysMessageManager {
}