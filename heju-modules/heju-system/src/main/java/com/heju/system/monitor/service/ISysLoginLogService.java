package com.heju.system.monitor.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.api.log.domain.dto.SysLoginLogDto;
import com.heju.system.api.log.domain.query.SysLoginLogQuery;

/**
 * 访问日志管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysLoginLogService extends IBaseService<SysLoginLogQuery, SysLoginLogDto> {

    /**
     * 清空系统登录日志
     */
    void cleanLoginLog();
}
