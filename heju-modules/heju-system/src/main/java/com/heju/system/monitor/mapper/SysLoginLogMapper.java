package com.heju.system.monitor.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.log.domain.dto.SysLoginLogDto;
import com.heju.system.api.log.domain.po.SysLoginLogPo;
import com.heju.system.api.log.domain.query.SysLoginLogQuery;

/**
 * 访问日志管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysLoginLogMapper extends BaseMapper<SysLoginLogQuery, SysLoginLogDto, SysLoginLogPo> {
}
