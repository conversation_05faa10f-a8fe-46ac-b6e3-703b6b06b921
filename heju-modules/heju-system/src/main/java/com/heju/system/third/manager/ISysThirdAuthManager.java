package com.heju.system.third.manager;


import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.po.SysDeptPo;
import com.heju.system.company.domain.dto.CompanyDeptPostDto;
import com.heju.system.company.domain.dto.CompanyDeptPostIdDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.common.web.entity.manager.IBaseManager;

import java.util.List;
import java.util.Map;

/**
 * 第三方认证管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysThirdAuthManager extends IBaseManager<SysThirdAuthQuery, SysThirdAuthDto> {

    /**
     * 校验第三方认证信息编码是否唯一
     *
     * @param Id   第三方认证信息Id
     * @param code 第三方认证信息编码
     * @return 第三方认证信息对象
     */
    SysThirdAuthDto checkThirdAuthCodeUnique(Long Id, String code);


    List<SysCompanyThirdAuthMerge> getDelList(Map<Long, Long> companyBeforeMap, Map<Long, Long> companyAfterMap);

    List<SysCompanyThirdAuthMerge> getAddList(List<SysCompanyThirdAuthMerge> delList,Map<Long, Long> companyAfterMap,Long thirdAuthId,Long thirdId);

    void delRedis(List<SysCompanyThirdAuthMergeDto> list);

    List<SysCompanyThirdAuthMerge> selectThirdAuthId(Long companyId, List<SysThirdDto> thirdDtoList);

    int deleteCompanyThirdAuthMerge(List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList);

    List<SysCompanyThirdAuthMerge> getByCompanyIds(List<Long> idList);

    List<SysCompanyThirdAuthMerge> getByCompanyId(Long companyId);

    void send2RestCloud(List<SysCompanyThirdAuthMerge> delList,int type);

    Map<Long,List<SysUserDto>> getUserListByCompanyId(Map<Long, List<SysDeptPo>> sysDeptPoMap);

    void setCompanyDeptPostList(List<CompanyDeptPostIdDto> companyDeptPostIdDtoList, List<CompanyDeptPostDto> companyDeptPostDtoList, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList, int type);

}