package com.heju.system.declaration.service.impl;

import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.query.SysBusinessAnnualReportQuery;
import com.heju.system.declaration.mapper.SysBusinessAnnualReportMapper;
import com.heju.system.declaration.service.ISysBusinessAnnualReportService;
import com.heju.system.declaration.manager.ISysBusinessAnnualReportManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.service.impl.SysEntityServiceImpl;
import com.heju.system.organize.service.impl.SysUserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;


/**
 * 工商年报管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysBusinessAnnualReportServiceImpl extends BaseServiceImpl<SysBusinessAnnualReportQuery, SysBusinessAnnualReportDto, ISysBusinessAnnualReportManager> implements ISysBusinessAnnualReportService {

    @Autowired
    SysEntityServiceImpl entityService;

    @Autowired
    SysUserServiceImpl userService;

    @Autowired
    SysBusinessAnnualReportMapper businessAnnualReportMapper;

    /**
     * 查询工商年报对象列表 | 数据权限
     *
     * @param businessAnnualReport 工商年报对象
     * @return 工商年报对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysBusinessAnnualReportMapper"})
    public List<SysBusinessAnnualReportDto> selectListScope(SysBusinessAnnualReportQuery businessAnnualReport) {
        List<SysBusinessAnnualReportDto> reportDto = baseManager.selectList(businessAnnualReport);
        for (SysBusinessAnnualReportDto dto : reportDto) {
            SysEntityPo sysEntityPo = entityService.selectById(dto.getEntityId());
            if (!Objects.isNull(sysEntityPo)){
                dto.setEntityName(sysEntityPo.getName());
            }
            SysUserDto sysUserDto = userService.selectById(dto.getCreateBy());
            if (!Objects.isNull(sysUserDto)){
                dto.setDeclareBy(sysUserDto.getNickName());
            }
        }
        reportDto.sort(Comparator.comparing(SysBusinessAnnualReportDto::getEntityId));
        return reportDto;
    }

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysBusinessAnnualReportDto selectById(Serializable id) {
        SysBusinessAnnualReportDto dto = baseManager.selectById(id);
        dto.setEntityName(entityService.selectById(dto.getEntityId()).getName());
        return subCorrelates(dto);
    }
}