package com.heju.system.declaration.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.po.SysBusinessAnnualReportPo;
import com.heju.system.declaration.domain.query.SysBusinessAnnualReportQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 工商年报 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysBusinessAnnualReportConverter extends BaseConverter<SysBusinessAnnualReportQuery, SysBusinessAnnualReportDto, SysBusinessAnnualReportPo> {
}
