package com.heju.system.forms.sheet.mapper;

import com.heju.system.forms.sheet.domain.query.SysSheetQuery;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.po.SysSheetPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;

/**
 * 表单管理管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysSheetMapper extends BaseMapper<SysSheetQuery, SysSheetDto, SysSheetPo> {

    @Update("CREATE TABLE ${apiName} ( " +
            "   `id` bigint(20) NOT NULL COMMENT '唯一标识id', " +
            "   `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）', " +
            "   PRIMARY KEY (`id`) USING BTREE " +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='${name}'")
    void createSheet(SysSheetDto dto);

    @Update("ALTER TABLE `${apiName}` COMMENT = #{name}")
    void updateSheet(SysSheetDto dto);

    @Update("DROP TABLE IF EXISTS `${apiName}`")
    void deleteSheet(SysSheetDto dto);
}
