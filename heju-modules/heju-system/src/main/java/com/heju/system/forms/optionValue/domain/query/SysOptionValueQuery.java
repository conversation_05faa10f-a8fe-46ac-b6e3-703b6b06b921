package com.heju.system.forms.optionValue.domain.query;

import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 选项值 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysOptionValueQuery extends SysOptionValuePo {

    @Serial
    private static final long serialVersionUID = 1L;
}