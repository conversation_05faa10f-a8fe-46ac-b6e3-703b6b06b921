package com.heju.system.notice.mapper;

import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import com.heju.system.notice.domain.query.SysMessageQuery;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.po.SysMessagePo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 消息通知管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysMessageMapper extends BaseMapper<SysMessageQuery, SysMessageDto, SysMessagePo> {
    @Insert({
            "<script>" +
                    "INSERT INTO sys_message (title, content, type, send_user_id, receive_user_id, change_message_id) " +
                    "VALUES " +
                    "<foreach collection='sysMessages' item='item' separator=','>" +
                    "(#{item.title},#{item.content},#{item.type},#{item.sendUserId},#{item.receiveUserId},#{item.changeMessageId})" +
                    "</foreach>" +
            "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void sendMessage(@Param("sysMessages") List<SysMessageDto> sysMessages);

    @Select("select count(*) from sys_message where receive_user_id = #{userId} and status = #{status}")
    int getMessageCount(@Param("userId") Long userId, @Param("status") String status);
}