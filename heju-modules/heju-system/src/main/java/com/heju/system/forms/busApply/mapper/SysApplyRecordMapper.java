package com.heju.system.forms.busApply.mapper;

import com.heju.system.forms.busApply.domain.query.SysApplyRecordQuery;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.forms.busApply.domain.query.UniversalApplyQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 行政申请管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysApplyRecordMapper extends BaseMapper<SysApplyRecordQuery, SysApplyRecordDto, SysApplyRecordPo> {

    /**
     * 查询申请列表总数
     * @param query
     * @return 总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) " +
            "FROM sys_apply_record " +
            "WHERE 1=1 " +
            "<if test='query.sheetApiName != null and query.sheetApiName != \"\"'> " +
            "  AND api_name = #{query.sheetApiName} " +
            "</if> " +
            "<if test='query.applyPersonId != null'> " +
            "  AND create_by = #{query.applyPersonId} " +
            "</if> " +
            "</script>")
    List<Map<String,Object>> countApplyList(@Param("query") UniversalApplyQuery query);

    @Select({
            "<script>",
            "${selectSql}, sys_apply_record.*, sys_apply_record.id AS applyId ",
            "FROM sys_apply_record ",
            "LEFT JOIN ${sheetApiName} ON sys_apply_record.business_id = ${sheetApiName}.id ",
            "WHERE 1=1 ",
            "<if test='sheetApiName != null'> ",
            "   AND sys_apply_record.api_name = #{sheetApiName} ",
            "</if> ",
            "<if test='query.applyPersonId != null'> " +
            "   AND sys_apply_record.create_by = #{query.applyPersonId} " +
            "</if> " +
            "</script>"
    })
    List<Map<String, Object>> selectApplyList(@Param("selectSql") String selectSql,
                                              @Param("sheetApiName") String sheetApiName,
                                              @Param("query") UniversalApplyQuery query);


    @Select({
            "<script>",
            "${selectSql} ",
            "FROM ${sheetApiName} ",
            "WHERE 1=1 ",
            "<if test='query.creditNo != null'> ",
            "   AND credit_no = #{query.creditNo} ",
            "</if> ",
            "</script>"
    })
    List<Map<String, Object>> selectAddList(@Param("selectSql") String selectSql,
                                              @Param("sheetApiName") String sheetApiName,
                                              @Param("query") UniversalApplyQuery query);


    @Select({
            "<script>",
            "SELECT business_id ",
            "FROM sys_apply_record ",
            "WHERE apply_status IN ('0', '2')",
            // 动态拼接其他查询条件（必须用 <script> 标签包裹）
            "<if test='query.sheetApiName != null and query.sheetApiName != \"\"'>",
            "  AND api_name = #{query.sheetApiName}",
            "</if>",
            "<if test='query.applyPersonId != null'>",
            "  AND create_by = #{query.applyPersonId}",
            "</if>",
            "</script>"
    })
    Set<Long> selectApplied(@Param("query") UniversalApplyQuery query);

    /**
     * 主查询
     * @param apiName 动态表名
     * @param query 分页对象
     * @return 列表
     */
    // 从动态表里查出 name，credit_no, code， 传递 credit_no, apiName 给 子查询 做 一对多 查询
    @Select("<script>" +
            "select DISTINCT ${apiName}.code, ${apiName}.credit_no, ${apiName}.name, #{apiName} as _apiName_" +
            " from ${apiName} " +
            " where del_flag=0 and `status`=0 " +
            " limit #{query.page}, #{query.pageSize}" +
            "</script>")
    @Results({
            @Result(property = "name", column = "name"),
            @Result(property = "code", column = "code"),
            @Result(property = "creditNo", column = "credit_no"),
            @Result(property = "relatedItems",
                    column = "{apiName=_apiName_, creditNo=credit_no}",
                    javaType = List.class,
                    many = @Many(select = "com.heju.system.forms.busApply.mapper.SysApplyRecordMapper.selectListByDynamicTable"))
    })
    List<Map<String, Object>> selectListWithQuote(@Param("apiName") String apiName, @Param("query") UniversalApplyQuery query);

    /**
     * 子查询
     * @param apiName 动态表名
     * @param creditNo 统一社会信用代码
     * @return 列表
     */
    @Select("select * from ${apiName} where credit_no = #{creditNo} and del_flag=0 and `status`=0")
    List<Map<String, Object>> selectListByDynamicTable(@Param("apiName") String apiName, @Param("creditNo") String creditNo);

    /**
     * 查询总数
     * @param apiName 动态表名
     * @return 总数
     */
    @Select("select count(*) from ${apiName} where del_flag=0 and `status`=0")
    List<Map<String,Object>> countList(@Param("apiName") String apiName);

    /**
     * 查询列表
     * @param apiName 动态表名
     * @param finalSelectSql 字段
     * @return 列表
     */
    @Select("<script>" +
            "select ${finalSelectSql}, app.`id` as applyId from sys_apply_record app left join ${apiName} on app.business_id=${apiName}.`id` " +
            " where ${apiName}.del_flag=0 and ${apiName}.`status`=0 and app.apply_status != '3' limit #{query.page}, #{query.pageSize}" +
            "</script>")
    List<Map<String, Object>> selectManageList(@Param("apiName") String apiName, @Param("finalSelectSql") String finalSelectSql, @Param("query") UniversalApplyQuery query);

    @Select("<script>" +
            "select count(*) from sys_apply_record app left join ${apiName} on app.business_id=${apiName}.`id`" +
            " where ${apiName}.del_flag=0 and ${apiName}.`status`=0"+
            "</script>")
    List<Map<String, Object>> countManageList(@Param("apiName") String apiName);

//    @Select("<script>" +
//            "select app.* from sys_apply_record app left join ${apiName} on app.business_id=${apiName}.`id`" +
//            "</script>")
//    List<Map<String,Object>> option(@Param("apiName") String apiName);
}
