package com.heju.system.phone.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.system.phone.domain.po.SysPhoPermissionRecordPo;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.system.phone.domain.model.SysPhoPermissionRecordConverter;
import com.heju.system.phone.mapper.SysPhoPermissionRecordMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.phone.manager.ISysPhoPermissionRecordManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 手机号授权管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysPhoPermissionRecordManager extends BaseManagerImpl<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto, SysPhoPermissionRecordPo, SysPhoPermissionRecordMapper, SysPhoPermissionRecordConverter> implements ISysPhoPermissionRecordManager {

    @Autowired
    private SysPhoPermissionRecordMapper mapper;

    /**
     * 查询授权列表
     * @param query 数据查询对象
     * @return
     */
    @Override
    public List<SysPhoPermissionRecordDto> selectPermList(SysPhoPermissionRecordQuery query) {
        //精确到秒级
        LocalDateTime now = LocalDateTime.now();
        List<SysPhoPermissionRecordPo> poList = baseMapper.selectList(new LambdaQueryWrapper<SysPhoPermissionRecordPo>(query)
                .isNull(SysPhoPermissionRecordPo::getEndTime)
                .or()
                .ge(SysPhoPermissionRecordPo::getEndTime, now));
        return subMerge(mapperDto(poList));
    }

    /**
     * 查借阅记录列表
     * @param query 数据查询对象
     * @return
     */
    public List<SysPhoPermissionRecordDto> selectOverTimeList(SysPhoPermissionRecordQuery query) {
        //精确到秒级
        LocalDateTime now = LocalDateTime.now();
        List<SysPhoPermissionRecordPo> poList = baseMapper.selectList(
                new LambdaQueryWrapper<SysPhoPermissionRecordPo>(query)
                        .and(wrapper -> wrapper
                                .isNotNull(SysPhoPermissionRecordPo::getEndTime)
                                .le(SysPhoPermissionRecordPo::getEndTime, now)
                        )
                        .or(wrapper -> wrapper
                                .isNull(SysPhoPermissionRecordPo::getEndTime)
                                .eq(SysPhoPermissionRecordPo::getRemainingTimes, 0)
                        )
        );
        return subMerge(mapperDto(poList));
    }


    /**
     * 短信查询条件获取
     * @param query 数据查询对象
     * @return
     */
    public List<SysPhoPermissionRecordDto> selectRecordForMsg(SysPhoPermissionRecordQuery query) {
        List<SysPhoPermissionRecordPo> poList = baseMapper.selectList(new LambdaQueryWrapper<SysPhoPermissionRecordPo>(query)
                .le(SysPhoPermissionRecordPo::getStartTime, query.getQueryTime())
                .ge(SysPhoPermissionRecordPo::getEndTime, query.getQueryTime())
                .eq(SysPhoPermissionRecordPo::getPermissionerId, query.getPermissionerId())
                .like(SysPhoPermissionRecordPo::getPhoneNumber, query.getPhoneNumber()));
        return subMerge(mapperDto(poList));
    }

    @Override
    public List<SysPhoPermissionRecordDto> selectRecordForTimes(SysPhoPermissionRecordQuery query) {
        LocalDateTime now = LocalDateTime.now();
        List<SysPhoPermissionRecordPo> poList = baseMapper.selectList(new LambdaQueryWrapper<SysPhoPermissionRecordPo>(query)
                .and(wrapper -> wrapper
                        .le(SysPhoPermissionRecordPo::getStartTime, now)
                        .ge(SysPhoPermissionRecordPo::getEndTime, now)
                        .or()
                        .isNull(SysPhoPermissionRecordPo::getStartTime)
                )
                .and(wrapper -> wrapper
                        .gt(SysPhoPermissionRecordPo::getRemainingTimes, 0)
                        .or()
                        .eq(SysPhoPermissionRecordPo::getTimes, -1)));
        return subMerge(mapperDto(poList));
    }

}
