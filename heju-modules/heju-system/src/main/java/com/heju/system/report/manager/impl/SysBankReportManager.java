package com.heju.system.report.manager.impl;

import com.heju.system.report.domain.po.SysBankReportPo;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.query.SysBankReportQuery;
import com.heju.system.report.domain.model.SysBankReportConverter;
import com.heju.system.report.mapper.SysBankReportMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.report.manager.ISysBankReportManager;
import org.springframework.stereotype.Component;

/**
 * 银行报表管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysBankReportManager extends BaseManagerImpl<SysBankReportQuery, SysBankReportDto, SysBankReportPo, SysBankReportMapper, SysBankReportConverter> implements ISysBankReportManager {
}