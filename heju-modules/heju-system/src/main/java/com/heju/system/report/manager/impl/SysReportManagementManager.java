package com.heju.system.report.manager.impl;

import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.service.impl.SysCompanyServiceImpl;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.service.impl.SysEntityServiceImpl;
import com.heju.system.report.domain.dto.*;
import com.heju.system.report.domain.model.SysReportManagementConverter;
import com.heju.system.report.domain.po.SysReportManagementPo;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import com.heju.system.report.manager.ISysReportManagementManager;
import com.heju.system.report.mapper.SysReportManagementMapper;
import com.heju.system.report.service.impl.SysBankReportServiceImpl;
import com.heju.system.report.service.impl.SysBillReportServiceImpl;
import com.heju.system.report.service.impl.SysFinanceReportServiceImpl;
import com.heju.system.report.service.impl.SysTaxReportServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class SysReportManagementManager extends BaseManagerImpl<SysReportManagementQuery, SysReportManagementDto, SysReportManagementPo, SysReportManagementMapper, SysReportManagementConverter> implements ISysReportManagementManager {
    @Autowired
    SysBankReportServiceImpl bankReportService;

    @Autowired
    SysBillReportServiceImpl billReportService;

    @Autowired
    SysFinanceReportServiceImpl financeReportService;

    @Autowired
    SysTaxReportServiceImpl taxReportService;

    @Autowired
    SysCompanyServiceImpl companyService;

    @Autowired
    SysEntityServiceImpl entityService;

    /**
     * 查询并整合所有报表
     * @return 报表list
     */
    public List<SysReportManagementQuery> Conformity(SysReportManagementQuery query) {
        List<SysReportManagementQuery> end = new ArrayList<>();
        Map<Long, List<SysBankReportDto>> bankReportMap = bankReportService.findAll(query)
                .stream()
                .collect(Collectors.groupingBy(SysBankReportDto::getEntityId));

        Map<Long, List<SysBillReportDto>> billReportMap = billReportService.findAll(query)
                .stream()
                .collect(Collectors.groupingBy(SysBillReportDto::getEntityId));

        Map<Long, List<SysFinanceReportDto>> financeReportMap = financeReportService.findAll(query)
                .stream()
                .collect(Collectors.groupingBy(SysFinanceReportDto::getEntityId));

        Map<Long, List<SysTaxReportDto>> taxReportMap = taxReportService.findAll(query)
                .stream()
                .collect(Collectors.groupingBy(SysTaxReportDto::getEntityId));

        // 查询entity名称和id
        List<SysEntityPo> entityDtoList = entityService.findAllEntityIdAndName(query);
        if (Objects.isNull(entityDtoList)){
            return end;
        }
        Map<Long, String> entityNameMap = new HashMap<>();
        for (SysEntityPo sysEntityPo : entityDtoList) {
            entityNameMap.put(sysEntityPo.getId(),sysEntityPo.getName());
        }

        // 2. 遍历companyNameMap,为每个公司构建SysReportManagementDto
        end = entityNameMap.entrySet().stream()
                .map(entry -> {
                    Long entityId = entry.getKey();
                    String entityName = entry.getValue();

                    SysReportManagementQuery dto = new SysReportManagementQuery();
                    dto.setEntityId(entityId);
                    dto.setEntityName(entityName);

                    // 从各个报表Map中获取数据,并填充到dto中
                    if (bankReportMap.containsKey(entityId)) {
                        dto.setBankReports(bankReportMap.get(entityId));
                    }
                    if (billReportMap.containsKey(entityId)) {
                        dto.setBillReports(billReportMap.get(entityId));
                    }
                    if (financeReportMap.containsKey(entityId)) {
                        dto.setFinanceReports(financeReportMap.get(entityId));
                    }
                    if (taxReportMap.containsKey(entityId)) {
                        dto.setTaxReports(taxReportMap.get(entityId));
                    }

                    return dto;
                })
                .collect(Collectors.toList());
        return end;
    }
}
