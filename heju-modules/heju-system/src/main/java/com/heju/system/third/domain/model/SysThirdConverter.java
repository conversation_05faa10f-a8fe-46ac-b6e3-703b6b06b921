package com.heju.system.third.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.po.SysThirdPo;
import com.heju.system.third.domain.query.SysThirdQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 第三方模块 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysThirdConverter extends BaseConverter<SysThirdQuery, SysThirdDto, SysThirdPo> {
}
