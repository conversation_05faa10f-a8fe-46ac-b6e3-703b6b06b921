package com.heju.system.declaration.mapper;

import com.heju.system.declaration.domain.query.SysTaxFilingsQuery;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.system.declaration.domain.po.SysTaxFilingsPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 税务申报管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysTaxFilingsMapper extends BaseMapper<SysTaxFilingsQuery, SysTaxFilingsDto, SysTaxFilingsPo> {

    @Select("select * from sys_tax_filings " +
            "where entity_id = #{entityId} and tax_type = #{taxType} and reporttime_type = #{reporttimeType} " +
            "and IF(#{year} is not null, year = #{year}, 1 = 1) " +
            "and IF(#{month} is not null, month = #{month}, 1 = 1) " +
            "and IF(#{season} is not null, season = #{season}, 1 = 1) " +
            "and del_flag = '0'" )
    SysTaxFilingsDto selectByCondition(SysTaxFilingsDto dto);

    @Select("select * from sys_tax_filings where " +
            "IF(#{entityId} is not null, entity_id = #{entityId}, 1 = 1) " +
            "and IF(#{year} is not null, year = #{year}, 1 = 1) " +
            "and del_flag = '0'")
    List<SysTaxFilingsDto> findAllByCondition(SysTaxFilingsQuery query);

    @Select("select * from sys_tax_report where del_flag = '0'" +
            "and (case when #{reporttypeType} is not null then tax_type = #{reporttypeType} else 1=1 end)" +
            "and (case when #{reporttimeType} is not null then reporttime_type = #{reporttimeType} else 1=1 end)" +
            "and (case when #{year} is not null then year = #{year} else 1=1 end)" +
            "and entity_id = #{entityId}")
    List<SysReportManagementQuery> selectByQuery(SysReportManagementQuery query);

    @Select("select * from sys_tax_filings")
    List<SysTaxFilingsDto> selectAll();
}