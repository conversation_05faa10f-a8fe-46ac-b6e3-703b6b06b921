package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.system.entity.domain.query.SysEntitySaicPartnerQuery;
import com.heju.system.entity.domain.model.SysEntitySaicPartnerConverter;
import com.heju.system.entity.mapper.SysEntitySaicPartnerMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntitySaicPartnerManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体工商股东管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntitySaicPartnerManager extends BaseManagerImpl<SysEntitySaicPartnerQuery, SysEntitySaicPartnerDto, SysEntitySaicPartnerPo, SysEntitySaicPartnerMapper, SysEntitySaicPartnerConverter> implements ISysEntitySaicPartnerManager {

    @Override
    public List<SysEntitySaicPartnerPo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntitySaicPartnerPo> lambdaPartner = new LambdaQueryWrapper<>();
        lambdaPartner.eq(SysEntitySaicPartnerPo::getEntityId,id);
        return baseMapper.selectList(lambdaPartner);
    }
}