package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.po.SysEntitySaicChangeRecordPo;
import com.heju.system.entity.domain.query.SysEntitySaicChangeRecordQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体工商变更记录 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntitySaicChangeRecordConverter extends BaseConverter<SysEntitySaicChangeRecordQuery, SysEntitySaicChangeRecordDto, SysEntitySaicChangeRecordPo> {
}
