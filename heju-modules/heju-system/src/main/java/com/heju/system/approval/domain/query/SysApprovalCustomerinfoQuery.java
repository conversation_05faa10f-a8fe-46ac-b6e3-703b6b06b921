package com.heju.system.approval.domain.query;

import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 客户信息审核 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysApprovalCustomerinfoQuery extends SysApprovalCustomerinfoPo {

    @Serial
    private static final long serialVersionUID = 1L;

    // 字段中文名
    private String fieldName;

    // 字段所属模块名称 sheet 表 api_name
    private String sheetName;
}