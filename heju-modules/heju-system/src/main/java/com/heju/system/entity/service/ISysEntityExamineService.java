package com.heju.system.entity.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.query.SysEntityExamineQuery;

import java.util.List;

/**
 * 实体信息审核管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysEntityExamineService extends IBaseService<SysEntityExamineQuery, SysEntityExamineDto> {

    AjaxResult editStatus(SysEntityExamineDto entityExamine);

    /**
     * 查询数据对象列表 | 数据权限 | 附加数据
     *
     * @param query 数据查询对象
     * @return 数据对象集合
     */
    List<SysEntityExamineDto> selectListScope(SysEntityExamineQuery query);
}