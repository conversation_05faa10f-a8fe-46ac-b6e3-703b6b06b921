package com.heju.system.entity.domain.query;

import com.heju.common.core.web.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.bouncycastle.cms.PasswordRecipientId;

import java.io.Serial;

/**
 * 实体信息管理 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityQuery extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer page;

    private Integer pageSize;

    private String entityType;

    private String creditNo;

    private String orderSort;

    private String code;
}