package com.heju.system.entity.service.impl;

import com.heju.system.entity.domain.dto.SysEntityTaxationUserDto;
import com.heju.system.entity.domain.query.SysEntityTaxationUserQuery;
import com.heju.system.entity.service.ISysEntityTaxationUserService;
import com.heju.system.entity.manager.ISysEntityTaxationUserManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体税务人员信息管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityTaxationUserServiceImpl extends BaseServiceImpl<SysEntityTaxationUserQuery, SysEntityTaxationUserDto, ISysEntityTaxationUserManager> implements ISysEntityTaxationUserService {

    /**
     * 查询实体税务人员信息对象列表 | 数据权限
     *
     * @param entityTaxationUser 实体税务人员信息对象
     * @return 实体税务人员信息对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntityTaxationUserMapper"})
    public List<SysEntityTaxationUserDto> selectListScope(SysEntityTaxationUserQuery entityTaxationUser) {
        return baseManager.selectList(entityTaxationUser);
    }

}