package com.heju.system.authority.manager.impl;

import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.model.SysRoleGroupConverter;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.query.SysRoleGroupQuery;
import com.heju.system.authority.manager.ISysRoleGroupManager;
import com.heju.system.authority.mapper.SysRoleGroupMapper;
import org.springframework.stereotype.Component;

/**
 * 角色组管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysRoleGroupManager extends BaseManagerImpl<SysRoleGroupQuery, SysRoleGroupDto, SysRoleGroupPo, SysRoleGroupMapper, SysRoleGroupConverter> implements ISysRoleGroupManager {
}