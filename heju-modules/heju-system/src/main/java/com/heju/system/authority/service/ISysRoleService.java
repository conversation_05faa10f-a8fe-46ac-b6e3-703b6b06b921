package com.heju.system.authority.service;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleEntityDto;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.api.authority.domain.query.SysRoleQuery;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.authority.domain.vo.SysRoleGroupScopeTree;

import java.util.List;

/**
 * 角色管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysRoleService extends IBaseService<SysRoleQuery, SysRoleDto> {



    /**
     * 修改角色组织权限
     *
     * @param role 角色对象
     * @return 结果
     */
    int updateDataScope(SysRoleDto role);

    /**
     * 校验角色编码是否唯一
     *
     * @param Id   角色Id
     * @param code 角色编码
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkRoleCodeUnique(Long Id, String code);

    /**
     * 校验角色权限是否唯一
     *
     * @param Id      角色Id
     * @param roleKey 角色权限
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkRoleKeyUnique(Long Id, String roleKey);

    /**
     * 查询角色已拥有的实体字段
     * @param roleId
     * @return
     */

    /**
     * 角色添加实体字段
     * @param sysRoleEntityDto
     */
    void roleBatchesAddEntity(SysRoleEntityDto sysRoleEntityDto);

    /**
     * 通过roleKey获取菜单Ids
     * @param roleKey 角色权限字符串
     * @return 角色id
     */
    SysUserDto getRoleIdsByRoleKey(String roleKey);

    /**
     * 查询角色组
     * @return
     */
    List<SysRolePo> getRoleGroupList();

    /**
     * 查询角色组织树
     * @return
     */
    List<SysRoleGroupScopeTree> selectRoleGroupScope();
}
