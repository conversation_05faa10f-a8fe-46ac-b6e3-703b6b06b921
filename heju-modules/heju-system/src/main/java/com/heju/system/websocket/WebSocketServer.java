package com.heju.system.websocket;

import com.heju.common.core.utils.JwtUtil;
import com.heju.common.security.service.TokenService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * websocket服务
 *
 * <AUTHOR>
 */
@ServerEndpoint("/websocket/{token}")
@Component
@Slf4j
public class WebSocketServer {
    /**
     * 记录当前在线连接数
     */
    private static AtomicInteger onlineCount = new AtomicInteger(0);

    /**
     * 存放所有在线的客户端
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 租户ID
     * -- GETTER --
     *  获取租户ID

     */
    @Getter
    private Long tenantId;

    /**
     * 用户ID
     * -- GETTER --
     *  获取用户ID

     */
    @Getter
    private Long userId;

    /**
     * TokenService注入
     */
    private static TokenService tokenService;

    @Autowired
    public void setTokenService(TokenService tokenService) {
        WebSocketServer.tokenService = tokenService;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        try {
            // 验证token
            if (StringUtils.isEmpty(token)) {
                session.close();
                log.error("WebSocket连接失败：token不能为空");
                return;
            }

            // 验证token有效性
            if (tokenService == null) {
                session.close();
                log.error("WebSocket连接失败：TokenService未注入");
                return;
            }

            // 从token中获取用户信息
            String tenantIdStr = JwtUtil.getEnterpriseId(token);
            String userIdStr = JwtUtil.getUserId(token);
            
            if (StringUtils.isEmpty(tenantIdStr) || StringUtils.isEmpty(userIdStr)) {
                session.close();
                log.error("WebSocket连接失败：token中缺少租户ID或用户ID");
                return;
            }

            // 验证token是否有效（检查Redis中是否存在）
            try {
                tokenService.getLoginUser(token);
            } catch (Exception e) {
                session.close();
                log.error("WebSocket连接失败：token无效或已过期，tenantId: {}, userId: {}", tenantIdStr, userIdStr);
                return;
            }

            // 解析ID
            this.tenantId = Long.parseLong(tenantIdStr);
            this.userId = Long.parseLong(userIdStr);
            String connectionKey = getConnectionKey(this.tenantId, this.userId);

            // 处理连接
            this.session = session;
            if (webSocketMap.containsKey(connectionKey)) {
                webSocketMap.remove(connectionKey);
                webSocketMap.put(connectionKey, this);
            } else {
                webSocketMap.put(connectionKey, this);
                addOnlineCount();
            }
            
            log.info("用户连接成功，tenantId: {}, userId: {}, 当前在线人数: {}", tenantId, userId, getOnlineCount());
            sendMessage("连接成功");
            
        } catch (Exception e) {
            log.error("WebSocket连接异常", e);
            try {
                session.close();
            } catch (IOException ex) {
                log.error("关闭WebSocket连接异常", ex);
            }
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        String connectionKey = getConnectionKey(this.tenantId, this.userId);
        if (webSocketMap.containsKey(connectionKey)) {
            webSocketMap.remove(connectionKey);
            subOnlineCount();
        }
        log.info("用户退出，tenantId: {}, userId: {}, 当前在线人数: {}", tenantId, userId, getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     * @param session 会话
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("用户消息，tenantId: {}, userId: {}, 报文: {}", tenantId, userId, message);
        if (StringUtils.isNotEmpty(message)) {
            try {
                // 处理心跳消息
                if (message.contains("PING")) {
                    String connectionKey = getConnectionKey(this.tenantId, this.userId);
                    if (webSocketMap.containsKey(connectionKey)) {
                        sendInfo("PONG", this.tenantId, this.userId);
                    }
                    return;
                }
                // 判断是否需要发送给指定用户
                if (message.startsWith("to:")) {
                    String[] parts = message.split(":", 3);
                    if (parts.length == 3) {
                        String toUser = parts[1];
                        String content = parts[2];
                        // 这里可以根据需要解析toUser，暂时发送给所有用户
                        sendInfo(content, null, null);
                    }
                // 群发消息    
                } else {
                    sendInfo(message, null, null);
                }
            } catch (Exception e) {
                log.error("处理消息异常: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 发生错误时间
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("用户错误，tenantId: {}, userId: {}, 原因: {}", this.tenantId, this.userId, error.getMessage());
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    public synchronized void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 发送自定义消息
     */
    public static void sendInfo(String message, Long tenantId, Long userId) throws IOException {
        log.info("发送消息，tenantId: {}, userId: {}, 报文: {}", tenantId, userId, message);
        
        if (tenantId != null && userId != null) {
            // 发送给指定用户
            String connectionKey = getConnectionKey(tenantId, userId);
            if (webSocketMap.containsKey(connectionKey)) {
                webSocketMap.get(connectionKey).sendMessage(message);
            }
        } else {
            // 群发消息
            for (WebSocketServer webSocketServer : webSocketMap.values()) {
                try {
                    webSocketServer.sendMessage(message);
                } catch (IOException e) {
                    log.error("发送消息失败，tenantId: {}, userId: {}", 
                             webSocketServer.getTenantId(), webSocketServer.getUserId(), e);
                }
            }
        }
    }

    /**
     * 获取连接键
     */
    private static String getConnectionKey(Long tenantId, Long userId) {
        return tenantId + "-" + userId;
    }

    public static synchronized int getOnlineCount() {
        return onlineCount.get();
    }
    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount.getAndIncrement();
    }
    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount.getAndDecrement();
    }
}

