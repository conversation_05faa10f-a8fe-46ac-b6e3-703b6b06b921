package com.heju.system.entity.domain.query;

import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体税务人员信息 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityTaxationUserQuery extends SysEntityTaxationUserPo {

    @Serial
    private static final long serialVersionUID = 1L;
}