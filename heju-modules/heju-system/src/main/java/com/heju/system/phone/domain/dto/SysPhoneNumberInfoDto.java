package com.heju.system.phone.domain.dto;

import com.heju.system.phone.domain.po.SysPhoneNumberInfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 手机号 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysPhoneNumberInfoDto extends SysPhoneNumberInfoPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 保管员名称
     */
    private String nickName;

}
