package com.heju.system.forms.sheet.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 表单管理 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_sheet")
public class SysSheetPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 表单类型（1-系统业务表单；2-自定义表单） */
    @Excel(name = "表单类型", readConverterExp = "1-系统业务表单；2-自定义表单")
    protected Integer sheetType;

    /** API名称 */
    @Excel(name = "API名称")
    protected String apiName;

    /** 描述 */
    @Excel(name = "描述")
    protected String remark;

    /**
     * 是否可设置为选项 0-是，1-否
     */
    @Excel(name = "是否可设置为选项")
    protected Integer isOption;
}