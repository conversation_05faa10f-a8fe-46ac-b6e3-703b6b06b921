package com.heju.system.report.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.system.ReportConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.service.impl.SysEntityServiceImpl;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysBankReportQuery;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import com.heju.system.report.mapper.SysBankReportMapper;
import com.heju.system.report.service.ISysBankReportService;
import com.heju.system.report.manager.ISysBankReportManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 银行报表管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysBankReportServiceImpl extends BaseServiceImpl<SysBankReportQuery, SysBankReportDto, ISysBankReportManager> implements ISysBankReportService {

    @Autowired
    SysBankReportMapper bankReportMapper;

    @Autowired
    SysEntityServiceImpl entityService;


    /**
     * 查询银行报表对象列表 | 数据权限
     *
     * @param bankReport 银行报表对象
     * @return 银行报表对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysBankReportMapper"})
    public List<SysBankReportDto> selectListScope(SysBankReportQuery bankReport) {
        List<SysBankReportDto> sysBankReportDtos = baseManager.selectList(bankReport);
        for (SysBankReportDto sysBankReportDto : sysBankReportDtos) {
            sysBankReportDto.setEntityName(entityService.selectById(sysBankReportDto.getEntityId()).getName());
        }
        return sysBankReportDtos;
    }


    /**
     * 新增数据对象(重写)
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysBankReportDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        SysEntityPo sysEntityPo = entityService.selectById(dto.getEntityId());
        dto.setEntityName(sysEntityPo.getName());
        String timeType = dto.getReporttimeType();
        if (timeType.equals(ReportConstants.TimeType.MONTH.getCode())) {
            //月度
            String yMd = dto.getMonth().split("T")[0];
            String[] end = yMd.split("-");
            dto.setYear(end[0]);
            dto.setMonth(end[1]);
        }
        if (dto.getSeason() != null && timeType.equals(ReportConstants.TimeType.SEASON.getCode())) {
            //季度
            String[] yearAndSeason = dto.getSeason().split("-");
            dto.setYear(yearAndSeason[0]);
            int season = Character.getNumericValue(yearAndSeason[1].toCharArray()[1]) - 1;
            dto.setSeason(String.valueOf(season));
        }
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        return row;
    }

    /**
     * 修改数据对象（重写）
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysBankReportDto dto) {
        SysBankReportDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        SysEntityPo sysEntityPo = entityService.selectById(dto.getEntityId());
        String name = sysEntityPo.getName();
        dto.setEntityName(name);
        String timeType = dto.getReporttimeType();
        if (timeType.equals(ReportConstants.TimeType.SEASON.getCode())) {
            //时间类型为季度
            if (dto.getSeason() != null) {
                String[] yearAndSeason = dto.getSeason().split("-");
                dto.setYear(yearAndSeason[0]);
                int season = Character.getNumericValue(yearAndSeason[1].toCharArray()[1]) - 1;
                dto.setSeason(String.valueOf(season));
                dto.setMonth(null);
            }
        } else if (timeType.equals(ReportConstants.TimeType.MONTH.getCode())) {
            //时间类型为月度
            String yMd = dto.getMonth().split("T")[0];
            String[] end = yMd.split("-");
            dto.setYear(end[0]);
            dto.setMonth(end[1]);
            dto.setSeason(null);
        } else if (timeType.equals(ReportConstants.TimeType.YEARS.getCode())) {
            //时间类型为年度
            String year = dto.getYear().split("-")[0];
            dto.setYear(year);
            dto.setMonth(null);
            dto.setSeason(null);
        }

        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        return row;
    }

    public List<SysBankReportDto> findAll(SysReportManagementQuery query) {
        return bankReportMapper.findAll(query);
    }

    /**
     * 根据Id查询单条数据对象（重写）
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysBankReportDto selectById(Serializable id) {
        SysBankReportDto dto = baseManager.selectById(id);
        dto.setEntityName(entityService.selectById(dto.getEntityId()).getName());
        return subCorrelates(dto);
    }

    public List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query) {
        return bankReportMapper.selectByCondition(query);
    }
}