package com.heju.system.declaration.domain.dto;

import com.heju.system.declaration.domain.po.SysBusinessAnnualReportPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工商年报 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysBusinessAnnualReportDto extends SysBusinessAnnualReportPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private String entityName;

    private String declareBy;

}