package com.heju.system.forms.sheet.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.query.SysSheetQuery;
import com.heju.system.forms.sheet.service.ISysSheetService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单管理管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sheet")
public class SysSheetController extends BaseController<SysSheetQuery, SysSheetDto, ISysSheetService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "表单管理" ;
    }

    /**
     * 查询表单管理列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_SHEET_LIST)
    public AjaxResult list(SysSheetQuery sheet) {
        return super.list(sheet);
    }

    /**
     * 查询表单管理详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_SHEET_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 表单管理新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_SHEET_ADD)
    @Log(title = "表单管理管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysSheetDto sheet) {
        return super.add(sheet);
    }

    /**
     * 表单管理修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_SHEET_EDIT)
    @Log(title = "表单管理管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysSheetDto sheet) {
        return super.edit(sheet);
    }

    /**
     * 表单管理修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_SHEET_EDIT, Auth.SYS_SHEET_ES}, logical = Logical.OR)
    @Log(title = "表单管理管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysSheetDto sheet) {
        return super.editStatus(sheet);
    }

    /**
     * 表单管理批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_SHEET_DEL)
    @Log(title = "表单管理管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取表单管理选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 获取业务选项选择框列表
     */
    @GetMapping("/optionList")
    public AjaxResult optionList(SysSheetQuery query) {
        return baseService.optionList(query);
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysSheetDto sheet) {
        if (baseService.checkConfigCodeUnique(sheet.getId(), sheet.getApiName()))
            warn(StrUtil.format("{}{}失败，{}API名称已存在", operate.getInfo(), getNodeName(), sheet.getApiName()));
    }


    /**
     * 前置校验 （强制）删除
     */
    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        List<SysFieldPo> sysFieldPos = baseService.selectFieldBySheetId(idList);
        if(!sysFieldPos.isEmpty()) {
            Map<Long, List<SysFieldPo>> collect = sysFieldPos.stream().collect(Collectors.groupingBy(SysFieldPo::getSheetId));
            List<Long> ids=new ArrayList<>();
            for (Long sheetId : collect.keySet()) {
                List<SysFieldPo> sysFieldList = collect.get(sheetId);
                if(sysFieldList.size()>1){
                    ids.add(sheetId);
                }
            }
            if(!ids.isEmpty()) {
                String names = baseService.selectByIds(ids);
                warn(StrUtil.format("{}{}失败，{}存在字段", operate.getInfo(), getNodeName(), names));
            }
        }
    }

}
