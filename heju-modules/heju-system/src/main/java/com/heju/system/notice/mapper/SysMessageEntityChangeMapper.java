package com.heju.system.notice.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import com.heju.system.notice.domain.query.SysMessageEntityChangeQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import java.io.Serializable;

/**
 * 消息通知管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysMessageEntityChangeMapper extends BaseMapper<SysMessageEntityChangeQuery, SysMessageEntityChangeDto, SysMessageEntityChangePo> {
    @Insert("insert into sys_message_entity_change (entity_id, change_field, change_before, change_after) " +
            "values (#{entityId},#{changeField},#{changeBefore},#{changeAfter})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertEntityChange(SysMessageEntityChangeDto entityChange);

    @Select("select * from sys_message_entity_change where id = #{id}")
    SysMessageEntityChangeDto getById(Serializable id);
}
