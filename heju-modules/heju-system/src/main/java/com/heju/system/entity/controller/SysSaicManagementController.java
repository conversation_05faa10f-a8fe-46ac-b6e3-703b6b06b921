package com.heju.system.entity.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.system.entity.domain.query.SysEntityQuery;
import com.heju.system.entity.service.ISysEntityService;
import com.heju.system.entity.service.ISysSaicManagementService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/Saic")
public class SysSaicManagementController {

    @Resource
    private ISysEntityService entityService;

    @Resource
    private ISysSaicManagementService sysSaicManagementService;


    /**
     * 查询实体信息管理列表
     */
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ENTITY_LIST)
    public AjaxResult list(SysEntityQuery entityQuery) {
        return entityService.list(entityQuery);
    }

    /**
     * 查询工商信息管理详情
     */
    @GetMapping("/getInfo")
    @RequiresPermissions(Auth.SYS_SAIC_SINGLE)
    public AjaxResult getInfo(SysEntityQuery entityQuery){
        return sysSaicManagementService.getInfo(entityQuery);
    }

    /**
     * 工商信息管理修改
     */
    @PutMapping
    @Log(title = "工商信息管理管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions(Auth.SYS_SAIC_EDIT)
    public AjaxResult edit(@RequestBody String entityJson) {
        return sysSaicManagementService.edit(entityJson);
    }
}
