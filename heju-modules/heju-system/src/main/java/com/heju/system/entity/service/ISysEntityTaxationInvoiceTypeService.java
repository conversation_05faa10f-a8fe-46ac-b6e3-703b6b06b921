package com.heju.system.entity.service;

import com.heju.system.entity.domain.query.SysEntityTaxationInvoiceTypeQuery;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.common.web.entity.service.IBaseService;

/**
 * 实体税务票种认定管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysEntityTaxationInvoiceTypeService extends IBaseService<SysEntityTaxationInvoiceTypeQuery, SysEntityTaxationInvoiceTypeDto> {
}