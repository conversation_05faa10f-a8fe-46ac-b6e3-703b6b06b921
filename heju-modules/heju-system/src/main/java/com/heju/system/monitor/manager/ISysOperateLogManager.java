package com.heju.system.monitor.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.api.log.domain.dto.SysOperateLogDto;
import com.heju.system.api.log.domain.query.SysOperateLogQuery;

/**
 * 操作日志管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysOperateLogManager extends IBaseManager<SysOperateLogQuery, SysOperateLogDto> {

    /**
     * 清空系统操作日志
     */
    void cleanOperateLog();
}
