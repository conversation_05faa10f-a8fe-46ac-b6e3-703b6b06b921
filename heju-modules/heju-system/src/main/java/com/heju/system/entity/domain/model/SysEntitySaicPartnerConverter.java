package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import com.heju.system.entity.domain.query.SysEntitySaicPartnerQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体工商股东 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntitySaicPartnerConverter extends BaseConverter<SysEntitySaicPartnerQuery, SysEntitySaicPartnerDto, SysEntitySaicPartnerPo> {
}
