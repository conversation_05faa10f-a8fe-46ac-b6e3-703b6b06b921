package com.heju.system.third.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.domain.SlaveRelation;
import com.heju.system.company.domain.merge.SysCompanyThirdMerge;
import com.heju.system.company.mapper.merge.SysCompanyThirdMergeMapper;
import com.heju.system.third.domain.po.SysThirdPo;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.query.SysThirdQuery;
import com.heju.system.third.domain.model.SysThirdConverter;
import com.heju.system.third.mapper.SysThirdMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.third.manager.ISysThirdManager;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.heju.system.api.organize.domain.merge.MergeGroup.Third_SysCompanyThirdMerge_GROUP;

/**
 * 第三方模块管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysThirdManager extends BaseManagerImpl<SysThirdQuery, SysThirdDto, SysThirdPo, SysThirdMapper, SysThirdConverter> implements ISysThirdManager {

    /**
     * 初始化从属关联关系
     *
     * @return 关系对象集合
     */
    protected List<SlaveRelation> subRelationInit() {
        return new ArrayList<>(){{
            add(new SlaveRelation(Third_SysCompanyThirdMerge_GROUP, SysCompanyThirdMergeMapper.class, SysCompanyThirdMerge.class, OperateConstants.SubOperateLimit.ONLY_DEL));
        }};
    }

    @Override
    public SysThirdDto checkThirdCodeUnique(Long Id, String code) {

        SysThirdPo company = baseMapper.selectOne(
                Wrappers.<SysThirdPo>query().lambda()
                        .ne(SysThirdPo::getId, Id)
                        .eq(SysThirdPo::getCode, code)
                        .last(SqlConstants.LIMIT_ONE));
        return mapperDto(company);
    }
}