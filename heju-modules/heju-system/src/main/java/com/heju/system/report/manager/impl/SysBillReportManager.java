package com.heju.system.report.manager.impl;

import com.heju.system.report.domain.po.SysBillReportPo;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.query.SysBillReportQuery;
import com.heju.system.report.domain.model.SysBillReportConverter;
import com.heju.system.report.mapper.SysBillReportMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.report.manager.ISysBillReportManager;
import org.springframework.stereotype.Component;

/**
 * 发票管理详情管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysBillReportManager extends BaseManagerImpl<SysBillReportQuery, SysBillReportDto, SysBillReportPo, SysBillReportMapper, SysBillReportConverter> implements ISysBillReportManager {
}