package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationInvoiceTypePo;
import com.heju.system.entity.domain.query.SysEntityTaxationInvoiceTypeQuery;

/**
 * 实体税务票种认定管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityTaxationInvoiceTypeMapper extends BaseMapper<SysEntityTaxationInvoiceTypeQuery, SysEntityTaxationInvoiceTypeDto, SysEntityTaxationInvoiceTypePo> {
}