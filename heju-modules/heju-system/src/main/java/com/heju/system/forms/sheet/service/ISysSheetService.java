package com.heju.system.forms.sheet.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.sheet.domain.query.SysSheetQuery;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;

/**
 * 表单管理管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysSheetService extends IBaseService<SysSheetQuery, SysSheetDto> {

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    int insert(SysSheetDto dto);

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    int update(SysSheetDto dto);

    /**
     * 校验参数编码是否唯一
     *
     * @param Id      参数Id
     * @param apiName API名称
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkConfigCodeUnique(Long Id, String apiName);

    List<SysFieldPo> selectFieldBySheetId(List<Long> idList);

    String selectByIds(List<Long> ids);

    SysSheetDto selectOneByApiName(String apiName);

    AjaxResult optionList(SysSheetQuery query);
}
