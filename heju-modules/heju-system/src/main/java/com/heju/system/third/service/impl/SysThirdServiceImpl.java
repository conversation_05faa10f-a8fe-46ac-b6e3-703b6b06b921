package com.heju.system.third.service.impl;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.query.SysThirdQuery;
import com.heju.system.third.service.ISysThirdService;
import com.heju.system.third.manager.ISysThirdManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 第三方模块管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysThirdServiceImpl extends BaseServiceImpl<SysThirdQuery, SysThirdDto, ISysThirdManager> implements ISysThirdService {

    /**
     * 查询第三方模块对象列表 | 数据权限
     *
     * @param third 第三方模块对象
     * @return 第三方模块对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysThirdMapper"})
    public List<SysThirdDto> selectListScope(SysThirdQuery third) {
        return baseManager.selectList(third);
    }

    @Override
    public boolean checkThirdCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkThirdCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }
}