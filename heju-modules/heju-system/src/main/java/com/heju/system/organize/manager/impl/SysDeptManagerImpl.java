package com.heju.system.organize.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.domain.SlaveRelation;
import com.heju.common.web.entity.manager.impl.TreeManagerImpl;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.model.SysDeptConverter;
import com.heju.system.api.organize.domain.po.SysDeptPo;
import com.heju.system.api.organize.domain.query.SysDeptQuery;
import com.heju.system.api.authority.domain.po.SysOrganizeRoleMerge;
import com.heju.system.organize.domain.merge.SysRoleDeptMerge;
import com.heju.system.organize.manager.ISysDeptManager;
import com.heju.system.organize.mapper.SysDeptMapper;
import com.heju.system.organize.mapper.merge.SysOrganizeRoleMergeMapper;
import com.heju.system.organize.mapper.merge.SysRoleDeptMergeMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.heju.system.api.organize.domain.merge.MergeGroup.*;

/**
 * 部门管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysDeptManagerImpl extends TreeManagerImpl<SysDeptQuery, SysDeptDto, SysDeptPo, SysDeptMapper, SysDeptConverter> implements ISysDeptManager {

    /**
     * 初始化从属关联关系
     *
     * @return 关系对象集合
     */
    protected List<SlaveRelation> subRelationInit() {
        return new ArrayList<>(){{
            add(new SlaveRelation(DEPT_SysPost_GROUP, SysPostManagerImpl.class, OperateConstants.SubOperateLimit.EX_ADD_OR_EDIT));
            add(new SlaveRelation(DEPT_OrganizeRoleMerge_GROUP, SysOrganizeRoleMergeMapper.class, SysOrganizeRoleMerge.class, OperateConstants.SubOperateLimit.ONLY_DEL));
            add(new SlaveRelation(DEPT_SysRoleDeptMerge_GROUP, SysRoleDeptMergeMapper.class, SysRoleDeptMerge.class, OperateConstants.SubOperateLimit.ONLY_DEL));
        }};
    }

    /**
     * 校验部门编码是否唯一
     *
     * @param Id   部门Id
     * @param code 部门编码
     * @return 部门对象
     */
    @Override
    public SysDeptDto checkDeptCodeUnique(Long Id, String code) {
        SysDeptPo dept = baseMapper.selectOne(
                Wrappers.<SysDeptPo>query().lambda()
                        .ne(SysDeptPo::getId, Id)
                        .eq(SysDeptPo::getCode, code)
                        .last(SqlConstants.LIMIT_ONE));
        return mapperDto(dept);
    }

}
