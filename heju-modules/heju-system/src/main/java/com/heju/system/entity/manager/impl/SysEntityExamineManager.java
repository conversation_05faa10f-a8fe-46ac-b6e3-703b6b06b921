package com.heju.system.entity.manager.impl;

import com.heju.system.entity.domain.po.SysEntityExaminePo;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.query.SysEntityExamineQuery;
import com.heju.system.entity.domain.model.SysEntityExamineConverter;
import com.heju.system.entity.mapper.SysEntityExamineMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntityExamineManager;
import org.springframework.stereotype.Component;

/**
 * 实体信息审核管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntityExamineManager extends BaseManagerImpl<SysEntityExamineQuery, SysEntityExamineDto, SysEntityExaminePo, SysEntityExamineMapper, SysEntityExamineConverter> implements ISysEntityExamineManager {
}