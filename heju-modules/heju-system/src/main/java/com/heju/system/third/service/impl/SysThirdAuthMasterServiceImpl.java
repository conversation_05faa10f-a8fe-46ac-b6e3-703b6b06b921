package com.heju.system.third.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.pool.NumberPool;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.service.ISysCompanyService;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.manager.ISysThirdAuthMasterManager;
import com.heju.system.third.mapper.SysThirdAuthMapper;
import com.heju.system.third.service.ISysThirdAuthMasterService;
import com.heju.system.third.service.ISysThirdAuthService;
import com.heju.system.utils.OperateEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 第三方认证管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysThirdAuthMasterServiceImpl extends BaseServiceImpl<SysThirdAuthQuery, SysThirdAuthDto, ISysThirdAuthMasterManager> implements ISysThirdAuthMasterService {

    @Resource
    private SysThirdAuthMapper sysThirdAuthMapper;

    @Resource
    private ISysCompanyService sysCompanyService;

    @Resource
    private ISysThirdAuthService thirdAuthService;

    /**
     * 缓存主键命名定义
     */
    @Override
    protected String getCacheKey() {
        return CacheConstants.CacheType.SYS_THIRD_AUTH_KEY.getCode();
    }

    /**
     * 查询第三方认证对象列表 | 数据权限
     *
     * @param thirdAuth 第三方认证对象
     * @return 第三方认证对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysThirdAuthMapper"})
    public List<SysThirdAuthDto> selectListScope(SysThirdAuthQuery thirdAuth) {
        return baseManager.selectList(thirdAuth);
    }

    @Override
    public boolean checkThirdAuthCodeUnique(Long Id, String code) {
        return ObjectUtil.isNotNull(baseManager.checkThirdAuthCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, code));
    }

    @Override
    public List<SysThirdAuthDto> optionByThirdId(Long thirdId) {
        SysThirdAuthQuery sysThirdAuthQuery = new SysThirdAuthQuery();
        sysThirdAuthQuery.setThirdId(thirdId);
        return baseManager.selectList(sysThirdAuthQuery);
    }

    /**
     * 超管 认证信息查询
     *
     * @param thirdId 第三方模块id
     * @return 该第三方关联所有认证的公司信息
     */
    @Override
    public List<SysCompanyThirdAuthMergeDto> getTenantCpmListAdmin(Long thirdId) {
        Map<String, SysCompanyDto> companyMap = sysCompanyService.getCompanyRedisList();
        Map<String, SysThirdAuthDto> thirdAuthMap = thirdAuthService.getThirdAuthRedis();
        Map<String, String> thirdAuthCompanyMap = thirdAuthService.getCompanyThirdAuthRedis();
        //企业信息数据 先根据third筛选一遍
        List<SysCompanyThirdAuthMergeDto> companyThirdAuthConfigureList = new ArrayList<>();
        if (companyMap.size() > 0) {
            companyMap.entrySet().removeIf(entry -> !Arrays.asList(entry.getValue().getThirdIds()).contains(thirdId));
            for (Map.Entry<String, SysThirdAuthDto> entry : thirdAuthMap.entrySet()) {
                SysThirdAuthDto thirdAuthDto = entry.getValue();
                if (thirdAuthDto.getThirdId().equals(thirdId)) {
                    Object o = thirdAuthCompanyMap.get(String.valueOf(thirdAuthDto.getId()));
                    if (!ObjectUtil.isNull(o) && !StringUtils.isEmpty(o.toString())) {
                        for (String companyId : o.toString().split(",")) {
                            if (thirdAuthDto.getIsAdmin().equals(String.valueOf(NumberPool.Zero))) {
                                SysCompanyThirdAuthMergeDto companyThirdAuthConfigure = new SysCompanyThirdAuthMergeDto();
                                BeanUtils.copyProperties(companyMap.get(companyId), companyThirdAuthConfigure);
                                companyThirdAuthConfigure.setThirdAuthId(thirdAuthDto.getId());
                                companyThirdAuthConfigure.setThirdAuthName(thirdAuthDto.getName());
                                companyThirdAuthConfigureList.add(companyThirdAuthConfigure);
                            }
                            //删除已添加过的公司信息
                            companyMap.remove(companyId);
                        }
                    }
                }
            }
            List<Object> valueList = new ArrayList<>(companyMap.values());
            companyThirdAuthConfigureList.addAll(JSONObject.parseArray(JSONObject.toJSONString(valueList), SysCompanyThirdAuthMergeDto.class));
            companyThirdAuthConfigureList.stream().sorted(Comparator.comparing(SysCompanyThirdAuthMergeDto::getSort));
        }
        return companyThirdAuthConfigureList;
    }

    /**
     * 超管配置认证信息
     *
     * @param dto 公司第三方认证关联信息
     * @return 该第三方关联所有认证的公司信息
     */
    @Override
    @DSTransactional
    public int addTenantCpmAuthAdmin(SysCompanyThirdAuthMergeAddDto dto) {
        Long thirdAuthId = dto.getThirdAuthId();
        Long thirdId = dto.getThirdId();
        //查询所有的数据源
        Map<String, Object> tenantMap = sysCompanyService.getCompanyTenantRedisList();
        //前端传入的认证信息
        List<SysCompanyThirdAuthMergeDto> companyIdListAfter = dto.getCompanyThirdAuthList();
        //新增集合
        HashMap<Long, SysCompanyThirdAuthMergeDto> companyIdListAfterMap = companyIdListAfter.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getId(), item),
                        HashMap::putAll);
        //包含数据源的企业集合
        List<SysCompanyThirdAuthMerge> companyIdListBySource = new ArrayList<>();
        //遍历赋值数据源名称
        for (SysCompanyThirdAuthMergeDto comList : companyIdListAfter) {
            SysCompanyThirdAuthMerge comDto = new SysCompanyThirdAuthMerge();
            comDto.setCompanyId(comList.getId());
            comDto.setThirdAuthId(thirdAuthId);
            comDto.setThirdId(thirdId);
            comDto.setSourceName(String.valueOf(tenantMap.get(String.valueOf(comList.getId()))));
            companyIdListBySource.add(comDto);
        }
        Map<String, List<SysCompanyThirdAuthMerge>> companyIdListByMap = companyIdListBySource
                .stream()
                .filter(map -> map.getSourceName() != null)
                .collect(Collectors.groupingBy(SysCompanyThirdAuthMerge::getSourceName));
        Map<String, List<Long>> delCompanyIds = new HashMap<>();
        Map<String, List<Long>> addCompanyIds = new HashMap<>();
        for (Map.Entry<String, List<SysCompanyThirdAuthMerge>> next : companyIdListByMap.entrySet()) {
            SecurityContextHolder.setSourceName(next.getKey());
            List<Long> companyIdList = sysThirdAuthMapper.selectCpmAuthByAuthId(thirdAuthId);
            List<Long> companyIdAfterList = companyIdListAfter.stream().map(SysCompanyThirdAuthMergeDto::getCompanyId).toList();
            List<Long> delId = companyIdList.stream().filter(companyId -> !companyIdAfterList.contains(companyId)).toList();
            delCompanyIds.put(next.getKey(), delId);
            List<Long> addId = companyIdAfterList.stream().filter(companyId -> !companyIdList.contains(companyId)).toList();
            addCompanyIds.put(next.getKey(), addId);
            sysThirdAuthMapper.delTenantCpmAuthByAuthId(thirdAuthId);
            sysThirdAuthMapper.addTenantCpmAuth(next.getValue());
        }
        Set<Long> companyIdSet = companyIdListAfterMap.keySet();
        String companyId = StringUtils.join(companyIdSet, ",");
        //修改redis
        thirdAuthService.setCompanyThirdAuthRedis(thirdAuthId, companyId);
        //推送谷云
        baseManager.send2RestCloud(delCompanyIds, OperateEnum.DELETE.getCode(), thirdAuthId);
        baseManager.send2RestCloud(addCompanyIds, OperateEnum.ADD.getCode(), thirdAuthId);
        return 0;
    }
}