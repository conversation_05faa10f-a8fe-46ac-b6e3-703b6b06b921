package com.heju.system.company.domain.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.web.tenant.base.TBasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_company_third_auth_merge")
public class SysCompanyThirdAuthMerge extends TBasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    protected Long id;

    private Long companyId;

    private Long thirdAuthId;

    private Long thirdId;

}
