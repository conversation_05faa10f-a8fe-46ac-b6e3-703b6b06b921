package com.heju.system.authority.controller;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.system.authority.domain.query.SysDisplayInfoQuery;
import com.heju.system.authority.service.ISysDisplayInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 显隐列管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/display")
public class SysDisplayInfoController extends BaseController<SysDisplayInfoQuery, SysDisplayInfoDto, ISysDisplayInfoService> {

    @Autowired ISysDisplayInfoService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "显隐列" ;
    }

    /**
     * 查询显隐列列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_DISPLAY_INFO_LIST)
    public AjaxResult list(SysDisplayInfoQuery displayInfo) {
        return super.list(displayInfo);
    }

    /**
     * 查询显隐列详细
     */
    @Override
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(Auth.SYS_DISPLAY_INFO_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 显隐列新增
     */
    @Override
    @PostMapping
//    @RequiresPermissions(Auth.SYS_DISPLAY_INFO_ADD)
    @Log(title = "显隐列管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysDisplayInfoDto displayInfo) {
        return super.add(displayInfo);
    }


    /**
     * 显隐列修改
     */
    @Override
    @PutMapping
//    @RequiresPermissions(Auth.SYS_DISPLAY_INFO_EDIT)
    @Log(title = "显隐列管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysDisplayInfoDto displayInfo) {
        return super.edit(displayInfo);
    }

    /**
     * 显隐列  新增/修改
     */
    @PostMapping("/addAndModify")
//    @RequiresPermissions(Auth.SYS_DISPLAY_INFO_ADD)
    @Log(title = "显隐列管理", businessType = BusinessType.INSERT)
    public AjaxResult addAndModify(@Validated({V_A.class}) @RequestBody SysDisplayInfoDto displayInfo) {
        return toAjax(service.addAndModify(displayInfo));
    }

    /**
     * 显隐列批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
//    @RequiresPermissions(Auth.SYS_DISPLAY_INFO_DEL)
    @Log(title = "显隐列管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取显隐列选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


    /**
     * api_code 校验
     * @param operate 操作类型
     * @param displayInfo     数据对象
     */
//    @Override
//    protected void AEHandle(BaseConstants.Operate operate, SysDisplayInfoDto displayInfo) {
//        if (baseService.checkNameUnique(displayInfo.getId(), displayInfo.getApiCode()))
//            warn(StrUtil.format("{}{}{}失败，表api_code已存在", operate.getInfo(), getNodeName(), displayInfo.getApiCode()));
//    }

}
