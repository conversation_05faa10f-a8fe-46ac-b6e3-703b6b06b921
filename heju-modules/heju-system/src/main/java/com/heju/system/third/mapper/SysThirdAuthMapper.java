package com.heju.system.third.mapper;

import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.po.SysThirdAuthPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 第三方认证管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysThirdAuthMapper extends BaseMapper<SysThirdAuthQuery, SysThirdAuthDto, SysThirdAuthPo> {
    List<SysCompanyThirdAuthMergeDto> getTenantCpmList(Long thirdId);

    int addTenantCpmAuth(List<SysCompanyThirdAuthMerge> listAdd);
    int delTenantCpmAuth(List<SysCompanyThirdAuthMerge> listDel);

    //根据thirdAuthId查询认证信息
    List<SysCompanyThirdAuthMergeDto> getTenantCpmListByAuthId(SysCompanyThirdAuthMergeAddDto dto);
    @Delete("delete from sys_company_third_auth_merge where third_auth_id =#{thirdAuthId}")
    int delTenantCpmAuthByAuthId(Long thirdAuthId);

    @Select("select company_id from sys_company_third_auth_merge where third_auth_id =#{thirdAuthId}")
    List<Long> selectCpmAuthByAuthId(Long thirdAuthId);



//    @Delete("<script>" +
//            "delete from sys_company_third_auth_merge" +
//            "<where>" +
//            "third_auth_id in  (" +
//            "   <foreach collection=\"idList\" open=\"\" close=\"\" separator=\",\" item=\"item\"> " +
//            "      #{item} " +
//            "   </foreach> " +
//            " ) " +
//            "</where>" +
//            "</script>")
//    int delMergeById(List<Long> idList);

    @Select("<script>" +
            "select id,third_auth_id,company_id,third_id from sys_company_third_auth_merge a where company_id=#{companyId} and third_id in " +
            "<foreach collection= 'thirdDtoList' open='(' close=')' separator=',' item='item' >" +
            " #{item.id}" +
            "</foreach>" +
            "</script>")
    List<SysCompanyThirdAuthMerge> selectThirdAuthId(@Param("companyId") Long companyId,@Param("thirdDtoList") List<SysThirdDto> thirdDtoList);

    @Delete("<script>" +
            "delete from sys_company_third_auth_merge where id in" +
            "<foreach collection= 'companyThirdAuthMergeList' open='(' close=')' separator=',' item='item' >" +
            " #{item.id}" +
            "</foreach>" +
            "</script>")
    int deleteCompanyThirdAuthMerge(@Param("companyThirdAuthMergeList") List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList);

    @Select("<script>" +
            "select id,third_auth_id,company_id,third_id from sys_company_third_auth_merge where  company_id in " +
            "<foreach collection= 'idList' open='(' close=')' separator=',' item='item' >" +
            " #{item}" +
            "</foreach>" +
            "</script>")
    List<SysCompanyThirdAuthMerge> getByCompanyIds(@Param("idList") List<Long> idList);
}