package com.heju.system.authority.controller;
import com.heju.common.core.utils.TreeUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BasisController;
import com.heju.system.api.authority.domain.dto.SysTenantMenuDto;
import com.heju.system.authority.domain.vo.SysAuthTree;
import com.heju.system.authority.service.ISysAuthService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权限管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
public class SysAuthController extends BasisController {

    @Resource
    private ISysAuthService authService;

    /**
     * 获取租户权限 | 叶子节点 | 内部调用
     */
    @InnerAuth
    @GetMapping("/inner/getTenantAuth")
    public R<Long[]> getTenantAuthInner() {
        List<SysAuthTree> leafNodes = TreeUtil.getLeafNodes(TreeUtil.buildTree(authService.selectTenantAuth()));
        return R.ok(leafNodes.stream().map(SysAuthTree::getId).toArray(Long[]::new));
    }

    /**
     * 租户删除菜单
     */
    @DeleteMapping("/inner/tenantDeleteMenus")
    public R<Boolean> tenantDeleteMenus(@RequestBody List<Long> menuIdList){
        authService.tenantDeleteMenus(menuIdList);
        return R.ok();
    }

    /**
     * 租户更新菜单
     */
    @PostMapping("/inner/tenantUpdateMenus")
    public R<Boolean> tenantUpdateMenus(@RequestBody SysTenantMenuDto sysTenantMenuDto){
        authService.tenantUpdateMenus(sysTenantMenuDto);
        return R.ok();
    }

    /**
     * 从查询租户已有菜单
     */
    @GetMapping("inner/tenantHasMenuIds")
    public List<Long> tenantHasMenuIds(@RequestParam(value = "tenantId") Long tenantId){
        return authService.tenantHasMenuIds(tenantId);
    }

    /**
     * 新增租户权限 | 内部调用
     */
    @InnerAuth
    @PostMapping("/inner/addTenantAuth")
    public R<Boolean> addTenantAuthInner(@RequestBody String authIdsString,Long enterpriseId) {
        String[] authIdsStringArray = authIdsString.split(",");
        Long[] authIds = new Long[authIdsStringArray.length];
        for (int i = 0; i < authIdsStringArray.length; i++) {
            authIds[i] = Long.parseLong(authIdsStringArray[i].trim());
        }
        authService.addTenantAuth(authIds,enterpriseId);
        return R.ok();
    }

    /**
     * 修改租户权限 | 内部调用
     */
    @InnerAuth
    @PostMapping("/inner/editTenantAuth")
    public R<Boolean> editTenantAuthInner(@RequestBody Long[] authIds) {
        authService.editTenantAuth(authIds);
        return R.ok();
    }

    /**
     * 获取公共模块|菜单权限树
     */
    @GetMapping(value = "/tenant/authScope")
    @RequiresPermissions(value = {Auth.TE_TENANT_ADD, Auth.TE_TENANT_AUTH}, logical = Logical.OR)
    public AjaxResult getCommonAuthScope() {
        return success(TreeUtil.buildTree(authService.selectCommonAuthScope()));
    }

    /**
     * 获取企业模块|菜单权限树
     */
    @GetMapping(value = "/enterprise/authScope")
    @RequiresPermissions(value = {Auth.SYS_ROLE_ADD, Auth.SYS_ROLE_AUTH}, logical = Logical.OR)
    public AjaxResult getEnterpriseAuthScope() {
        return success(TreeUtil.buildTree(authService.selectEnterpriseAuthScope()));
    }

    /**
     * 获取角色组菜单权限树
     */
    @GetMapping(value = "/roleGroup/authScope")
    @RequiresPermissions(value = {Auth.SYS_ROLE_ADD, Auth.SYS_ROLE_AUTH}, logical = Logical.OR)
    public AjaxResult getRoleGroupAuthScope(Long id) {
        return success(TreeUtil.buildTree(authService.selectRoleGroupAuthScope(id)));
    }

}
