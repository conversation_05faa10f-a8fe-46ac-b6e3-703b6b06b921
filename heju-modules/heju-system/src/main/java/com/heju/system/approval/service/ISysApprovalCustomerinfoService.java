package com.heju.system.approval.service;


import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;

/**
 * 客户信息审核管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysApprovalCustomerinfoService extends IBaseService<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto> {
    int pass(SysApprovalCustomerinfoDto approvalCustomerinfo);

    int reject(SysApprovalCustomerinfoDto approvalCustomerinfo);
}