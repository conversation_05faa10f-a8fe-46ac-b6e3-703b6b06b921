package com.heju.system.forms.field.domain.merge;

import com.baomidou.mybatisplus.annotation.*;
import com.heju.common.core.web.tenant.base.TBasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 字段角色关系 持久化对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_field_role_merge")
public class SysFieldRoleMerge extends TBasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId
    protected Long id;

    /** 字段id */
    protected Long fieldId;

    /** 角色id */
    protected Long roleId;

    /** 表单id **/
    protected Long sheetId;

}