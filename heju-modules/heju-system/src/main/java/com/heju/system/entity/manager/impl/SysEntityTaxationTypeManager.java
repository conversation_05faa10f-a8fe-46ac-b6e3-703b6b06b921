package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import com.heju.system.entity.domain.query.SysEntityTaxationTypeQuery;
import com.heju.system.entity.domain.model.SysEntityTaxationTypeConverter;
import com.heju.system.entity.mapper.SysEntityTaxationTypeMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntityTaxationTypeManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体税务税费种认定管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntityTaxationTypeManager extends BaseManagerImpl<SysEntityTaxationTypeQuery, SysEntityTaxationTypeDto, SysEntityTaxationTypePo, SysEntityTaxationTypeMapper, SysEntityTaxationTypeConverter> implements ISysEntityTaxationTypeManager {
    @Override
    public List<SysEntityTaxationTypePo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntityTaxationTypePo> lambdaType = new LambdaQueryWrapper<>();
        lambdaType.eq(SysEntityTaxationTypePo::getEntityId,id);
        return baseMapper.selectList(lambdaType);
    }

    @Override
    public int updateByExamine(SysEntityExamineDto entityExamine) {
        UpdateWrapper<SysEntityTaxationTypePo> wrapper=new UpdateWrapper<>();
        wrapper.eq("id",entityExamine.getListId()).set(entityExamine.getFieldName(),entityExamine.getAfterText());
        return baseMapper.update(null,wrapper);
    }
}