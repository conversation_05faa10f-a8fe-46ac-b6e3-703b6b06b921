package com.heju.system.third.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.po.SysThirdAuthPo;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 第三方认证 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysThirdAuthConverter extends BaseConverter<SysThirdAuthQuery, SysThirdAuthDto, SysThirdAuthPo> {
}
