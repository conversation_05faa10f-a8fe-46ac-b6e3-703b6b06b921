package com.heju.system.notice.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.heju.common.core.constant.basic.EntityConstants.*;
import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 消息实体更变通知 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_message_entity_change", excludeProperty = { UPDATE_BY, CREATE_BY, DEL_FLAG, UPDATE_TIME, REMARK, NAME })
public class SysMessageEntityChangePo extends TBaseEntity {


    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

    /** 变更字段 */
    @Excel(name = "变更字段")
    protected String changeField;

    /** 变更前 */
    @Excel(name = "变更前")
    protected String changeBefore;

    /** 变更后 */
    @Excel(name = "变更后")
    protected String changeAfter;

}
