package com.heju.system.notice.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import com.heju.system.notice.domain.query.SysMessageEntityChangeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 消息实体变更通知 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysMessageEntityChangeConverter extends BaseConverter<SysMessageEntityChangeQuery, SysMessageEntityChangeDto, SysMessageEntityChangePo> {
}
