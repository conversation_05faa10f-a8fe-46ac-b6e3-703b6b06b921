package com.heju.system.authority.domain.merge;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Correlation;
import com.heju.common.core.annotation.Correlations;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.web.tenant.base.TBasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serial;


import static com.heju.system.api.authority.domain.merge.MergeGroup.*;

/**
 * 角色-实体字段 持久化对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role_entity_field_merge")
public class SysRoleEntityFieldMerge extends TBasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 角色Id */
    @Correlations({
            @Correlation(groupName = ROLE_SysRoleEntityFieldMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_MAIN),
            @Correlation(groupName = ENTITYFIELD_SysRoleEntityFieldMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_SLAVE)
    })
    private Long roleId;

    /** 实体字段Id */
    @Correlations({
            @Correlation(groupName = ROLE_SysRoleEntityFieldMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_SLAVE),
            @Correlation(groupName = ENTITYFIELD_SysRoleEntityFieldMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_MAIN)
    })
    private Long entityFieldId;

    public SysRoleEntityFieldMerge(Long roleId, Long entityFieldId) {
        this.roleId = roleId;
        this.entityFieldId = entityFieldId;
    }


}
