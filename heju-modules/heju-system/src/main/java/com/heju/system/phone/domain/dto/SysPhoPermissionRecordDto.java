package com.heju.system.phone.domain.dto;

import com.heju.system.phone.domain.po.SysPhoPermissionRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Optional;

/**
 * 手机号授权 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysPhoPermissionRecordDto extends SysPhoPermissionRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 授权人员名称
     */
    private String nickName;

    /**
     * 增加剩余次数
     */
    public void incrementViewedTimes() {
        this.viewedTimes = Optional.ofNullable(this.viewedTimes).orElse(0) + 1;
    }

    /**
     * 减少剩余次数（至少减到 0）
     */
    public void decrementRemainingTimes() {
        if (this.remainingTimes == null) {
            this.remainingTimes = 0;
        } else if (this.remainingTimes > 0) {
            this.remainingTimes -= 1;
        }
        // 如果剩余次数已经是 0 或负数，则不继续减少
    }

}

