package com.heju.system.entity.domain.po;

import com.heju.common.core.web.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.heju.common.core.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体税务人员信息 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_taxation_user", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK, NAME })
public class SysEntityTaxationUserPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 姓名 */
    @Excel(name = "姓名")
    protected String userName;

    /** 人员类型 */
    @Excel(name = "人员类型")
    protected Integer userType;

    /** 身份证件种类 */
    @Excel(name = "身份证件种类")
    protected Integer identityType;

    /** 身份证件号码 */
    @Excel(name = "身份证件号码")
    protected String identityNumber;

    /** 固定电话 */
    @Excel(name = "固定电话")
    protected String phone;

    /** 移动电话 */
    @Excel(name = "移动电话")
    protected String telephone;

    /** 电子邮箱 */
    @Excel(name = "电子邮箱")
    protected String eMail;

    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

}