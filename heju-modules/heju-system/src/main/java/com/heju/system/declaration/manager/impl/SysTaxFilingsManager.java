package com.heju.system.declaration.manager.impl;

import com.heju.common.core.constant.system.ReportConstants;
import com.heju.system.declaration.domain.dto.TaxFilingsView;
import com.heju.system.declaration.domain.po.SysTaxFilingsPo;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.system.declaration.domain.query.SysTaxFilingsQuery;
import com.heju.system.declaration.domain.model.SysTaxFilingsConverter;
import com.heju.system.declaration.mapper.SysTaxFilingsMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.declaration.manager.ISysTaxFilingsManager;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.service.impl.SysEntityServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 税务申报管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysTaxFilingsManager extends BaseManagerImpl<SysTaxFilingsQuery, SysTaxFilingsDto, SysTaxFilingsPo, SysTaxFilingsMapper, SysTaxFilingsConverter> implements ISysTaxFilingsManager {

    @Autowired
    SysTaxFilingsMapper taxFilingsMapper;

    @Autowired
    SysEntityServiceImpl entityService;

    public List<SysTaxFilingsQuery> Conformity(SysTaxFilingsQuery query) {
        List<SysTaxFilingsQuery> end = new ArrayList<>();
        //条件查询所有税务申报信息
        Map<Long, Map<String, Map<String, List<SysTaxFilingsDto>>>> groupedResult = taxFilingsMapper.findAllByCondition(query).stream()
                .collect(Collectors.groupingBy(
                        SysTaxFilingsDto::getEntityId,
                        Collectors.groupingBy(
                                SysTaxFilingsDto::getTaxType,
                                Collectors.groupingBy(
                                        SysTaxFilingsDto::getReporttimeType
                                )
                        )
                ));
        //查询所有实体信息
        List<SysEntityPo> entityList = entityService.findAllEntityIdAndName(query);
        if (Objects.isNull(entityList)) {
            return end;
        }
        //所有信息初始化
        for (SysEntityPo sysEntityPo : entityList) {
            SysTaxFilingsQuery temp = new SysTaxFilingsQuery(query.getYear());
            temp.setEntityId(sysEntityPo.getId());
            temp.setEntityName(sysEntityPo.getName());
            end.add(temp);
        }
        Map<Long, String> entityNameMap = new HashMap<>();
        for (SysEntityPo sysEntityPo : entityList) {
            entityNameMap.put(sysEntityPo.getId(), sysEntityPo.getName());
        }

        //封装数据
        for (Map.Entry<Long, Map<String, Map<String, List<SysTaxFilingsDto>>>> entityEntry : groupedResult.entrySet()) {
            Long entityId = entityEntry.getKey(); //实体id
            String entityName = entityNameMap.get(entityId); //实体名称
            SysTaxFilingsQuery temp = new SysTaxFilingsQuery(query.getYear());
            temp.setEntityId(entityId);
            temp.setEntityName(entityName);
            for (Map.Entry<String, Map<String, List<SysTaxFilingsDto>>> taxTypeEntry : entityEntry.getValue().entrySet()) {
                String taxType = taxTypeEntry.getKey();
                TaxFilingsView taxFilingsView = new TaxFilingsView();
                for (Map.Entry<String, List<SysTaxFilingsDto>> reportTimeEntry : taxTypeEntry.getValue().entrySet()) {

                    String reportTimeType = reportTimeEntry.getKey();
                    List<SysTaxFilingsDto> sysTaxFilingsDtoList = reportTimeEntry.getValue();

                    //更新各种时间类型申报信息map
                    if (reportTimeType.equals(ReportConstants.TimeType.MONTH.getCode())) {
                        taxFilingsView.updateMonthTax(sysTaxFilingsDtoList);
                    }
                    if (reportTimeType.equals(ReportConstants.TimeType.SEASON.getCode())) {
                        taxFilingsView.updateSeasonTax(sysTaxFilingsDtoList);
                    }
                    if (reportTimeType.equals(ReportConstants.TimeType.YEARS.getCode())) {
                        taxFilingsView.updateYearTax(sysTaxFilingsDtoList);
                    }
                }
                temp.updateTaxTypes(taxType, taxFilingsView);
            }
            //删除重复entityId的数据
            end.removeIf(sysTaxFilingsQuery -> sysTaxFilingsQuery.getEntityId().equals(temp.getEntityId()));
            end.add(temp);
        }
        return end;
    }
}