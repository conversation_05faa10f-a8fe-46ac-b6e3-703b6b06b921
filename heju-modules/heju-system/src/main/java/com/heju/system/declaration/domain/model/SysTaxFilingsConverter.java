package com.heju.system.declaration.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.system.declaration.domain.po.SysTaxFilingsPo;
import com.heju.system.declaration.domain.query.SysTaxFilingsQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 税务申报 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysTaxFilingsConverter extends BaseConverter<SysTaxFilingsQuery, SysTaxFilingsDto, SysTaxFilingsPo> {
}
