package com.heju.system.entity.domain.po;

import java.time.LocalDate;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体工商变更记录 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_saic_change_record", excludeProperty = {STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK, NAME})
public class SysEntitySaicChangeRecordPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变更日期
     */
    @Excel(name = "变更日期")
    protected LocalDate changeDate;

    /**
     * 变更类型
     */
    @Excel(name = "变更类型")
    protected String changeType;

    /**
     * 变更前
     */
    @Excel(name = "变更前")
    protected String changeBefore;

    /**
     * 变更后
     */
    @Excel(name = "变更后")
    protected String changeAfter;

    /**
     * 公司id
     */
    @Excel(name = "公司id")
    protected Long entityId;

}