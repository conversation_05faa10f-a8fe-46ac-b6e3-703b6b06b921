package com.heju.system.phoneinfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.system.phone.manager.ISysPhoPermissionRecordManager;
import com.heju.system.phone.manager.ISysPhoneNumberInfoManager;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.query.SysTelephoneCodeQuery;
import com.heju.system.phoneinfo.manager.ISysTelephoneCodeManager;
import com.heju.system.phoneinfo.service.ISysTelephoneCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 转发短信管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysTelephoneCodeServiceImpl extends BaseServiceImpl<SysTelephoneCodeQuery, SysTelephoneCodeDto, ISysTelephoneCodeManager> implements ISysTelephoneCodeService {

    @Autowired
    private ISysTelephoneCodeManager manager;

    @Autowired
    private ISysPhoneNumberInfoManager sysPhoneNumberInfoManager;

    @Autowired
    private ISysPhoPermissionRecordManager sysPhoPermissionManager;

    /**
     * 查询转发短信对象列表 | 数据权限
     *
     * @param telephoneCode 转发短信对象
     * @return 转发短信对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysTelephoneCodeMapper"})
    public List<SysTelephoneCodeDto> selectListScope(SysTelephoneCodeQuery telephoneCode) {
        return baseManager.selectList(telephoneCode);
    }


    public List<SysTelephoneCodeDto> phoneMsgList(SysTelephoneCodeQuery query) {
        List<SysTelephoneCodeDto> telephoneCodeDtos = new ArrayList<>();

        SysPhoneNumberInfoQuery infoQuery = new SysPhoneNumberInfoQuery();
        infoQuery.setPhoneNumber(query.getReceive());
        infoQuery.setCustody(SecurityUtils.getUser().getId());
        List<SysPhoneNumberInfoDto> infoDtos = sysPhoneNumberInfoManager.selectList(infoQuery);
        //存在  当前手机号我是保管人
        if ((infoDtos != null && !infoDtos.isEmpty()) || SecurityUtils.getUser().isAdmin()) {
            telephoneCodeDtos = baseManager.selectList(query);
        } else {
            //被授权人
            SysPhoPermissionRecordQuery recordQuery = new SysPhoPermissionRecordQuery();
            recordQuery.setPermissionerId(SecurityUtils.getUser().getId());
            recordQuery.setQueryTime(LocalDateTime.now());
            recordQuery.setPhoneNumber(query.getReceive());
            // 查用户 授权表符合记录   得到起始、终止时间  授权剩余次数
            List<SysPhoPermissionRecordDto> recordDtos = sysPhoPermissionManager.selectRecordForMsg(recordQuery);
            for (SysPhoPermissionRecordDto recordDto : recordDtos) {
                if (recordDto.getRemainingTimes() != null && recordDto.getViewedTimes() != null
                        && recordDto.getStartTime() != null && recordDto.getEndTime() != null) {
                    query.setStartTime(recordDto.getStartTime());
                    query.setEndTime(recordDto.getEndTime());
                    query.setRemainingTimes(recordDto.getRemainingTimes());
                    query.setViewedTimes(recordDto.getViewedTimes());
                    query.setPermissionTime(recordDto.getCreateTime());
                }
            }
            //根据 时间区间(授权手机号中的),  次数 查询短信信息
            //有时间  createTime between start and end
            List<SysTelephoneCodeDto> msgList = manager.selectPhoMsgList(query);
            // 授权有限次
            // 获取次数相关字段
            Integer remainingTimes = query.getRemainingTimes();
            Integer viewedTimes = query.getViewedTimes();
            // 授权有限次（且剩余次数 > 0）  0返回空列表
            if (remainingTimes != null && remainingTimes != NumberUtil.Ne_One && viewedTimes != null) {
                telephoneCodeDtos = msgList.stream()
                        .limit(viewedTimes)
                        .collect(Collectors.toList());
            // 授权无限次（times == -1）
            } else if (remainingTimes != null && remainingTimes == NumberUtil.Ne_One && viewedTimes != null) {
                telephoneCodeDtos = msgList;
            // 默认情况（无授权信息、次数为空等）
            } else {
                telephoneCodeDtos = Collections.emptyList(); // 或者根据业务需求处理
            }
        }
        return telephoneCodeDtos;
    }

    /**
     * 短信接口新增逻辑
     * @param dto 数据对象
     * @return
     */
    @Override
    @DSTransactional
    public int insert(SysTelephoneCodeDto dto) {
        SecurityContextHolder.setSourceName("240b0d22cd1a48069501d000f1c8efe5");
        SecurityContextHolder.setIsLessor("N");
        SecurityContextHolder.setUserType("00");
        SecurityContextHolder.setEnterpriseId("1830847511921819650");
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        SysPhoPermissionRecordQuery query = new SysPhoPermissionRecordQuery();
        List<SysPhoPermissionRecordDto> recordDtos = sysPhoPermissionManager.selectRecordForTimes(query);
        for (SysPhoPermissionRecordDto recordDto : recordDtos) {
            //已看次数 + 1
            recordDto.incrementViewedTimes();
            if (recordDto.getTimes() != NumberUtil.Ne_One) {
                //剩余次数 -1
                recordDto.decrementRemainingTimes();
            }
        }

        if (!recordDtos.isEmpty() && recordDtos != null) {
            sysPhoPermissionManager.updateBatch(recordDtos);
        }
        Pattern pattern = Pattern.compile("(?<!\\d)(\\d{4,8})(?!\\d)");
        Matcher matcher = pattern.matcher(dto.getMsgContent());
        if (matcher.find()) {
            dto.setVerificationCode(matcher.group(1));
        }
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        return row;
    }
}
