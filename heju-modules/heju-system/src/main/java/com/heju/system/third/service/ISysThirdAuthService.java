package com.heju.system.third.service;


import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto;
import com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto;
import com.heju.system.third.domain.query.SysThirdAuthQuery;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.common.web.entity.service.IBaseService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 第三方认证管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysThirdAuthService extends IBaseService<SysThirdAuthQuery, SysThirdAuthDto> {


    /**
     * 校验第三方认证信息编码是否唯一
     *
     * @param Id   第三方认证信息Id
     * @param code 第三方认证信息编码
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkThirdAuthCodeUnique(Long Id, String code);


    /**
     * 租户下，查询认证账号
     *
     * @param thirdId
     * @return
     */
    List<SysCompanyThirdAuthMergeDto> getTenantCpmList(Long thirdId);

    /**
     * 租户 新增认证关联
     *
     * @param dto
     * @return
     */
    int addTenantCpmAuth(SysCompanyThirdAuthMergeAddDto dto);


    List<SysThirdAuthDto> optionByThirdId(Long thirdId);


    boolean checkByThirdId(List<Long> idList);

    boolean checkCompanyById(List<Long> idList);

    Map<String,String> getCompanyThirdAuthRedis();

    Map<String,SysThirdAuthDto> getThirdAuthRedis();

    void setCompanyThirdAuthRedis(Long thirdAuthId,String companyId);

    List<SysCompanyThirdAuthMerge> getByCompanyIds(List<Long> idList);
}