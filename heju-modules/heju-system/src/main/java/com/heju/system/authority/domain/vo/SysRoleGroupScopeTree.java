package com.heju.system.authority.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.heju.common.core.utils.core.pool.NumberPool;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.authority.domain.constant.SysRoleGroupConstants;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组织对象 合成通用结构
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SysRoleGroupScopeTree {

    public static final Integer ROLE_GROUP = 1;

    /** Id */
    private Long id;

    /** 父级Id（所属角色组id） */
    private Long parentId;

    /** 名称 */
    private String label;

    /** 状态 */
    private String status;

    /** 类型（是否是角色组 0为角色中  1为非角色组） */
    private String type;

    /** 子角色 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SysRoleGroupScopeTree> children;

    /**
     * 角色组-角色转换
     */
    public SysRoleGroupScopeTree(SysRolePo role) {
        if((!ROLE_GROUP.equals(role.getIsRoleGroup()))){
            this.id = role.getId();
            this.parentId = Long.parseLong(String.valueOf(NumberPool.Zero));
            this.label = role.getName();
            this.type = SysRoleGroupConstants.ROLEGROUP;
        }else{
            this.id = role.getId();
            this.parentId = role.getRoleGroupId();
            this.label = role.getName();
            this.type = SysRoleGroupConstants.ROLE;
        }
    }

}
