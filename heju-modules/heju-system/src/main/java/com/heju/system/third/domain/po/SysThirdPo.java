package com.heju.system.third.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 第三方模块 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_third")
public class SysThirdPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 第三方编码 */
    @Excel(name = "第三方编码")
    protected String code;

    /** 第三方LOGO */
    @Excel(name = "第三方LOGO")
    protected String logo;

    /** 第三方认证字段（以，隔开） */
    @Excel(name = "第三方认证字段", readConverterExp = "以=，隔开")
    protected String authStr;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}