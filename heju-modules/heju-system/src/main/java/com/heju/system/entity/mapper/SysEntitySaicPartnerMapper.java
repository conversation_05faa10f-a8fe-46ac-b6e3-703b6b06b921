package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import com.heju.system.entity.domain.query.SysEntitySaicPartnerQuery;

/**
 * 实体工商股东管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntitySaicPartnerMapper extends BaseMapper<SysEntitySaicPartnerQuery, SysEntitySaicPartnerDto, SysEntitySaicPartnerPo> {
}