package com.heju.system.company.domain.dto;

import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.po.SysDeptPo;
import com.heju.system.api.organize.domain.po.SysPostPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CompanyDeptPostDto extends SysCompanyDto {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<SysDeptPo> deptDtoList;

    private List<SysPostPo> postDtoList;

    private List<SysUserDto> userDtoList;
}
