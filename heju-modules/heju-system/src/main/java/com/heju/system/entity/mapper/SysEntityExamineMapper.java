package com.heju.system.entity.mapper;

import com.heju.system.entity.domain.query.SysEntityExamineQuery;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.po.SysEntityExaminePo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;

/**
 * 实体信息审核管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityExamineMapper extends BaseMapper<SysEntityExamineQuery, SysEntityExamineDto, SysEntityExaminePo> {
}