package com.heju.system.organize.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import com.heju.system.api.organize.domain.query.SysEnterpriseQuery;

/**
 * 企业管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEnterpriseManager extends IBaseManager<SysEnterpriseQuery, SysEnterpriseDto> {

    /**
     * 根据名称查询状态正常企业对象
     *
     * @param name 名称
     * @return 企业对象
     */
    SysEnterpriseDto selectByName(String name);

    Long getUserIdByTenant(String enterpriseName, String telephone,Long userId);

    Long getUserIdByTenantUnionId(String enterpriseName, String unionId);

    String getIdByTelephone(String telephone);

    int insertByTelephone(String id,String telephone);

    int insertTenantUser(Long tenantUserId, Long tenantId, String userId);
}
