package com.heju.system.annualReport.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;

import java.util.List;

/**
 * 工商年报管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysAnnualReportService extends IBaseService<SysAnnualReportQuery, SysAnnualReportDto> {

    List<SysAnnualReportDto> selectPartList(SysAnnualReportQuery annualReport);

    List<SysAnnualReportDto> selectEntityOption();

    int pass(SysAnnualReportDto annualReport);

    int reject(SysAnnualReportDto annualReport);

    SysAnnualReportDto selectByEntityAndYear(SysAnnualReportDto annualReport);

    int check();
}