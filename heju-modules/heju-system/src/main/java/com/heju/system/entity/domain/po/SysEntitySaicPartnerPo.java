package com.heju.system.entity.domain.po;

import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体工商股东 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_saic_partner", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK, NAME })
public class SysEntitySaicPartnerPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实缴出资额 */
    @Excel(name = "实缴出资额")
    protected String totalRealCapital;

    /** 股东类型 */
    @Excel(name = "股东类型")
    protected String partnerType;

    /** 认缴出资额 */
    @Excel(name = "认缴出资额")
    protected String totalShouldCapital;

    protected String percent;

    /** 股东名称 */
    @Excel(name = "股东名称")
    protected String partnerName;

    /** 公司id */
    @Excel(name = "公司id")
    protected Long entityId;

}