package com.heju.system.forms.optionValue.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.model.SysOptionValueConverter;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import com.heju.system.forms.optionValue.manager.ISysOptionValueManager;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 选项值管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysOptionValueManager extends BaseManagerImpl<SysOptionValueQuery, SysOptionValueDto, SysOptionValuePo, SysOptionValueMapper, SysOptionValueConverter> implements ISysOptionValueManager {
    @Override
    public Map<Long, List<SysOptionValuePo>> selectByOptionId(List<Long> optionId) {
        List<SysOptionValuePo> optionValues = baseMapper.selectList(
                Wrappers.<SysOptionValuePo>query().lambda().in(SysOptionValuePo::getOptionId, optionId));
        return optionValues.stream().collect(Collectors.groupingBy(SysOptionValuePo::getOptionId));

    }

    @Override
    public SysOptionValueDto checkConfigCodeUnique(Long Id, String name) {
        SysOptionValuePo option = baseMapper.selectOne(
                Wrappers.<SysOptionValuePo>query().lambda()
                        .ne(SysOptionValuePo::getId, Id)
                        .eq(SysOptionValuePo::getName, name)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(option);
    }
}