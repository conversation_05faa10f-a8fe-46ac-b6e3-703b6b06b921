package com.heju.system.notice.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.heju.common.core.annotation.Excel;
import com.heju.system.notice.domain.po.SysMessagePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 消息通知 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMessageDto extends SysMessagePo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 消息id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 发送人名称 */
    private String sendUserName;

    /** 接收人名称 */
    private String receiveUserName;

    /** 变更字段 */
    @Excel(name = "变更字段")
    protected String changeField;

    /** 变更前 */
    @Excel(name = "变更前")
    protected String changeBefore;

    /** 变更后 */
    @Excel(name = "变更后")
    protected String changeAfter;
}