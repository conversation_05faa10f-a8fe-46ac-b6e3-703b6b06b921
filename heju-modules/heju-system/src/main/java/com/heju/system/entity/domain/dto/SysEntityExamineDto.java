package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntityExaminePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体信息审核 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityExamineDto extends SysEntityExaminePo {

    @Serial
    private static final long serialVersionUID = 1L;

    private  String createName;

}