package com.heju.system.report.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.po.SysFinanceReportPo;
import com.heju.system.report.domain.query.SysFinanceReportQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 财税报表 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysFinanceReportConverter extends BaseConverter<SysFinanceReportQuery, SysFinanceReportDto, SysFinanceReportPo> {
}
