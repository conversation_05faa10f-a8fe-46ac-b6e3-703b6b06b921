package com.heju.system.forms.field.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.utils.FieldTypeConstants;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 字段管理管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/field")
public class SysFieldController extends BaseController<SysFieldQuery, SysFieldDto, ISysFieldService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "字段管理" ;
    }

    /**
     * 查询字段管理列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FIELD_LIST)
    public AjaxResult list(SysFieldQuery field) {
        return success(baseService.selectListScope(field));
    }

    /**
     * 查询字段管理详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FIELD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 字段管理新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FIELD_ADD)
    @Log(title = "字段管理管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFieldDto field) {
        return super.add(field);
    }

    /**
     * 字段管理修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FIELD_EDIT)
    @Log(title = "字段管理管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFieldDto field) {
        return super.edit(field);
    }

    /**
     * 字段管理修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FIELD_EDIT, Auth.SYS_FIELD_ES}, logical = Logical.OR)
    @Log(title = "字段管理管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFieldDto field) {
        return super.editStatus(field);
    }

    /**
     * 字段管理批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FIELD_DEL)
    @Log(title = "字段管理管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取字段管理选择框列表
     */
    @GetMapping("/option")
    public AjaxResult option(SysFieldQuery field) {
        return baseService.option(field);
    }

    /**
     * 关联关系列表
     */
    @GetMapping("/relationList")
    @RequiresPermissions(Auth.SYS_FIELD_RELATION)
    public AjaxResult relationList(SysFieldQuery field) {
        field.setFieldType(FieldTypeConstants.RELATION);
        startPage();
        List<SysFieldDto> list = baseService.relationList(field);
        return getDataTable(list);
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysFieldDto field) {
        if (baseService.checkConfigCodeUnique(field.getId(), field.getApiName(),field.getSheetId()))
            warn(StrUtil.format("{}{}失败，{}API名称已存在", operate.getInfo(), getNodeName(), field.getApiName()));
    }


    /**
     * 前置校验 （强制）删除
     */
    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        List<SysFieldDto> sysFields = baseService.selectListByIds(idList);
        List<SysFieldDto> list = sysFields.stream().filter(sysField -> sysField.getFieldType().equals("0")).toList();
        if(!list.isEmpty()){
            SysFieldQuery sysFieldQuery=new SysFieldQuery();
            sysFieldQuery.setSheetId(list.get(0).getSheetId());
            List<SysFieldDto> sysFieldDtos = baseService.selectList(sysFieldQuery);
            if(sysFieldDtos.size()>1){
                warn(StrUtil.format("{}{}失败，{}是唯一标识字段字段,只可在无其他字段时删除", operate.getInfo(), getNodeName(), list.get(0).getName()));
            }
        }
        List<SysFieldDto> collect = sysFields.stream().filter(sysField -> sysField.getFieldSystemType() == 1).toList();
        if(!collect.isEmpty()){
            List<String> names=new ArrayList<>();
            for (SysFieldDto dto : collect) {
                names.add(dto.getName());
            }
            warn(StrUtil.format("{}{}失败，{}是系统默认字段不可删除", operate.getInfo(), getNodeName(), String.join(",", names)));
        }
        List<Long> ids = sysFields.stream().map(SysFieldDto::getId).toList();
        List<SysFieldPo> sysFieldPos = baseService.selectQuote(ids);
        if(!sysFieldPos.isEmpty()){
            List<String> names=new ArrayList<>();
            for (SysFieldPo po : sysFieldPos) {
                names.add(po.getName());
            }
            warn(StrUtil.format("{}{}失败，{}是被引用字段不可删除", operate.getInfo(), getNodeName(), String.join(",", names)));
        }
        List<SysFieldPo> relation = baseService.selectRelation(ids);
        if(!relation.isEmpty()){
            List<String> names=new ArrayList<>();
            for (SysFieldPo po : sysFieldPos) {
                names.add(po.getName());
            }
            warn(StrUtil.format("{}{}失败，{}是被关联字段不可删除", operate.getInfo(), getNodeName(), String.join(",", names)));
        }
    }
}
