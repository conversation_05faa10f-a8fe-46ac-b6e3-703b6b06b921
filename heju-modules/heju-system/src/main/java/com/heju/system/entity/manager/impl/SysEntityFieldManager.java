package com.heju.system.entity.manager.impl;

import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.model.SysEntityFieldConverter;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.query.SysEntityFieldQuery;
import com.heju.system.entity.manager.ISysEntityFieldManager;
import com.heju.system.entity.mapper.SysEntityFieldMapper;
import org.springframework.stereotype.Component;

/**
 * 实体字段管理管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntityFieldManager extends BaseManagerImpl<SysEntityFieldQuery, SysEntityFieldDto, SysEntityFieldPo, SysEntityFieldMapper, SysEntityFieldConverter> implements ISysEntityFieldManager {
}