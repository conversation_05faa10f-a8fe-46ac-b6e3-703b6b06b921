package com.heju.system.report.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.po.SysBankReportPo;
import com.heju.system.report.domain.query.SysBankReportQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 银行报表 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysBankReportConverter extends BaseConverter<SysBankReportQuery, SysBankReportDto, SysBankReportPo> {
}
