package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.dto.SysEntityTaxationUserDto;
import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import com.heju.system.entity.domain.query.SysEntityExamineQuery;
import com.heju.system.entity.domain.query.SysEntityTaxationUserQuery;

import java.util.List;

/**
 * 实体税务人员信息管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntityTaxationUserManager extends IBaseManager<SysEntityTaxationUserQuery, SysEntityTaxationUserDto> {

    List<SysEntityTaxationUserPo> getByEntityId(Long id);

    int updateByExamine(SysEntityExamineDto entityExamine);
}