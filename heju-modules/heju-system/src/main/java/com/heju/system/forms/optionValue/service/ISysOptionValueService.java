package com.heju.system.forms.optionValue.service;

import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;

/**
 * 选项值管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysOptionValueService extends IBaseService<SysOptionValueQuery, SysOptionValueDto> {

    /**
     * 校验参数编码是否唯一
     *
     * @param Id      参数Id
     * @param name    名称
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkConfigCodeUnique(Long Id, String name);

    /**
     * 根据optionID 查所有选项值
     */
    List<SysOptionValueDto> getValueListByOptionId(SysOptionValueQuery optionValue);
}
