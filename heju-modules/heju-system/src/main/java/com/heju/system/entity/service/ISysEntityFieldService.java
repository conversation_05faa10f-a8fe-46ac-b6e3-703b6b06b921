package com.heju.system.entity.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.query.SysEntityFieldQuery;

import java.util.HashMap;
import java.util.List;

/**
 * 实体字段管理管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysEntityFieldService extends IBaseService<SysEntityFieldQuery, SysEntityFieldDto> {

    List<SysEntityFieldDto> listByBelong(SysEntityFieldQuery entityField);

    HashMap<String,List<SysEntityFieldPo>> list(Long roleGroupId);

}