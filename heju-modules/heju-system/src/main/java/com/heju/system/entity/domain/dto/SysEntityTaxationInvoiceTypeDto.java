package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntityTaxationInvoiceTypePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体税务票种认定 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityTaxationInvoiceTypeDto extends SysEntityTaxationInvoiceTypePo {

    @Serial
    private static final long serialVersionUID = 1L;

}