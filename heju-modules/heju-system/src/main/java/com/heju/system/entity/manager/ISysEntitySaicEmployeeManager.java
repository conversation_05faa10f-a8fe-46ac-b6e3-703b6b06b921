package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.po.SysEntitySaicEmployeePo;
import com.heju.system.entity.domain.query.SysEntitySaicEmployeeQuery;

import java.util.List;

/**
 * 实体工商董事会成员管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntitySaicEmployeeManager extends IBaseManager<SysEntitySaicEmployeeQuery, SysEntitySaicEmployeeDto> {

    List<SysEntitySaicEmployeePo> getByEntityId(Long id);
}