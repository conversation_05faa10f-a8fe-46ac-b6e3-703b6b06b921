package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.po.SysEntitySaicChangeRecordPo;
import com.heju.system.entity.domain.query.SysEntitySaicChangeRecordQuery;

/**
 * 实体工商变更记录管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntitySaicChangeRecordMapper extends BaseMapper<SysEntitySaicChangeRecordQuery, SysEntitySaicChangeRecordDto, SysEntitySaicChangeRecordPo> {
}