package com.heju.system.entity.domain.query;

import com.heju.common.core.web.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体信息管理 数据查询对象
 *
 * <AUTHOR>
 */
@Data
public class SysEnglishQuery {

    @Serial
    private static final long serialVersionUID = 1L;
    private Integer startNum;

    private Integer endNum;

    private String englishCode;

    private Integer num;

    //1-短前长后；2-短后长前
    private Integer type;
}