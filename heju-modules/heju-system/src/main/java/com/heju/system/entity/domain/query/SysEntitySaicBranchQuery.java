package com.heju.system.entity.domain.query;

import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体工商分支机构 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntitySaicBranchQuery extends SysEntitySaicBranchPo {

    @Serial
    private static final long serialVersionUID = 1L;
}