package com.heju.system.declaration.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.query.SysBusinessAnnualReportQuery;
import com.heju.system.declaration.mapper.SysBusinessAnnualReportMapper;
import com.heju.system.declaration.service.ISysBusinessAnnualReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 工商年报管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business")
public class SysBusinessAnnualReportController extends BaseController<SysBusinessAnnualReportQuery, SysBusinessAnnualReportDto, ISysBusinessAnnualReportService> {

    @Autowired
    SysBusinessAnnualReportMapper businessAnnualReportMapper;


    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "工商年报" ;
    }

    /**
     * 查询工商年报列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_LIST)
    public AjaxResult list(SysBusinessAnnualReportQuery businessAnnualReport) {
        return super.list(businessAnnualReport);
    }

    /**
     * 查询工商年报管理列表
     */
    @GetMapping("/manage/list")
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_MANAGE_LIST)
    public AjaxResult manageList(SysBusinessAnnualReportQuery businessAnnualReport) {
        return super.list(businessAnnualReport);
    }

    /**
     * 查询工商年报详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 查询工商年报管理详细
     */
    @GetMapping(value = "/manage/{id}")
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_MANAGE_SINGLE)
    public AjaxResult getInfoManage(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 工商年报新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_ADD)
    @Log(title = "工商年报管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysBusinessAnnualReportDto businessAnnualReport) {
        SysBusinessAnnualReportDto dto = businessAnnualReportMapper.selectByCondition(businessAnnualReport);
        if (!Objects.isNull(dto)){
            return AjaxResult.error("您已申请过该年的工商年报");
        }
        return super.add(businessAnnualReport);
    }

    /**
     * 工商年报修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_EDIT)
    @Log(title = "工商年报管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysBusinessAnnualReportDto businessAnnualReport) {
        String[] split = businessAnnualReport.getYear().split("-");
        businessAnnualReport.setYear(split[0]);
        return super.edit(businessAnnualReport);
    }

    /**
     * 工商年报修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_BUSINESS_ANNUAL_REPORT_EDIT, Auth.SYS_BUSINESS_ANNUAL_REPORT_ES}, logical = Logical.OR)
    @Log(title = "工商年报管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysBusinessAnnualReportDto businessAnnualReport) {
        return super.editStatus(businessAnnualReport);
    }

    /**
     * 工商年报批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_BUSINESS_ANNUAL_REPORT_DEL)
    @Log(title = "工商年报管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取工商年报选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 工商年报审批通过
     */
    @PutMapping("/pass")
    @RequiresPermissions(value = {Auth.SYS_BUSINESS_ANNUAL_REPORT_PASS, Auth.SYS_BUSINESS_ANNUAL_REPORT_PASS}, logical = Logical.OR)
    @Log(title = "工商年报管理", businessType = BusinessType.PASS)
    public AjaxResult Approved(@RequestBody SysBusinessAnnualReportDto businessAnnualReportDto){
        String[] split = businessAnnualReportDto.getYear().split("-");
        businessAnnualReportDto.setYear(split[0]);
        return super.edit(businessAnnualReportDto);
    }

    /**
     * 工商年报审批驳回
     */
    @PutMapping("/overrule")
    @RequiresPermissions(value = {Auth.SYS_BUSINESS_ANNUAL_REPORT_REJECT, Auth.SYS_BUSINESS_ANNUAL_REPORT_REJECT}, logical = Logical.OR)
    @Log(title = "工商年报管理", businessType = BusinessType.NO_PASS)
    public AjaxResult overrule(@RequestBody SysBusinessAnnualReportDto businessAnnualReportDto){
        String[] split = businessAnnualReportDto.getYear().split("-");
        businessAnnualReportDto.setYear(split[0]);
        return super.edit(businessAnnualReportDto);

    }

}
