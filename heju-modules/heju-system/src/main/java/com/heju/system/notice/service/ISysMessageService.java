package com.heju.system.notice.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.notice.domain.query.SysMessageQuery;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;

/**
 * 消息通知管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysMessageService extends IBaseService<SysMessageQuery, SysMessageDto> {
    int getMessageCount();
}