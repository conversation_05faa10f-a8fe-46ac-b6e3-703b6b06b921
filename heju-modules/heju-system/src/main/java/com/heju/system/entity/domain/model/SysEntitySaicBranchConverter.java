package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import com.heju.system.entity.domain.query.SysEntitySaicBranchQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体工商分支机构 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntitySaicBranchConverter extends BaseConverter<SysEntitySaicBranchQuery, SysEntitySaicBranchDto, SysEntitySaicBranchPo> {
}
