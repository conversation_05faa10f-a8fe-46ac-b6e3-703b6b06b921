package com.heju.system.declaration.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.system.declaration.domain.query.SysTaxFilingsQuery;
import com.heju.system.declaration.service.ISysTaxFilingsService;
import com.heju.system.declaration.service.impl.SysTaxFilingsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 税务申报管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/taxFilings")
public class SysTaxFilingsController extends BaseController<SysTaxFilingsQuery, SysTaxFilingsDto, ISysTaxFilingsService> {

    @Autowired
    SysTaxFilingsServiceImpl taxFilingsService;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "税务申报" ;
    }

    /**
     * 查询税务申报列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_TAX_FILINGS_LIST)
    public AjaxResult list(SysTaxFilingsQuery taxFilings) {
        return super.list(taxFilings);
    }

    /**
     * 查询税务申报详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_TAX_FILINGS_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 税务申报新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_TAX_FILINGS_ADD)
    @Log(title = "税务申报管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysTaxFilingsDto taxFilings) {
        return super.add(taxFilings);
    }

    /**
     * 税务申报修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_TAX_FILINGS_EDIT)
    @Log(title = "税务申报管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysTaxFilingsDto taxFilings) {
        return super.edit(taxFilings);
    }

    /**
     * 税务申报修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_TAX_FILINGS_EDIT, Auth.SYS_TAX_FILINGS_ES}, logical = Logical.OR)
    @Log(title = "税务申报管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysTaxFilingsDto taxFilings) {
        return super.editStatus(taxFilings);
    }

    /**
     * 税务申报批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_TAX_FILINGS_DEL)
    @Log(title = "税务申报管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取税务申报选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 查询税务申报管理列表
     */
    @GetMapping("/ManageList")
    public AjaxResult taxManagement(SysTaxFilingsQuery taxFilings){
        startPage();
        List<SysTaxFilingsQuery> list = taxFilingsService.getAll(taxFilings);
        return getDataTable(list);
    }

}
