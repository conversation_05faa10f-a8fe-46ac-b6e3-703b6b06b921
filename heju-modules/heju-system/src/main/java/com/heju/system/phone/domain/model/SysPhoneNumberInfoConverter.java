package com.heju.system.phone.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.po.SysPhoneNumberInfoPo;
import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 手机号 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysPhoneNumberInfoConverter extends BaseConverter<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto, SysPhoneNumberInfoPo> {
}
