package com.heju.system.forms.cascade.domain.dto;

import cn.hutool.core.lang.tree.Tree;
import com.heju.common.core.annotation.Excel;
import com.heju.system.forms.cascade.domain.po.CascadeTree;
import com.heju.system.forms.cascade.domain.po.SysCascadePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 级联 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysCascadeDto extends SysCascadePo {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<Tree<String>> cascadeTreeStringList;

    private List<CascadeTree> cascadeTreeList;

    private String mainOptionName;

    private String cascade1OptionName;

    private String cascade2OptionName;

    private String cascade3OptionName;

    private String cascade4OptionName;
}