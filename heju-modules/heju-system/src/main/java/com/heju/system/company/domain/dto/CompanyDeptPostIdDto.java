package com.heju.system.company.domain.dto;

import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CompanyDeptPostIdDto extends SysCompanyDto {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<Long> deptIdList;

    private List<Long> postIdList;

    private List<SysUserDto> userDtoList;

    private List<Long> userIdList;
}
