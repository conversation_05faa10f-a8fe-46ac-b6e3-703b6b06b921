package com.heju.system.forms.sheet.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.query.SysSheetQuery;
import com.heju.system.forms.sheet.manager.ISysSheetManager;
import com.heju.system.forms.sheet.service.ISysSheetService;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 表单管理管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysSheetServiceImpl extends BaseServiceImpl<SysSheetQuery, SysSheetDto, ISysSheetManager> implements ISysSheetService {



    /**
     * 查询表单管理对象列表 | 数据权限
     *
     * @param sheet 表单管理对象
     * @return 表单管理对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysSheetMapper"})
    public List<SysSheetDto> selectListScope(SysSheetQuery sheet) {
        return baseManager.selectList(sheet);
    }

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysSheetDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        if(row> NumberUtil.Zero){
            // 添加数据库表
            baseManager.createSheet(dto);
            //添加唯一标识字段
            baseManager.insertField(dto);
        }
        return row;
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysSheetDto dto) {
        SysSheetDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        if(row> NumberUtil.Zero){
            // 修改数据库表
            baseManager.updateSheet(dto);
        }
        return row;
    }

    @Override
    public boolean checkConfigCodeUnique(Long Id, String apiName) {
        return ObjectUtil.isNotNull(baseManager.checkConfigCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, apiName));
    }

    @Override
    public List<SysFieldPo> selectFieldBySheetId(List<Long> idList) {
        return baseManager.selectFieldBySheetId(idList);
    }

    @Override
    public String selectByIds(List<Long> ids) {
        List<SysSheetDto> sysSheetDtos = baseManager.selectListByIds(ids);
        StringBuilder name = new StringBuilder();
        for (SysSheetDto sysSheetDto : sysSheetDtos) {
            name.append(sysSheetDto.getName()).append(",");
        }
        return name.toString();
    }

    @Override
    public SysSheetDto selectOneByApiName(String apiName) {
        return  baseManager.selectOne(apiName);
    }

    /**
     * 根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    @Override
    @DSTransactional
    public int deleteByIds(Collection<? extends Serializable> idList) {
        List<SysSheetDto> originList = selectListByIds(idList);
        startBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, originList, null);
        int rows = baseManager.deleteByIds(idList);
        for (SysSheetDto sysSheetDto : originList) {
            baseManager.deleteSheet(sysSheetDto);
        }
        endBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, rows, originList, null);
        return rows;
    }

    @Override
    public AjaxResult optionList(SysSheetQuery query) {
        try {
            query.setStatus(BaseConstants.Status.NORMAL.getCode());
            query.setIsOption(0);
            return AjaxResult.success(baseManager.selectList(query));
        } catch (Exception ignored) {}
        return AjaxResult.error();
    }
}
