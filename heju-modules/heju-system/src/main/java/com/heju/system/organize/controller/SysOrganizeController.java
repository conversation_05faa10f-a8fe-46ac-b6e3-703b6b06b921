package com.heju.system.organize.controller;

import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.utils.TreeUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BasisController;
import com.heju.system.organize.domain.vo.SysOrganizeTree;
import com.heju.system.organize.service.ISysOrganizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 组织管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/organize")
public class SysOrganizeController extends Ba<PERSON><PERSON>ontroller {

    @Autowired
    private ISysOrganizeService organizeService;

    /**
     * 获取企业部门|岗位树
     */
    @GetMapping(value = "/organizeScope")
    @RequiresPermissions(value = {Auth.SYS_ROLE_ADD, Auth.SYS_ROLE_AUTH}, logical = Logical.OR)
    public AjaxResult getOrganizeScope() {
        return success(TreeUtil.buildTree(organizeService.selectOrganizeScope()));
    }

    /**
     * 获取下拉树列表
     */
    @GetMapping("/option")
    public AjaxResult option() {
        return success(organizeService.selectOrganizeTreeExDeptNode());
    }

    /**
     * 获取下拉树列表 | 远程调用
     */
    @InnerAuth
    @GetMapping("/remoteOption")
    public AjaxResult remoteOption(String sourceName) {
        SecurityContextHolder.setSourceName(sourceName);
        List<SysOrganizeTree> sysOrganizeTrees = organizeService.selectOrganizeTreeExDeptNode();
        return success(sysOrganizeTrees);
    }

}
