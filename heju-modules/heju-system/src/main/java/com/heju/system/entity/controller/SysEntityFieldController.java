package com.heju.system.entity.controller;


import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.query.SysEntityFieldQuery;
import com.heju.system.entity.service.ISysEntityFieldService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 实体字段管理管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/entityField")
public class SysEntityFieldController extends BaseController<SysEntityFieldQuery, SysEntityFieldDto, ISysEntityFieldService> {
    @Resource
    private ISysEntityFieldService ISysEntityFieldService;

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "实体字段管理";
    }

    /**
     * 查询实体字段管理列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ENTITY_FIELD_LIST)
    public AjaxResult list(SysEntityFieldQuery entityField) {
        return super.list(entityField);
    }


    /**
     * 查询所有实体字段列表分类与分类所对应的字段
     */
    @RequiresPermissions(Auth.SYS_ENTITY_FIELD_LIST)
    @GetMapping("/AllList")
    public AjaxResult AllList(@RequestParam(value = "id",required = false )Long id){
        HashMap<String,List<SysEntityFieldPo>> hashMap  =  ISysEntityFieldService.list(id);
        return new AjaxResult(200,"success","查询所有字段",hashMap);
    }


    /**
     * 查询实体字段管理详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_ENTITY_FIELD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 实体字段管理新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_ENTITY_FIELD_ADD)
    @Log(title = "实体字段管理管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntityFieldDto entityField) {
        entityField.setIsChange(1);
        return super.add(entityField);
    }

    /**
     * 实体字段管理修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_ENTITY_FIELD_EDIT)
    @Log(title = "实体字段管理管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysEntityFieldDto entityField) {
        return super.edit(entityField);
    }

    /**
     * 实体字段管理修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_ENTITY_FIELD_EDIT, Auth.SYS_ENTITY_FIELD_ES}, logical = Logical.OR)
    @Log(title = "实体字段管理管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysEntityFieldDto entityField) {
        return super.editStatus(entityField);
    }

    /**
     * 实体字段管理批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_ENTITY_FIELD_DEL)
    @Log(title = "实体字段管理管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取实体字段管理选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 根据所属模块查询实体字段
     */
    @PostMapping("/listByBelong")
    public AjaxResult listByBelong(@RequestBody SysEntityFieldQuery entityField) {
        return AjaxResult.success(ISysEntityFieldService.listByBelong(entityField));
    }
}
