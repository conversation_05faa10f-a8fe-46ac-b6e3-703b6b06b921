package com.heju.system.forms.cascade.manager;

import cn.hutool.core.lang.tree.Tree;
import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.po.CascadeTree;
import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;

import java.util.List;

/**
 * 级联管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysCascadeManager extends IBaseManager<SysCascadeQuery, SysCascadeDto> {

    void insertCascadeRelation(List<CascadeTree> cascadeTreeList, Long cascadeId);

    void deleteCascadeRelation(Long cascadeId);

    List<Tree<String>> selectCascadeRelation(Long cascadeId);
}