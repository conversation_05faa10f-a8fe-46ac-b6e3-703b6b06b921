package com.heju.system.organize.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.utils.core.DesensitizedUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.datascope.annotation.DataScope;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.query.SysUserQuery;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.manager.ISysCompanyManager;
import com.heju.system.organize.manager.ISysPostManager;
import com.heju.system.organize.manager.impl.SysUserManagerImpl;
import com.heju.system.organize.mapper.SysUserMapper;
import com.heju.system.organize.service.ISysEnterpriseService;
import com.heju.system.organize.service.ISysUserService;
import com.heju.system.third.manager.ISysThirdAuthManager;
import com.heju.system.utils.OperateEnum;
import com.heju.system.utils.OrganizeEnum;
import com.heju.tenant.api.source.feign.RemoteSourceService;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.feign.RemoteInviteRegisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
@Isolate
public class SysUserServiceImpl extends BaseServiceImpl<SysUserQuery, SysUserDto, SysUserManagerImpl> implements ISysUserService {

    @Resource
    SysUserMapper userMapper;

    @Resource
    private ISysCompanyManager sysCompanyManager;

    @Resource
    private ISysThirdAuthManager thirdAuthManager;

    @Resource
    private ISysPostManager postManager;

    @Resource
    private ISysEnterpriseService sysEnterpriseService;

    @Resource
    private SysUserMapper sysBaseUserMapper;

    @Resource
    private RemoteInviteRegisService inviteRegisService;

    /**
     * 用户登录校验 | 查询用户信息
     *
     * @param userName 用户账号
     * @param password 密码
     * @return 用户对象
     */
    @Override
    public SysUserDto userLogin(String userName, String password) {
        return baseManager.userLogin(userName, password);
    }

    /**
     * 新增用户 | 内部调用
     *
     * @param user 用户对象
     * @return 结果
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int addInner(SysUserDto user) {
        return super.insert(user);
    }

    @Override
    public int addInnerUser(SysUserDto user,String sourceName) {
        if (sourceName.contains(",")){
            String[] split = sourceName.split(",");
            SecurityContextHolder.setSourceName(split[0]);
        }
        SecurityContextHolder.setSourceName(sourceName);
        return baseManager.insert(user);
    }

    @Override
    public SysUserDto innerAddUser(SysUserDto user,String sourceName) {
        if (sourceName.contains(",")){
            String[] split = sourceName.split(",");
            SecurityContextHolder.setSourceName(split[0]);
        }
        SecurityContextHolder.setSourceName(sourceName);
        baseManager.insert(user);
        return user;
    }

    @Override
    public SysUserDto insertGetId(SysUserDto userDto) {
        return userMapper.insertGetId(userDto);
    }


    /**
     * 更改用户绑定微信
     * @param openId openId
     * @param phone 手机号
     * @return 成功/失败
     */
    @Override
    public AjaxResult editWechat(String openId, String phone) {
        BaseUserDto dto = new BaseUserDto();
        dto.setOpenId(openId);
        dto.setTelephone(phone);
        return inviteRegisService.editWechatBind(SecurityConstants.INNER, dto);
    }

    @Override
    public SysUserDto selectByOpenId(String openId,String sourceName) {
        SecurityContextHolder.setSourceName(sourceName);
        return userMapper.selectByOpenId(openId);
    }

    /**
     * 查询用户对象列表 | 数据权限 | 附加数据
     *
     * @param user 用户对象
     * @return 用户对象集合
     */
    @Override
    @DataScope(userAlias = "id", mapperScope = {"SysUserMapper"})
    public List<SysUserDto> selectListScope(SysUserQuery user) {
        return baseManager.selectList(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param id       用户Id
     * @param nickName 用户昵称
     * @param sex      用户性别
     * @param profile  个人简介
     * @return 结果
     */
    @Override
    public int updateUserProfile(Long id, String nickName, String sex, String profile) {
        return baseManager.updateUserProfile(id, nickName, sex, profile);
    }

    /**
     * 更新用户账号
     *
     * @param id       用户Id
     * @param userName 用户账号
     * @return 结果
     */
    @Override
    public int updateUserName(Long id, String userName) {
        return baseManager.updateUserName(id, userName);
    }

    /**
     * 更新用户邮箱
     *
     * @param id    用户Id
     * @param email 邮箱
     * @return 结果
     */
    @Override
    public int updateEmail(Long id, String email) {
        return baseManager.updateEmail(id, email);
    }

    /**
     * 更新用户手机号
     *
     * @param id    用户Id
     * @param phone 手机号
     * @return 结果
     */
    @Override
    public int updatePhone(Long id, String phone) {
        return baseManager.updatePhone(id, phone);
    }

    /**
     * 修改用户头像
     *
     * @param id     用户Id
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public int updateUserAvatar(Long id, String avatar) {
        return baseManager.updateUserAvatar(id, avatar);
    }

    /**
     * 重置用户密码
     *
     * @param id       用户Id
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPassword(Long id, String password) {
        return baseManager.resetUserPassword(id, password);
    }

    /**
     * 校验用户编码是否唯一
     *
     * @param Id       用户Id
     * @param userCode 用户编码
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkUserCodeUnique(Long Id, String userCode) {
        return ObjectUtil.isNotNull(baseManager.checkUserCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, userCode));
    }

    /**
     * 校验用户账号是否唯一
     *
     * @param id       Id
     * @param userName 用户账号
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkUserNameUnique(Serializable id, String userName) {
        return ObjectUtil.isNotNull(baseManager.checkUserNameUnique(ObjectUtil.isNull(id) ? BaseConstants.NONE_ID : id, userName));
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param id    用户Id
     * @param phone 手机号码
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkPhoneUnique(Long id, String phone) {
        return ObjectUtil.isNotNull(baseManager.checkPhoneUnique(ObjectUtil.isNull(id) ? BaseConstants.NONE_ID : id, phone));
    }

    /**
     * 校验email是否唯一
     *
     * @param id    用户Id
     * @param email email
     * @return 结果 | true/false 唯一/不唯一
     */
    @Override
    public boolean checkEmailUnique(Long id, String email) {
        return ObjectUtil.isNotNull(baseManager.checkEmailUnique(ObjectUtil.isNull(id) ? BaseConstants.NONE_ID : id, email));
    }

    /**
     * 校验用户是否允许操作
     *
     * @param id 用户Id
     * @return 结果 | true/false 允许/禁止
     */
    @Override
    public boolean checkUserAllowed(Long id) {
        SysUserDto user = baseManager.selectById(id);
        return SysUserDto.isNotAdmin(user.getUserType());
    }

    /**
     * 用户数据脱敏
     *
     * @param user 用户对象
     */
    @Override
    public void userDesensitized(SysUserDto user) {
        user.setPhone(DesensitizedUtil.mobilePhone(user.getPhone()));
        user.setEmail(DesensitizedUtil.email(user.getEmail()));
        user.setPassword(StrUtil.EMPTY);
    }

    @Override
    public List<SysUserDto> selectListByPostIds(List<Long> postIdList) {
        return baseManager.getUserByPost(postIdList);
    }

    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysUserDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        String phone = dto.getPhone();
        if(row>0){
            String id = sysEnterpriseService.getIdByTelephone(phone);
            if(id==null){
                UUID randomUUID = UUID.randomUUID();
                id=randomUUID.toString().replaceAll("-", "");
                if(sysEnterpriseService.insertByTelephone(id,phone)<=0){
                    throw new RuntimeException();
                }
            }
            sysEnterpriseService.insertTenantUser(dto.getId(),SecurityUtils.getEnterpriseId(),id);
        }
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        List<SysPostDto> posts = postManager.selectListByIds(Arrays.asList(dto.getPostIds()));
        List<Long> companyIds=posts.stream().map(SysPostDto::getCompanyId).toList().stream().distinct().collect(Collectors.toList());
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthManager.getByCompanyIds(companyIds);
        sysCompanyManager.buildOrganizeUserOne(OperateEnum.ADD.getCode(), OrganizeEnum.USER.getCode(),companyThirdAuthMergeList, dto);
        return row;
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysUserDto dto) {
        SysUserDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        List<SysPostDto> nowPosts = dto.getPosts();
        List<Long> nowCompanyIds= new ArrayList<>(nowPosts.stream().map(SysPostDto::getCompanyId).toList().stream().distinct().toList());
        List<SysPostDto> oldPosts = originDto.getPosts();
        List<Long> oldCompanyIds=oldPosts.stream().map(SysPostDto::getCompanyId).toList().stream().distinct().toList();
        //取新公司list中不包含老公司list
        List<Long> addCompany=nowCompanyIds.stream().filter(item -> !oldCompanyIds.contains(item)).toList();
        //取老公司list中不包含新公司list
        List<Long> delCompany=oldCompanyIds.stream().filter(item -> !nowCompanyIds.contains(item)).toList();
        //取新老公司交集
        nowCompanyIds.retainAll(oldCompanyIds);
        sysCompanyManager.buildOrganizeUserOne(nowCompanyIds,addCompany,delCompany,originDto,dto);
        return row;
    }

    /**
     * 根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    @Override
    @DSTransactional
    public int deleteByIds(Collection<? extends Serializable> idList) {
        List<SysUserDto> originList = selectListByIds(idList);
        startBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, originList, null);
        int rows = baseManager.deleteByIds(idList);
        endBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, rows, originList, null);
        sysCompanyManager.buildUser(originList);
        return rows;
    }

    @Override
    public SysUserDto loginByUserId(Long userId) {
        return baseManager.loginByUserId(userId);
    }

    /** 通过接收信息状态查询用户 */
    @Override
    public List<SysUserDto> selectListByReception(String reception) {
        return userMapper.selectByReception(reception);
    }

    @Override
    public SysUserDto selectBaseUserByOpenId(String openId) {
        return sysBaseUserMapper.selectBaseUserByOpenId(openId);
    }

    @Override
    public int setUserTenantMerge(Long baseUserId, Long tenantId, Long userId) {
        return sysBaseUserMapper.addUserTenantMerge(baseUserId,tenantId,userId);
    }


}
