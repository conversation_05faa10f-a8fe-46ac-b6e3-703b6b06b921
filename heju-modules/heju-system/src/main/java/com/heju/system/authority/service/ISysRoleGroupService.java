package com.heju.system.authority.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.query.SysRoleGroupQuery;
import com.heju.system.api.organize.domain.po.SysUserPo;

import java.util.List;

/**
 * 角色组管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysRoleGroupService extends IBaseService<SysRoleGroupQuery, SysRoleGroupDto> {
    List<SysRoleGroupPo> getUserRoleGroup();


    List<SysUserPo> getUser();

    List<SysRoleGroupDto>  getUserRoleGroupAndRole();
}