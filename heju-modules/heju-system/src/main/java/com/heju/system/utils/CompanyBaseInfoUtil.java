package com.heju.system.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.heju.system.entity.domain.dto.CompanyAbnormalInfoListDto;
import com.heju.system.entity.domain.dto.CompanyBaseInfoApiDto;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import java.util.HashMap;
import java.util.Map;

public class CompanyBaseInfoUtil {
    public static CompanyBaseInfoApiDto getCompanyBaseInfo(String entityName) {
        String host = HttpConstants.SAIC_URL;
        String path = HttpConstants.SAIC_PATH+entityName+"/";
        String method = HttpConstants.GET;
        String appcode = HttpConstants.APPCODE_VALUE;
        Map<String, String> headers = new HashMap<>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put(HttpConstants.AUTHORIZATION_HEADER, HttpConstants.APPCODE+ appcode);
        CompanyBaseInfoApiDto companyBaseInfoApiDto=new CompanyBaseInfoApiDto();
        try {
            HttpResponse response = HttpUtils.doGet(host, path, method, headers, new HashMap<>());
            //获取response的body
            JSONObject json= JSON.parseObject(EntityUtils.toString(response.getEntity()));
            boolean status = Boolean.parseBoolean(json.get("status").toString());
            if(status){
                JSONObject jsonData=JSON.parseObject(json.get("data").toString());
                companyBaseInfoApiDto=JSONObject.toJavaObject(jsonData, CompanyBaseInfoApiDto.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return companyBaseInfoApiDto;
    }

    public static CompanyAbnormalInfoListDto getCompanyAbnormalInfo(String companyName) {
        String host = HttpConstants.SAIC_URL;
        String path = HttpConstants.SAIC_PATH2+companyName+"/";
        String method = HttpConstants.GET;
        String appcode = HttpConstants.APPCODE_VALUE;
        Map<String, String> headers = new HashMap<>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put(HttpConstants.AUTHORIZATION_HEADER, HttpConstants.APPCODE+ appcode);
        CompanyAbnormalInfoListDto companyAbnormalInfoListDto=new CompanyAbnormalInfoListDto();
        try {
            HttpResponse response = HttpUtils.doGet(host, path, method, headers, new HashMap<>());
            //获取response的body
            JSONObject json= JSON.parseObject(EntityUtils.toString(response.getEntity()));
            boolean status = Boolean.parseBoolean(json.get("status").toString());
            if(status){
                Object data = json.get("data");
                if (data instanceof String && "该公司暂时没有经营异常信息".equals(data)) {
                    // 处理没有经营异常信息的情况，可以根据需求返回特定的 DTO 或者进行其他操作
                    companyAbnormalInfoListDto.setList(null);
                    companyAbnormalInfoListDto.setTotal(0);
                }else {
                    JSONObject jsonData = (JSONObject) data;
                    companyAbnormalInfoListDto = JSONObject.toJavaObject(jsonData, CompanyAbnormalInfoListDto.class);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return companyAbnormalInfoListDto;
    }
}
