package com.heju.system.third.domain.dto;

import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 第三方模块 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysCompanyThirdAuthRedisDto extends SysCompanyThirdAuthMerge {

    @Serial
    private static final long serialVersionUID = 1L;


    protected String companyIds;

}