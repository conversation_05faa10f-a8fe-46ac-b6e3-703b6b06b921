package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.system.entity.domain.po.SysEntitySaicEmployeePo;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.query.SysEntitySaicEmployeeQuery;
import com.heju.system.entity.domain.model.SysEntitySaicEmployeeConverter;
import com.heju.system.entity.mapper.SysEntitySaicEmployeeMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntitySaicEmployeeManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体工商董事会成员管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntitySaicEmployeeManager extends BaseManagerImpl<SysEntitySaicEmployeeQuery, SysEntitySaicEmployeeDto, SysEntitySaicEmployeePo, SysEntitySaicEmployeeMapper, SysEntitySaicEmployeeConverter> implements ISysEntitySaicEmployeeManager {
    @Override
    public List<SysEntitySaicEmployeePo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntitySaicEmployeePo> lambdaEmployee = new LambdaQueryWrapper<>();
        lambdaEmployee.eq(SysEntitySaicEmployeePo::getEntityId,id);
        return baseMapper.selectList(lambdaEmployee);
    }
}