package com.heju.system.company.domain.dto;

import com.heju.common.core.annotation.Correlation;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.web.validate.V_A_E;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.company.domain.po.SysCompanyPo;
import com.heju.system.third.domain.dto.SysThirdDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

import static com.heju.system.api.organize.domain.merge.MergeGroup.Company_SysCompanyThirdMerge_GROUP;
import static com.heju.system.api.organize.domain.merge.MergeGroup.USER_SysUserPostMerge_GROUP;

/**
 * 子公司 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysCompanyDto extends SysCompanyPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 岗位对象 */
    @Correlation(groupName = Company_SysCompanyThirdMerge_GROUP, keyType = OperateConstants.SubKeyType.RECEIVE)
    private List<SysThirdDto> thirds;

    /** 第三方模块组 */
    @Correlation(groupName = Company_SysCompanyThirdMerge_GROUP, keyType = OperateConstants.SubKeyType.RECEIVE_ARR)
    private Long[] thirdIds;




}