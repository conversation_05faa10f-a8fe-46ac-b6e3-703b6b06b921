package com.heju.system.declaration.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.system.ReportConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.system.declaration.domain.dto.TaxFilingsView;
import com.heju.system.declaration.domain.query.SysTaxFilingsQuery;
import com.heju.system.declaration.manager.impl.SysTaxFilingsManager;
import com.heju.system.declaration.mapper.SysTaxFilingsMapper;
import com.heju.system.declaration.service.ISysTaxFilingsService;
import com.heju.system.declaration.manager.ISysTaxFilingsManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.service.impl.SysEntityServiceImpl;
import com.heju.system.organize.service.impl.SysUserServiceImpl;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 税务申报管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysTaxFilingsServiceImpl extends BaseServiceImpl<SysTaxFilingsQuery, SysTaxFilingsDto, ISysTaxFilingsManager> implements ISysTaxFilingsService {

    @Autowired
    SysEntityServiceImpl entityService;

    @Autowired
    SysUserServiceImpl userService;

    @Autowired
    SysTaxFilingsMapper taxFilingsMapper;

    @Autowired
    SysTaxFilingsManager taxFilingsManager;


    /**
     * 查询税务申报对象列表 | 数据权限
     *
     * @param taxFilings 税务申报对象
     * @return 税务申报对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysTaxFilingsMapper"})
    public List<SysTaxFilingsDto> selectListScope(SysTaxFilingsQuery taxFilings) {
        List<SysTaxFilingsDto> TaxFilingsDtoList = baseManager.selectList(taxFilings);
        for (SysTaxFilingsDto dto : TaxFilingsDtoList) {
            SysEntityPo sysEntityPo = entityService.selectById(dto.getEntityId());
            if (!Objects.isNull(sysEntityPo)) {
                dto.setEntityName(sysEntityPo.getName());
            }
            SysUserDto sysUserDto = userService.selectById(dto.getCreateBy());
            if (!Objects.isNull(sysUserDto)){
                dto.setDeclareBy(sysUserDto.getNickName());
            }
        }
        return TaxFilingsDtoList;
    }

    /**
     * 新增数据对象(重写)
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysTaxFilingsDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        //获取时间类型
        String timeType = dto.getReporttimeType();
        if (timeType.equals(ReportConstants.TimeType.MONTH.getCode())){
            //月度
            String yMd = dto.getMonth().split("T")[0];
            String[] end = yMd.split("-");
            dto.setYear(end[0]);
            dto.setMonth(end[1]);
        }
        if (dto.getSeason() != null && timeType.equals(ReportConstants.TimeType.SEASON.getCode())){
            //季度
            String[] yearAndSeason = dto.getSeason().split("-");
            dto.setYear(yearAndSeason[0]);
            int season = Character.getNumericValue(yearAndSeason[1].toCharArray()[1])-1;
            dto.setSeason(String.valueOf(season));
        }
        SysTaxFilingsDto temp = taxFilingsMapper.selectByCondition(dto);
        if (!Objects.isNull(temp)){
            throw new RuntimeException("您已申报过该类型及时间的信息");
        }
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        return row;
    }


    @Override
    @DSTransactional
    public int update(SysTaxFilingsDto dto) {
        SysTaxFilingsDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        String timeType = dto.getReporttimeType();
        if (timeType.equals(ReportConstants.TimeType.SEASON.getCode())){
            //时间类型为季度
            if (dto.getSeason() != null){
                String[] yearAndSeason = dto.getSeason().split("-");
                dto.setYear(yearAndSeason[0]);
                int season = Character.getNumericValue(yearAndSeason[1].toCharArray()[1])-1;
                dto.setSeason(String.valueOf(season));
                dto.setMonth(null);
            }
        }else if (timeType.equals(ReportConstants.TimeType.MONTH.getCode())){
            //时间类型为月度
            String yMd = dto.getMonth().split("T")[0];
            String[] end = yMd.split("-");
            dto.setYear(end[0]);
            dto.setMonth(end[1]);
            dto.setSeason(null);
        }else if (timeType.equals(ReportConstants.TimeType.YEARS.getCode())){
            //时间类型为年度
            String year = dto.getYear().split("-")[0];
            dto.setYear(year);
            dto.setMonth(null);
            dto.setSeason(null);
        }

        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        return row;
    }

    public List<SysTaxFilingsQuery> getAll(SysTaxFilingsQuery query) {
        return taxFilingsManager.Conformity(query);
    }

    public List<SysReportManagementQuery> selectByCondition(SysReportManagementQuery query) {
        return taxFilingsMapper.selectByQuery(query);
    }
}