package com.heju.system.utils;

import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;

import java.util.ArrayList;
import java.util.List;

public class MessageUtil {

    public static List<SysMessageDto> packingMessage(String entityName, String TypeName, String type, String before, String after, Long send, List<SysUserDto> userList,Long changeMessageId) {
        List<SysMessageDto> messageList = new ArrayList<>();
        for (SysUserDto sysUserDto : userList) {
            SysMessageDto message = new SysMessageDto();
            message.setTitle(entityName + TypeName + "通知");//设置消息标题
            message.setType(Integer.parseInt(type));//设置消息种类
            message.packageMessageContent(TypeName, before, after);
            message.setSendUserId(send);
            message.setReceiveUserId(sysUserDto.getId());
            message.setChangeMessageId(changeMessageId);
            messageList.add(message);
        }
        return messageList;
    }

    public static SysMessageEntityChangeDto packDetails(Long entityId, String changeField, String before, String after) {
        SysMessageEntityChangeDto changeMessage = new SysMessageEntityChangeDto();
//        StringBuilder messageId = new StringBuilder();
//        for (int i = 0; i < messages.size(); i++) {
//            messageId.append(messages.get(i).getId());
//            if (i < messages.size() - 1) {
//                messageId.append(",");
//            }
//        }
//        changeMessage.setMessageId(messageId.toString());
        changeMessage.setEntityId(entityId);//设置实体id
        changeMessage.setChangeField(changeField); //变更字段
        changeMessage.setChangeBefore(before); //变更前
        changeMessage.setChangeAfter(after); //变更后
        return changeMessage;
    }

    public static SysMessageDto packView(SysMessageDto messageDto,SysMessageEntityChangeDto messageEntityChangeDto){
        messageDto.setChangeField(messageEntityChangeDto.getChangeField());//变更字段
        messageDto.setChangeBefore(messageEntityChangeDto.getChangeBefore());//变更前
        messageDto.setChangeAfter(messageEntityChangeDto.getChangeAfter());//变更后
        return messageDto;
    }

}
