package com.heju.system.entity.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 实体字段管理 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_field", excludeProperty = { NAME })
public class SysEntityFieldPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 字段中文名 */
    @Excel(name = "字段中文名")
    protected String fieldComment;

    /** 字段编码 */
    @Excel(name = "字段编码")
    protected String fieldName;

    /** 字段类型 */
    @Excel(name = "字段类型")
    protected String fieldType;

    /** 字段长度 */
    @Excel(name = "字段长度")
    protected String fieldLength;

    /** 字段所属模块 */
    @Excel(name = "字段所属模块")
    protected String fieldBelong;

    /** 是否可变 */
    @Excel(name = "是否可变")
    protected Integer isChange;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}