package com.heju.system.phoneinfo.domain.po;

import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 转发短信 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_telephone_code", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, UPDATE_TIME, REMARK, NAME })
public class SysTelephoneCodePo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**  */
    @Excel(name = "")
    protected String source;

    /**  */
    @Excel(name = "")
    protected String msgContent;

    /**  */
    @Excel(name = "")
    protected String receive;

    /**
     * 验证码
     */
    @Excel(name = "验证码")
    private String verificationCode;

}
