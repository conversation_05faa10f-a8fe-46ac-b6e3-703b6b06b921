package com.heju.system.entity.domain;

import lombok.Data;

/**
 * 实体信息所属模块常数类
 */

@Data
public class FieldBelongConstant {

    //工商基础信息
    public static final String SAIC_BASIC="1";

    //财税基础信息
    public static final String TAXATION_BASIC="2";

    //税控软件信息
    public static final String TAXATION_SOFTWARE="3";

    //工商其他信息
    public static final String SAIC_OTHER="4";

    //税务其他信息
    public static final String TAXATION_OTHER="5";

    //税务人员信息
    public static final String TAXATION_USER="6";

    //税费种认定信息
    public static final String TAXATION_TYPE="7";

    //票种认定信息
    public static final String INVOICE_TYPE="8";

    //股东信息
    public static final String SAIC_PARTNER="9";

    //董事会成员信息
    public static final String SAIC_EMPLOYEE="10";

    //分支机构
    public static final String SAIC_BRANCH="11";

    //工商变更记录
    public static final String SAIC_CHANGE="12";

}
