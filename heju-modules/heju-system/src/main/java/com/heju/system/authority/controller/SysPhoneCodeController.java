package com.heju.system.authority.controller;


import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.system.authority.domain.TelephoneCode;
import com.heju.system.authority.service.SysPhoneCodeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class SysPhoneCodeController {

    @Resource
    private SysPhoneCodeService sysPhoneCodeService;

    /**
     * 从redis中获取手机号与验证码
     */
    @GetMapping("/getPhoneAndCode")
    @RequiresPermissions(Auth.SYS_PHONE_CODE)
    public AjaxResult getCode(){
        List<TelephoneCode> telephoneCodePairPoList= sysPhoneCodeService.getPhoneAndCode();
        return AjaxResult.success(telephoneCodePairPoList);
    }
}
