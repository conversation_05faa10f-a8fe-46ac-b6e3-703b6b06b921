package com.heju.system.approval.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;

import java.util.List;
import java.util.Set;

/**
 * 客户信息审核管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysApprovalCustomerinfoManager extends IBaseManager<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto> {

    List<SysApprovalCustomerinfoDto> selectByQuery(SysApprovalCustomerinfoQuery query);

    List<SysApprovalCustomerinfoDto> selectBusinessName(String apiName, Set<Long> businessIds);

    SysApprovalCustomerinfoDto selectBusinessNameById(String apiName, Long businessId);
}