package com.heju.system.report.domain.query;

import com.alibaba.druid.sql.visitor.functions.Char;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.po.SysReportManagementPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysReportManagementQuery extends SysReportManagementPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 实体id
     */
    private Long entityId;
    /**
     * 实体名称
     */
    private String entityName;
    /**
     * 报表种类
     */
    private String reportClass;
    /**
     * 报表子类型
     */
    private String reporttypeType;
    /**
     * 报表时间类型
     */
    private String reporttimeType;
    /**
     * 银行报表集合
     */
    private List<SysBankReportDto> bankReports;
    /**
     * 发票集合
     */
    private List<SysBillReportDto> billReports;
    /**
     * 财税报表集合
     */
    private List<SysFinanceReportDto> financeReports;
    /**
     * 税务申报报表集合
     */
    private List<SysTaxReportDto> taxReports;

    private int page;

    private int pageSize;
}
