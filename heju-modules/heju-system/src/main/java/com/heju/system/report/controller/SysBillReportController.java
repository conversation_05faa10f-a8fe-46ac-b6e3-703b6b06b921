package com.heju.system.report.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.query.SysBillReportQuery;
import com.heju.system.report.service.ISysBillReportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 发票管理详情管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bill")
public class SysBillReportController extends BaseController<SysBillReportQuery, SysBillReportDto, ISysBillReportService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "发票管理详情" ;
    }

    /**
     * 查询发票管理详情列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_BILL_REPORT_LIST)
    public AjaxResult list(SysBillReportQuery billReport) {
        return super.list(billReport);
    }

    /**
     * 查询发票管理详情详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_BILL_REPORT_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 发票管理详情新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_BILL_REPORT_ADD)
    @Log(title = "发票管理详情管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysBillReportDto billReport) {
        return super.add(billReport);
    }

    /**
     * 发票管理详情修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_BILL_REPORT_EDIT)
    @Log(title = "发票管理详情管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysBillReportDto billReport) {
        return super.edit(billReport);
    }

    /**
     * 发票管理详情修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_BILL_REPORT_EDIT, Auth.SYS_BILL_REPORT_ES}, logical = Logical.OR)
    @Log(title = "发票管理详情管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysBillReportDto billReport) {
        return super.editStatus(billReport);
    }

    /**
     * 发票管理详情批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_BILL_REPORT_DEL)
    @Log(title = "发票管理详情管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取发票管理详情选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


}
