package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体税务税费种认定 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityTaxationTypeDto extends SysEntityTaxationTypePo {

    @Serial
    private static final long serialVersionUID = 1L;

}