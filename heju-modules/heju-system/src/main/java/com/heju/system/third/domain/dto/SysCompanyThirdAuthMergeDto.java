package com.heju.system.third.domain.dto;

import com.heju.system.company.domain.dto.SysCompanyDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 公司第三方认证关联
 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysCompanyThirdAuthMergeDto extends SysCompanyDto {

    @Serial
    private static final long serialVersionUID = 1L;

    protected Long companyId;

    /** 第三方认证id */
    protected Long thirdAuthId;

    /** 第三方认证name */
    protected String thirdAuthName;

    /**  是否是超管  */
    protected String isAdmin;

    /**  第三方id  */
    protected Long thirdId;

}