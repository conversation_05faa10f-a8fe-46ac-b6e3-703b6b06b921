package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体工商分支机构 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntitySaicBranchDto extends SysEntitySaicBranchPo {

    @Serial
    private static final long serialVersionUID = 1L;

}