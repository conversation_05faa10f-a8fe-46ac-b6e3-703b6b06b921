package com.heju.system.declaration.domain.dto;

import com.heju.common.core.constant.system.TaxFilingsConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class TaxFilingsView extends SysTaxFilingsDto {

    /**
     * 税务子类型各时间类型信息
     */

    /** 税种月度申报状态Map集合(Key:月份 Value:申报状态) */
    private Map<String, String> monthTax;

    /** 税种季度申报状态Map集合(Key:季度 Value:申报状态) */
    private Map<String, String> seasonTax;

    /** 税种年度申报状态Map集合(Key:年份 Value:申报状态) */
    private Map<String, String> yearTax;

    /** 初始化代码块 */
    public TaxFilingsView() {
        this.monthTax = new HashMap<>();
        this.seasonTax = new HashMap<>();
        this.yearTax = new HashMap<>();
        initMonthTax();
        initSeasonTax();
    }

    /** 月度状态Map初始化方法 */
    private void initMonthTax() {
        for (int i = 0; i <= Integer.parseInt(TaxFilingsConstants.TimeType.MONTH_NUMBER.getCode()) - 1; i++) {
            monthTax.put(i + "", TaxFilingsConstants.TaxStatus.NOT_DECLARED.getCode());
        }
    }

    /** 季度状态Map初始化方法 */
    private void initSeasonTax() {
        for (int i = 0; i <= Integer.parseInt(TaxFilingsConstants.TimeType.SEASON_NUMBER.getCode()) - 1; i++) {
            seasonTax.put(i + "", TaxFilingsConstants.TaxStatus.NOT_DECLARED.getCode());
        }
    }

    /** 初始化年度申报Map */
    public void initYearTax(String year) {
        yearTax.put(year, TaxFilingsConstants.TaxStatus.NOT_DECLARED.getCode());
    }

    /** 更新月度状态Map方法 */
    public void updateMonthTax(List<SysTaxFilingsDto> taxFilingsDtoList) {
        for (SysTaxFilingsDto sysTaxFilingsDto : taxFilingsDtoList) {
            String month =  sysTaxFilingsDto.getMonth();
            if (month.startsWith("0")) {
                month = month.substring(1);
            }
            monthTax.put(String.valueOf(Integer.parseInt(month)-1), sysTaxFilingsDto.getDeclareStatus());
        }
    }

    /** 更新季度状态Map方法 */
    public void updateSeasonTax(List<SysTaxFilingsDto> taxFilingsDtoList) {
        for (SysTaxFilingsDto sysTaxFilingsDto : taxFilingsDtoList) {
            seasonTax.put(sysTaxFilingsDto.getSeason(), sysTaxFilingsDto.getDeclareStatus());
        }
    }

    /** 更新年度状态Map方法 */
    public void updateYearTax(List<SysTaxFilingsDto> taxFilingsDtoList) {
        for (SysTaxFilingsDto sysTaxFilingsDto : taxFilingsDtoList) {
            yearTax.put(sysTaxFilingsDto.getYear(), sysTaxFilingsDto.getDeclareStatus());
        }
    }
}
