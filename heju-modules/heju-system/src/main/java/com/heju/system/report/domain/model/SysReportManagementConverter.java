package com.heju.system.report.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.po.SysReportManagementPo;
import com.heju.system.report.domain.query.SysReportManagementQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysReportManagementConverter extends BaseConverter<SysReportManagementQuery, SysReportManagementDto, SysReportManagementPo> {
}
