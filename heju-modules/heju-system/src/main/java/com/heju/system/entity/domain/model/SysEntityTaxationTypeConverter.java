package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import com.heju.system.entity.domain.query.SysEntityTaxationTypeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体税务税费种认定 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntityTaxationTypeConverter extends BaseConverter<SysEntityTaxationTypeQuery, SysEntityTaxationTypeDto, SysEntityTaxationTypePo> {
}
