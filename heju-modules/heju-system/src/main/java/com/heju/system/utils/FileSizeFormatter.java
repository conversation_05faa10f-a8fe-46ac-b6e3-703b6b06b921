package com.heju.system.utils;

import java.text.DecimalFormat;

public class FileSizeFormatter {
    public static String formatBytes(Long bytes) {
        if (bytes <= 0) return "0 B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int index = (int) Math.floor(Math.log(bytes) / Math.log(1024));

        // 防止超出单位数组范围
        index = Math.min(index, units.length - 1);

        double size = bytes / Math.pow(1024, index);
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(size) + " " + units[index];
    }
}
