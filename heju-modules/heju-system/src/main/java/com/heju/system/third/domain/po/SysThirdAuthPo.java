package com.heju.system.third.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.ArrayUtils;

import java.io.Serial;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 第三方认证 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_third_auth")
public class SysThirdAuthPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 三方认证信息编码 */
    @Excel(name = "三方认证信息编码")
    protected String code;

    /** 三方认证信息内容 */
    @Excel(name = "三方认证信息内容")
    protected String authJson;

    /** 是否超管（0是 1否） */
    @Excel(name = "是否超管", readConverterExp = "0=是,1=否")
    protected String isAdmin;

    /** 第三方模块id */
    @Excel(name = "第三方模块id")
    protected Long thirdId;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;


    public static void main(String[] args) {
//        String a = "[{\"authJson\":\"{\\\"t_id\\\":\\\"123\\\",\\\"t_name1\\\":\\\"超管moka2\\\",\\\"t_name2\\\":\\\"sssss\\\"}\",\"createBy\":1,\"id\":1744278995860148226,\"idStr\":\"1744278995860148226\",\"isAdmin\":\"0\",\"name\":\"超管moka2\",\"operateType\":1,\"organizeInfo\":\"{\\\"ancestors\\\":\\\"0\\\",\\\"childAncestors\\\":\\\"0,1748169700701560833\\\",\\\"code\\\":\\\"211\\\",\\\"companyId\\\":1744279565459214337,\\\"companyName\\\":\\\"测试公司2\\\",\\\"createBy\\\":1,\\\"id\\\":1748169700701560833,\\\"idStr\\\":\\\"1748169700701560833\\\",\\\"level\\\":1,\\\"levelChange\\\":1,\\\"name\\\":\\\"测试部门2-11\\\",\\\"oldChildAncestors\\\":\\\"null,1748169700701560833\\\",\\\"operate\\\":\\\"ADD\\\",\\\"parentId\\\":0,\\\"sort\\\":0,\\\"status\\\":\\\"0\\\"}\",\"organizeType\":2,\"sort\":1,\"status\":\"0\",\"thirdId\":1744278033724559362},{\"authJson\":\"{\\\"t_id\\\":\\\"1202\\\",\\\"t_name1\\\":\\\"租户企微2\\\"}\",\"createBy\":1,\"id\":1744279884050157569,\"idStr\":\"1744279884050157569\",\"isAdmin\":\"1\",\"name\":\"租户qiwe2\",\"operateType\":1,\"organizeInfo\":\"{\\\"ancestors\\\":\\\"0\\\",\\\"childAncestors\\\":\\\"0,1748169700701560833\\\",\\\"code\\\":\\\"211\\\",\\\"companyId\\\":1744279565459214337,\\\"companyName\\\":\\\"测试公司2\\\",\\\"createBy\\\":1,\\\"id\\\":1748169700701560833,\\\"idStr\\\":\\\"1748169700701560833\\\",\\\"level\\\":1,\\\"levelChange\\\":1,\\\"name\\\":\\\"测试部门2-11\\\",\\\"oldChildAncestors\\\":\\\"null,1748169700701560833\\\",\\\"operate\\\":\\\"ADD\\\",\\\"parentId\\\":0,\\\"sort\\\":0,\\\"status\\\":\\\"0\\\"}\",\"organizeType\":2,\"sort\":1,\"status\":\"0\",\"thirdId\":1744277946449481729}]";
//        String b ="{\"t_id\":\"123\",\"t_name1\":\"超管moka2\",\"t_name2\":\"sssss\"}";
//        JSONArray.parseArray(a);
//        JSONObject.parse(b);
//        System.out.println(JSONArray.parseArray(a));
//        System.out.println(JSONObject.parse(b));

//
//        Map map = new HashMap<>();
//        map.put("a","aaaa");
//        map.put("b","aaaa");
//        map.put("c","aaaa");
//        System.out.println(map.containsKey("0")); //输出true 如果这个map集合中有这个key就返回true
//        System.out.println(map); //输出true 如果这个map集合中有这个key就返回true
//        System.out.println();

        List<SysUserPo> list = new ArrayList<SysUserPo>();

        SysUserPo dto = new SysUserPo();
        dto.setId(101L);
        dto.setUserName("a");
        list.add(dto);
        System.out.println(list);

        Set<SysUserPo> collect = list.stream().filter(i -> i.getId().toString().equals("101")).collect(Collectors.toSet());
        System.out.println(collect);

        String[] a = {"123321","15665","000111"};
        String[] b = {"101010","15665"};
        String[] both = (String[]) ArrayUtils.addAll(a, b);
        String ArrayString = Arrays.toString(a);//将数组转为字符串
        System.out.println(ArrayString);
        Set<String> set = new HashSet<>(Arrays.asList(both));
        String[] result = set.toArray(new String[0]);
        String bothString = Arrays.toString(result);//将数组转为字符串
        System.out.println(bothString);

        Map<String, String[]> map = new HashMap<>();

    }




}