package com.heju.system.annualReport.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;

import java.util.List;

/**
 * 工商年报管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysAnnualReportManager extends IBaseManager<SysAnnualReportQuery, SysAnnualReportDto> {

    /**
     * 查询工商年报列表 并按照公司ID和报告年份进行排序
     * @param annualReport 工商年报对象
     * @return 按照 entity_id 和 report_year 排序后的所有年度报告列表。
     */
    List<SysAnnualReportPo> selectByEntity(SysAnnualReportQuery annualReport);

    /**
     * 查询工商年报列表 排除未申报
     * @param annualReport 工商年报对象
     * @return 工商年报对象集合
     */
    List<SysAnnualReportPo> selectPartList(SysAnnualReportQuery annualReport);

    /**
     * 查询单个工商年报 通过entityId and reportYear
     * @param annualReport 工商年报对象
     * @return 工商年报对象
     */
    SysAnnualReportPo selectByEntityAndYear(SysAnnualReportDto annualReport);
}