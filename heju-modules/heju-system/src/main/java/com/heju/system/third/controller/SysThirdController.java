package com.heju.system.third.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.query.SysThirdQuery;
import com.heju.system.third.service.ISysThirdAuthService;
import com.heju.system.third.service.ISysThirdService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 第三方模块管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/third")
public class SysThirdController extends BaseController<SysThirdQuery, SysThirdDto, ISysThirdService> {

    @Resource
    private ISysThirdAuthService thirdAuthService;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "第三方模块" ;
    }

    /**
     * 查询第三方模块列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_THIRD_LIST)
    public AjaxResult list(SysThirdQuery third) {
        return super.list(third);
    }

    /**
     * 查询第三方模块详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_THIRD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 第三方模块新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_THIRD_ADD)
    @Log(title = "第三方模块管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysThirdDto third) {
        return super.add(third);
    }

    /**
     * 第三方模块修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_THIRD_EDIT)
    @Log(title = "第三方模块管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysThirdDto third) {
        return super.edit(third);
    }

    /**
     * 第三方模块修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_THIRD_EDIT, Auth.SYS_THIRD_ES}, logical = Logical.OR)
    @Log(title = "第三方模块管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysThirdDto third) {
        return super.editStatus(third);
    }

    /**
     * 第三方模块批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_THIRD_DEL)
    @Log(title = "第三方模块管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取第三方模块选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysThirdDto third) {
        if (baseService.checkThirdCodeUnique(third.getId(), third.getCode()))
            warn(StrUtil.format("{}{}{}失败，第三方编码已存在", operate.getInfo(), getNodeName(), third.getName()));
        else if (baseService.checkNameUnique(third.getId(), third.getName()))
            warn(StrUtil.format("{}{}{}失败，第三方名称已存在", operate.getInfo(), getNodeName(), third.getName()));
    }

    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        if(thirdAuthService.checkByThirdId(idList)){
            warn(StrUtil.format("{}{}{}失败，第三方模块已关联认证信息，请先删除认证信息",operate.getInfo(), getNodeName()));
        }
    }
}
