package com.heju.system.phone.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.po.SysPhoPermissionRecordPo;
import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 手机号授权 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysPhoPermissionRecordConverter extends BaseConverter<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto, SysPhoPermissionRecordPo> {
}
