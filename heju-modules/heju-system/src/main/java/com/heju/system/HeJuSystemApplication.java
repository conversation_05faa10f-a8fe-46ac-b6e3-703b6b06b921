package com.heju.system;

import com.heju.common.security.annotation.EnableCustomConfig;
import com.heju.common.security.annotation.EnableRyFeignClients;
import com.heju.common.swagger.annotation.EnableCustomSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 系统模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger
@EnableRyFeignClients
@SpringBootApplication
public class HeJuSystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(HeJuSystemApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " ('-. .-.          \n" +
                "( OO )  /          \n" +
                ",--. ,--.     ,--. \n" +
                "|  | |  | .-')| ,| \n" +
                "|   .|  |( OO |(_| \n" +
                "|       || `-'|  | \n" +
                "|  .-.  |,--. |  | \n" +
                "|  | |  ||  '-'  / \n" +
                "`--' `--' `-----'  \n");
    }
}