package com.heju.system.entity.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import com.heju.system.entity.domain.query.SysEntitySaicBranchQuery;

/**
 * 实体工商分支机构管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntitySaicBranchMapper extends BaseMapper<SysEntitySaicBranchQuery, SysEntitySaicBranchDto, SysEntitySaicBranchPo> {
}