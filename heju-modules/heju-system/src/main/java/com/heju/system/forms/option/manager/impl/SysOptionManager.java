package com.heju.system.forms.option.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.domain.model.SysOptionConverter;
import com.heju.system.forms.option.domain.po.SysOptionPo;
import com.heju.system.forms.option.domain.query.SysOptionQuery;
import com.heju.system.forms.option.manager.ISysOptionManager;
import com.heju.system.forms.option.mapper.SysOptionMapper;
import org.springframework.stereotype.Component;

/**
 * 选项管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysOptionManager extends BaseManagerImpl<SysOptionQuery, SysOptionDto, SysOptionPo, SysOptionMapper, SysOptionConverter> implements ISysOptionManager {
    @Override
    public SysOptionDto checkConfigCodeUnique(Long Id, String name) {
        SysOptionPo option = baseMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .ne(SysOptionPo::getId, Id)
                        .eq(SysOptionPo::getName, name)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(option);
    }
}