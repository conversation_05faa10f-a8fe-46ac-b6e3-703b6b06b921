package com.heju.system.entity.domain.model;

import com.heju.system.entity.domain.dto.*;
import com.heju.system.entity.domain.po.SysEntityPo;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class CompanyBaseInfoApiConverter {

    public static SysEntityPo companyBaseInfo2Entity(CompanyBaseInfoApiDto companyBaseInfoApiDto) {
        SysEntityPo sysEntityPo = new SysEntityPo();
        //解析工商基本信息
        EmployeeDataApiDto employeeData = companyBaseInfoApiDto.getEmployeeData();
        PartnerDataApiDto partnerData = companyBaseInfoApiDto.getPartnerData();
        ChangeRecordDataApiDto changeRecordData = companyBaseInfoApiDto.getChangeRecordData();
        RegisterDataApiDto registerData = companyBaseInfoApiDto.getRegisterData();
        sysEntityPo.setCreditNo(registerData.getCreditNo());
        sysEntityPo.setLegalPersonName(companyBaseInfoApiDto.getLegalPersonName());
        sysEntityPo.setRegisterCapital(registerData.getRegisterCapital());
        sysEntityPo.setRegisterCapitalCurrency(registerData.getRegisterCapitalCurrency());
        sysEntityPo.setRegType(registerData.getRegType());
        sysEntityPo.setName(companyBaseInfoApiDto.getName());
        sysEntityPo.setBusinessScope(registerData.getBusinessScope());
        sysEntityPo.setAddress(registerData.getAddress());
        sysEntityPo.setBusinessStatus(registerData.getStatus());
        sysEntityPo.setBusinessTerm(registerData.getBusinessTerm());
        sysEntityPo.setBelongOrg(registerData.getBelongOrg());
        sysEntityPo.setRegisterNo(registerData.getRegisterNo());
        try {
            sysEntityPo.setStartDate(LocalDate.parse(companyBaseInfoApiDto.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } catch (Exception e) {
            sysEntityPo.setStartDate(null);
        }

        sysEntityPo.setPartnerTotal(partnerData.getTotal());
        sysEntityPo.setEmployeeTotal(employeeData.getTotal());
        //解析分支机构信息
        List<SysEntitySaicBranchDto> branchPoList = new ArrayList<>();
        if (companyBaseInfoApiDto.getBranchDataApiDto() != null) {
            List<BranchDataListDto> branchDataList = companyBaseInfoApiDto.getBranchDataApiDto().getList();
            sysEntityPo.setBranchTotal(branchDataList.size());
            for (BranchDataListDto branchDataListDto : branchDataList) {
                SysEntitySaicBranchDto entitySaicBranchPo = new SysEntitySaicBranchDto();
                entitySaicBranchPo.setName(branchDataListDto.getName());
                branchPoList.add(entitySaicBranchPo);
            }
        }
        sysEntityPo.setBranchPoList(branchPoList);
        //解析董事会成员信息
        List<SysEntitySaicEmployeeDto> employeePoList = new ArrayList<>();
        if (employeeData.getTotal() > 0) {
            List<EmployeeDataListDto> list = employeeData.getList();
            for (EmployeeDataListDto employeeDataListDto : list) {
                SysEntitySaicEmployeeDto sysEntitySaicEmployeePo = new SysEntitySaicEmployeeDto();
                sysEntitySaicEmployeePo.setName(employeeDataListDto.getName());
                sysEntitySaicEmployeePo.setTitle(employeeDataListDto.getTitle());
                employeePoList.add(sysEntitySaicEmployeePo);
            }
        }
        sysEntityPo.setEmployeePoList(employeePoList);
        //解析股东信息
        List<SysEntitySaicPartnerDto> partnerPoList = new ArrayList<>();
        if (partnerData.getTotal() > 0) {
            List<PartnerDataListDto> list = partnerData.getList();
            for (PartnerDataListDto partnerDataListDto : list) {
                SysEntitySaicPartnerDto sysEntitySaicPartnerPo = new SysEntitySaicPartnerDto();
                sysEntitySaicPartnerPo.setPartnerName(partnerDataListDto.getPartnerName());
                sysEntitySaicPartnerPo.setPartnerType(partnerDataListDto.getPartnerType());
                sysEntitySaicPartnerPo.setTotalRealCapital(partnerDataListDto.getTotalRealCapital());
                sysEntitySaicPartnerPo.setTotalShouldCapital(partnerDataListDto.getTotalShouldCapital());
                sysEntitySaicPartnerPo.setPercent(partnerDataListDto.getPercent());
                partnerPoList.add(sysEntitySaicPartnerPo);
            }
        }
        sysEntityPo.setPartnerPoList(partnerPoList);
        //解析变更记录
        List<SysEntitySaicChangeRecordDto> changeRecordPoList = new ArrayList<>();
        if (changeRecordData.getTotal() > 0) {
            List<ChangeRecordDataListDto> list = changeRecordData.getList();
            for (ChangeRecordDataListDto changeRecordDataListDto : list) {
                SysEntitySaicChangeRecordDto sysEntitySaicChangeRecordPo = new SysEntitySaicChangeRecordDto();
                try {
                    sysEntitySaicChangeRecordPo.setChangeDate(LocalDate.parse(changeRecordDataListDto.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                } catch (Exception e) {
                    sysEntitySaicChangeRecordPo.setChangeDate(null);
                }
                sysEntitySaicChangeRecordPo.setChangeType(changeRecordDataListDto.getItem());
                sysEntitySaicChangeRecordPo.setChangeBefore(changeRecordDataListDto.getBefore());
                sysEntitySaicChangeRecordPo.setChangeAfter(changeRecordDataListDto.getAfter());
                changeRecordPoList.add(sysEntitySaicChangeRecordPo);
            }
        }
        sysEntityPo.setChangeRecordPoList(changeRecordPoList);
        return sysEntityPo;
    }
}
