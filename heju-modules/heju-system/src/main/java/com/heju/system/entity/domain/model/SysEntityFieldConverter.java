package com.heju.system.entity.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import com.heju.system.entity.domain.query.SysEntityFieldQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 实体字段管理 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEntityFieldConverter extends BaseConverter<SysEntityFieldQuery, SysEntityFieldDto, SysEntityFieldPo> {
}
