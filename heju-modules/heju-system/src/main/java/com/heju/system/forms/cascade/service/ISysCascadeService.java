package com.heju.system.forms.cascade.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;

/**
 * 级联管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysCascadeService extends IBaseService<SysCascadeQuery, SysCascadeDto> {

}