package com.heju.system.report.domain.po;

import com.heju.common.core.constant.system.ReportConstants;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysReportManagementPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体id */
    private Long entityId;

    /** 实体名称 */
    private String entityName;

    /** 年份 */
    protected String year;

    /** 月份 */
    protected String month;

    /** 季度 */
    protected String season;

    /** 银行报表类型 */
    private Map<String, Boolean> bankReportTypes;

    /** 发票类型 */
    private Map<String, Boolean> billReportsTypes;

    /** 财税报表类型 */
    private Map<String, Boolean> financeReportTypes;

    /** 税务申报类型 */
    private Map<String, Boolean> taxReportTypes;


    public SysReportManagementPo() {
        this.bankReportTypes = new HashMap<>();
        this.billReportsTypes = new HashMap<>();
        this.financeReportTypes = new HashMap<>();
        this.taxReportTypes = new HashMap<>();
        initBankTypes();
        initBillTypes();
        initFinanceTypes();
        initTaxTypes();
    }

    /**
     * 初始化银行报表类型集合
     */
    private void initBankTypes() {
        for (int i = 0; i <= Integer.parseInt(ReportConstants.BankType.NUMBER.getCode()) - 1; i++) {
            bankReportTypes.put(i + "", false);
        }
    }

    /**
     * 初始化发票类型集合
     */
    private void initBillTypes() {
        for (int i = 0; i <= Integer.parseInt(ReportConstants.BillType.NUMBER.getCode()) - 1; i++) {
            billReportsTypes.put(i + "", false);
        }
    }

    /**
     * 初始化财税报表类型集合
     */
    private void initFinanceTypes() {
        for (int i = 0; i <= Integer.parseInt(ReportConstants.FinanceType.NUMBER.getCode()) - 1; i++) {
            financeReportTypes.put(i + "", false);
        }
    }

    /**
     * 初始化税务申报类型集合
     */
    private void initTaxTypes() {
        for (int i = 0; i <= Integer.parseInt(ReportConstants.TaxType.NUMBER.getCode()) - 1; i++) {
            taxReportTypes.put(i + "", false);
        }
    }

    /**
     * 修改银行报表类型显示集合
     * @param bankReportDtoList 银行报表信息List
     */
    public void updateBankMap(List<SysBankReportDto> bankReportDtoList) {
        if (!Objects.isNull(bankReportDtoList)){
            for (SysBankReportDto dto : bankReportDtoList) {
                String reportType = dto.getReporttypeType();
                bankReportTypes.put(reportType, true);
            }
        }
    }

    /**
     * 修改发票类型显示集合
     * @param billReportDtoList 发票信息List
     */
    public void updateBillMap(List<SysBillReportDto> billReportDtoList) {
        if (!Objects.isNull(billReportDtoList)){
            for (SysBillReportDto dto : billReportDtoList) {
                String reportType = dto.getBillType();
                billReportsTypes.put(reportType, true);
            }
        }
    }

    /**
     * 修改财税报表类型显示集合
     * @param financeReportDtoList 财税报表信息List
     */
    public void updateFinanceMap(List<SysFinanceReportDto> financeReportDtoList) {
        if (!Objects.isNull(financeReportDtoList)){
            for (SysFinanceReportDto dto : financeReportDtoList) {
                String reportType = dto.getFinanceType();
                financeReportTypes.put(reportType, true);
            }
        }
    }

    /**
     * 修改税务申报类型显示集合
     * @param taxReportDtoList 税务申报信息List
     */
    public void updateTaxMap(List<SysTaxReportDto> taxReportDtoList) {
        if (!Objects.isNull(taxReportDtoList)){
            for (SysTaxReportDto dto : taxReportDtoList) {
                String reportType = dto.getTaxType();
                taxReportTypes.put(reportType, true);
            }
        }
    }
}
