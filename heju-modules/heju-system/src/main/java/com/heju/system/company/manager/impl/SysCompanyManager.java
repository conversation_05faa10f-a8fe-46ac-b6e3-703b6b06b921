package com.heju.system.company.manager.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.redis.service.RedisService;
import com.heju.common.web.entity.domain.SlaveRelation;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.query.SysDeptQuery;
import com.heju.system.api.organize.domain.query.SysPostQuery;
import com.heju.system.company.domain.dto.CompanyDeptPostDto;
import com.heju.system.company.domain.dto.CompanyDeptPostIdDto;
import com.heju.system.company.domain.merge.SysCompanyThirdMerge;
import com.heju.system.company.domain.po.SysCompanyPo;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyThirdAuthMerge;
import com.heju.system.company.domain.query.SysCompanyQuery;
import com.heju.system.company.domain.model.SysCompanyConverter;
import com.heju.system.company.mapper.SysCompanyMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.company.manager.ISysCompanyManager;
import com.heju.system.company.mapper.merge.SysCompanyThirdMergeMapper;
import com.heju.system.organize.domain.vo.SysUserCompany;
import com.heju.system.organize.manager.ISysDeptManager;
import com.heju.system.organize.manager.ISysPostManager;
import com.heju.system.organize.manager.ISysUserManager;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.dto.ThirdOrganizeDto;
import com.heju.system.third.domain.dto.ThirdOrganizeOneDto;
import com.heju.system.third.manager.impl.SysThirdManager;
import com.heju.system.third.mapper.SysThirdAuthMapper;
import com.heju.system.utils.OperateEnum;
import com.heju.system.utils.OrganizeEnum;
import com.heju.system.utils.ThirdOrganizeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.heju.system.api.organize.domain.merge.MergeGroup.Company_SysCompanyThirdMerge_GROUP;

/**
 * 子公司管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysCompanyManager extends BaseManagerImpl<SysCompanyQuery, SysCompanyDto, SysCompanyPo, SysCompanyMapper, SysCompanyConverter> implements ISysCompanyManager {

    @Autowired
    private ISysDeptManager deptManager;

    @Autowired
    private ISysPostManager postManager;

    @Autowired
    private ISysUserManager userManager;

    @Resource
    private SysThirdAuthMapper thirdAuthMapper;

    @Resource
    protected RedisService redisService;

    /**
     * 初始化从属关联关系
     *
     * @return 关系对象集合
     */
    protected List<SlaveRelation> subRelationInit() {
        return new ArrayList<>() {{
            add(new SlaveRelation(Company_SysCompanyThirdMerge_GROUP, SysThirdManager.class, SysCompanyThirdMergeMapper.class, SysCompanyThirdMerge.class));
        }};
    }


    @Override
    public SysCompanyDto checkCompanyCodeUnique(Long Id, String code) {
        SysCompanyPo company = baseMapper.selectOne(
                Wrappers.<SysCompanyPo>query().lambda()
                        .ne(SysCompanyPo::getId, Id)
                        .eq(SysCompanyPo::getCode, code)
                        .last(SqlConstants.LIMIT_ONE));
        return mapperDto(company);
    }

    /**
     * 根据公司信息组装公司-部门-岗位-用户list
     *
     * @param dto 公司信息
     * @return list
     */
    @Override
    @Async
    public void getDeptPostById(SysCompanyDto dto, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList) {
        CompanyDeptPostIdDto companyDeptPostDto = new CompanyDeptPostIdDto();
        BeanUtils.copyProperties(dto, companyDeptPostDto);
        SysDeptQuery deptQuery = new SysDeptQuery();
        deptQuery.setCompanyId(dto.getId());
        List<SysDeptDto> sysDeptList = deptManager.selectList(deptQuery);
        List<Long> deptIdList = sysDeptList.stream().map(SysDeptDto::getId).toList();
        SysPostQuery postQuery = new SysPostQuery();
        postQuery.setCompanyId(dto.getId());
        List<SysPostDto> sysPostList = postManager.selectList(postQuery);
        List<Long> postIdList = sysPostList.stream().map(SysPostDto::getId).toList();
        List<SysUserDto> listByDeptId = userManager.getListByDeptId(deptIdList);
        for (SysUserDto sysUserDto : listByDeptId) {
            List<SysPostDto> userPosts = sysUserDto.getPosts().stream().filter(sysPostDto -> sysPostDto.getCompanyId().equals(dto.getId())).toList();
            sysUserDto.setPosts(userPosts);
        }
        companyDeptPostDto.setDeptIdList(deptIdList);
        companyDeptPostDto.setPostIdList(postIdList);
        companyDeptPostDto.setUserDtoList(listByDeptId);
        List<CompanyDeptPostIdDto> companyDeptPostDtoIdList = new ArrayList<>();
        companyDeptPostDtoIdList.add(companyDeptPostDto);
        this.buildCompany(companyDeptPostDtoIdList, null, OperateEnum.DELETE.getCode(), companyThirdAuthMergeList);
    }

    /**
     * 批量信息同步第三方
     *
     * @param companyDeptPostDtoIdList  公司-部门-岗位-人员Id list
     * @param companyDeptPostDtoList    公司-部门-岗位-人员list
     * @param type                      1-新增；2-修改；3-删除
     * @param companyThirdAuthMergeList 第三方认证信息-公司关联list
     */
    @Override
    public void buildCompany(List<CompanyDeptPostIdDto> companyDeptPostDtoIdList, List<CompanyDeptPostDto> companyDeptPostDtoList, int type, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList) {
        List<ThirdOrganizeDto> organizeDtoList = new ArrayList<>();
        Map<String, SysThirdAuthDto> thirdAuthRedis = redisService.redisTemplate.opsForHash().entries(CacheConstants.CacheType.SYS_THIRD_AUTH_KEY.getCode());
        Map<Long, List<SysCompanyThirdAuthMerge>> companyThirdAuthMap = companyThirdAuthMergeList.stream().collect(Collectors.groupingBy(SysCompanyThirdAuthMerge::getThirdAuthId));
        for (Long thirdAuthId : companyThirdAuthMap.keySet()) {
            ThirdOrganizeDto thirdOrganizeDto = new ThirdOrganizeDto();
            SysThirdAuthDto thirdAuthDto = thirdAuthRedis.get(String.valueOf(thirdAuthId));
            BeanUtils.copyProperties(thirdAuthDto, thirdOrganizeDto);
            List<SysCompanyThirdAuthMerge> companyThirdAuthList = companyThirdAuthMap.get(thirdAuthId);
            List<Long> companyId = companyThirdAuthList.stream().map(SysCompanyThirdAuthMerge::getCompanyId).toList();
            if (type == OperateEnum.ADD.getCode()) {
                List<CompanyDeptPostDto> addCompanyList = companyDeptPostDtoList.stream().filter(companyDeptPostDto ->
                        companyId.contains(companyDeptPostDto.getId())).collect(Collectors.toList());
                thirdOrganizeDto.setAddCompanyList(addCompanyList);
            } else if (type == OperateEnum.DELETE.getCode()) {
                List<CompanyDeptPostIdDto> delCompanyList = companyDeptPostDtoIdList.stream().filter(companyDeptPostDto ->
                        companyId.contains(companyDeptPostDto.getId())).collect(Collectors.toList());
                thirdOrganizeDto.setDelCompanyList(delCompanyList);
            }
            organizeDtoList.add(thirdOrganizeDto);
        }
        ThirdOrganizeUtil.sendThirdOrganize(organizeDtoList);
    }

    @Override
    @Async
    public void buildDept(List<SysDeptDto> originList) {
        Map<Long, List<SysDeptDto>> deptCompanyMap = originList.stream().collect(Collectors.groupingBy(SysDeptDto::getCompanyId));
        List<SysCompanyDto> companyDtoList = this.selectListByIds(deptCompanyMap.keySet());
        List<CompanyDeptPostIdDto> companyDeptPostDtoIdList = new ArrayList<>();
        for (SysCompanyDto companyDto : companyDtoList) {
            CompanyDeptPostIdDto companyDeptPostIdDto = new CompanyDeptPostIdDto();
            BeanUtils.copyProperties(companyDto, companyDeptPostIdDto);
            List<Long> deptIdList = deptCompanyMap.get(companyDto.getId()).stream().map(SysDeptDto::getId).toList();
            companyDeptPostIdDto.setDeptIdList(deptIdList);
            companyDeptPostDtoIdList.add(companyDeptPostIdDto);
        }
        List<Long> companyIds = deptCompanyMap.keySet().stream().toList();
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthMapper.getByCompanyIds(companyIds);
        this.buildCompany(companyDeptPostDtoIdList, null, OperateEnum.DELETE.getCode(), companyThirdAuthMergeList);
    }

    @Override
    @Async
    public void buildPost(List<SysPostDto> originList) {
        Map<Long, List<SysPostDto>> deptCompanyMap = originList.stream().collect(Collectors.groupingBy(SysPostDto::getCompanyId));
        List<SysCompanyDto> companyDtoList = this.selectListByIds(deptCompanyMap.keySet());
        List<CompanyDeptPostIdDto> companyDeptPostDtoIdList = new ArrayList<>();
        for (SysCompanyDto companyDto : companyDtoList) {
            CompanyDeptPostIdDto companyDeptPostIdDto = new CompanyDeptPostIdDto();
            BeanUtils.copyProperties(companyDto, companyDeptPostIdDto);
            List<Long> postIdList = deptCompanyMap.get(companyDto.getId()).stream().map(SysPostDto::getId).toList();
            companyDeptPostIdDto.setDeptIdList(postIdList);
            companyDeptPostDtoIdList.add(companyDeptPostIdDto);
        }
        List<Long> companyIds = deptCompanyMap.keySet().stream().toList();
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthMapper.getByCompanyIds(companyIds);
        this.buildCompany(companyDeptPostDtoIdList, null, OperateEnum.DELETE.getCode(), companyThirdAuthMergeList);
    }

    @Override
    @Async
    public void buildUser(List<SysUserDto> originList) {
        List<SysUserCompany> companyById=new ArrayList<>();
        for (SysUserDto sysUserDto : originList) {
            for (SysPostDto post : sysUserDto.getPosts()) {
                SysUserCompany sysUserCompany=new SysUserCompany();
                sysUserCompany.setCompanyId(post.getCompanyId());
                sysUserCompany.setId(sysUserDto.getId());
                companyById.add(sysUserCompany);
            }
        }
        companyById = companyById.stream().distinct().toList();
        Map<Long, List<SysUserCompany>> userCompanyMap = companyById.stream().collect(Collectors.groupingBy(SysUserCompany::getCompanyId));
        List<SysCompanyPo> companyDtoList =baseMapper.selectBatchIds(userCompanyMap.keySet());
        List<CompanyDeptPostIdDto> companyDeptPostDtoIdList = new ArrayList<>();
        for (SysCompanyPo companyDto : companyDtoList) {
            CompanyDeptPostIdDto companyDeptPostIdDto = new CompanyDeptPostIdDto();
            BeanUtils.copyProperties(companyDto, companyDeptPostIdDto);
            List<Long> userIdList = userCompanyMap.get(companyDto.getId()).stream().map(SysUserCompany::getId).toList();
            companyDeptPostIdDto.setUserIdList(userIdList);
            companyDeptPostDtoIdList.add(companyDeptPostIdDto);
        }
        List<Long> companyIds = userCompanyMap.keySet().stream().toList();
        List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthMapper.getByCompanyIds(companyIds);
        this.buildCompany(companyDeptPostDtoIdList, null, OperateEnum.DELETE.getCode(), companyThirdAuthMergeList);
    }

    /**
     * 单条信息同步第三方
     *
     * @param operateType               1-新增；2-修改；3-删除
     * @param organizeType              1-公司；2-部门；3-岗位；4-用户
     * @param companyThirdAuthMergeList 第三方认证信息-公司关联list
     * @param organizeInfo              同步的数据
     */
    @Override
    @Async
    public void buildOrganizeOne(int operateType, int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList, String organizeInfo) {
        List<ThirdOrganizeOneDto> organizeDtoList = new ArrayList<>();
        Map<String, SysThirdAuthDto> thirdAuthRedis = redisService.redisTemplate.opsForHash().entries(CacheConstants.CacheType.SYS_THIRD_AUTH_KEY.getCode());
        for (SysCompanyThirdAuthMerge companyThirdAuthMerge : companyThirdAuthMergeList) {
            ThirdOrganizeOneDto thirdOrganizeOneDto = new ThirdOrganizeOneDto();
            SysThirdAuthDto thirdAuthDto = thirdAuthRedis.get(String.valueOf(companyThirdAuthMerge.getThirdAuthId()));
            BeanUtils.copyProperties(thirdAuthDto, thirdOrganizeOneDto);
            thirdOrganizeOneDto.setOrganizeType(organizeType);
            thirdOrganizeOneDto.setOperateType(operateType);
            thirdOrganizeOneDto.setOrganizeInfo(organizeInfo);
            organizeDtoList.add(thirdOrganizeOneDto);
        }
        ThirdOrganizeUtil.sendThirdOrganizeOne(organizeDtoList);
    }

    /**
     * 用户-单条信息同步第三方
     *
     * @param nowCompanyIds 修改前后公司id交集
     * @param addCompany    需新增公司id
     * @param delCompany    需删除公司id
     * @param originDto     修改前用户信息
     * @param dto           修改后用户信息
     */
    @Override
    @Async
    public void buildOrganizeUserOne(List<Long> nowCompanyIds, List<Long> addCompany, List<Long> delCompany, SysUserDto originDto, SysUserDto dto) {
        List<ThirdOrganizeOneDto> organizeDtoList = new ArrayList<>();
        if (!nowCompanyIds.isEmpty()) {
            List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthMapper.getByCompanyIds(nowCompanyIds);
            organizeDtoList.addAll(this.buildUserList(OperateEnum.EDIT.getCode(), OrganizeEnum.USER.getCode(), companyThirdAuthMergeList, dto));
        }
        if (!addCompany.isEmpty()) {
            List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthMapper.getByCompanyIds(addCompany);
            organizeDtoList.addAll(this.buildUserList(OperateEnum.ADD.getCode(), OrganizeEnum.USER.getCode(), companyThirdAuthMergeList, dto));
        }
        if (!delCompany.isEmpty()) {
            List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList = thirdAuthMapper.getByCompanyIds(delCompany);
            dto.setPosts(originDto.getPosts());
            organizeDtoList.addAll(this.buildUserList(OperateEnum.DELETE.getCode(), OrganizeEnum.USER.getCode(), companyThirdAuthMergeList, dto));
        }
        ThirdOrganizeUtil.sendThirdOrganizeOne(organizeDtoList);
    }

    public List<ThirdOrganizeOneDto> buildUserList(int operateType, int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList, SysUserDto dto) {
        List<ThirdOrganizeOneDto> organizeDtoList = new ArrayList<>();
        Map<String, SysThirdAuthDto> thirdAuthRedis = redisService.redisTemplate.opsForHash().entries(CacheConstants.CacheType.SYS_THIRD_AUTH_KEY.getCode());
        Map<Long, List<SysCompanyThirdAuthMerge>> companyThirdAuthMap = companyThirdAuthMergeList.stream().collect(Collectors.groupingBy(SysCompanyThirdAuthMerge::getThirdAuthId));
        for (Long thirdAuthId : companyThirdAuthMap.keySet()) {
            ThirdOrganizeOneDto thirdOrganizeOneDto = new ThirdOrganizeOneDto();
            SysThirdAuthDto thirdAuthDto = thirdAuthRedis.get(String.valueOf(thirdAuthId));
            BeanUtils.copyProperties(thirdAuthDto, thirdOrganizeOneDto);
            thirdOrganizeOneDto.setOrganizeType(organizeType);
            thirdOrganizeOneDto.setOperateType(operateType);
            List<Long> companyIdList = companyThirdAuthMap.get(thirdAuthId).stream().map(SysCompanyThirdAuthMerge::getCompanyId).toList();
            List<SysPostDto> collect = dto.getPosts().stream().filter(sysPostDto -> companyIdList.contains(sysPostDto.getCompanyId())).toList();
            SysUserDto thisDto = new SysUserDto();
            BeanUtils.copyProperties(dto, thisDto);
            thisDto.setPosts(collect);
            thirdOrganizeOneDto.setOrganizeInfo(JSON.toJSONString(thisDto));
            organizeDtoList.add(thirdOrganizeOneDto);
        }
        return organizeDtoList;
    }

    @Override
    @Async
    public void buildOrganizeUserOne(int operateType, int organizeType, List<SysCompanyThirdAuthMerge> companyThirdAuthMergeList, SysUserDto dto) {
        List<ThirdOrganizeOneDto> organizeDtoList = buildUserList(operateType, organizeType, companyThirdAuthMergeList, dto);
        ThirdOrganizeUtil.sendThirdOrganizeOne(organizeDtoList);
    }
}