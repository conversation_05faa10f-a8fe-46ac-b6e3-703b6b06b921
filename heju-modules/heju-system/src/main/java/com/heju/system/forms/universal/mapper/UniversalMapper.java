package com.heju.system.forms.universal.mapper;

import com.alibaba.fastjson.JSONObject;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.entity.domain.dto.CustomerDto;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 级联管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface UniversalMapper {


    @Select("<script>" +
            " ${selectSql} " +
            "where del_flag=0 and status=0" +
            " limit #{query.page},#{query.pageSize} " +
            "</script>")
    List<Map<String,Object>> selectList(@Param("selectSql") String selectSql, @Param("query") UniversalQuery query);


    @Select("select count(*) from ${sheetApiName}")
    List<Map<String,Object>> countList(String sheetApiName);

    @Select("${selectSql} and del_flag=0 ")
    Map<String,Object> selectInfo(@Param("selectSql") String selectSql);


    @Select("${selectSql} and del_flag=0 ")
    List<Map<String,Object>> selectRelationList(@Param("selectSql") String selectSql);

    @Insert("${insertSql}")
    int insert(@Param("insertSql") String insertSql);

    @Update("<script>" +
            "update ${sheetApiName} set " +
            "<foreach collection='jsonObject' open='' separator=',' item='jsonValue' index='jsonKey'>" +
            " ${jsonKey} = #{jsonValue}" +
            "</foreach>" +
            " where id=#{id}" +
            "</script>")
    int update(@Param("jsonObject") JSONObject jsonObject,@Param("id") Long id,@Param("sheetApiName") String sheetApiName);

    @Update("<script>" +
            "UPDATE ${sheetApiName} SET del_flag = 1, status = 1 " +
            " WHERE `id` IN " +
            "<foreach collection='idList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
            " AND del_flag = 0 " +
            "</script>")
    int delete(@Param("sheetApiName") String sheetApiName,@Param("idList")List<Long> idList);

    @Select("select ${selectSql} from ${sheetApiName} where del_flag=0")
    List<Map<String,Object>> option(@Param("sheetApiName") String sheetApiName, @Param("selectSql") String selectSql);

    @Insert("insert into bus_customer (" +
            "`code`, `name` ,`legal_person_name` ,`credit_no` ,`register_capital` ,`register_capital_currency` ,`reg_type`, `business_scope` ,`address`,`business_status` ,`business_term` ,`belong_org`,`register_no`,`start_date` ,`create_by`" +
            ") values (" +
            "#{code},#{name},#{legalPersonName},#{creditNo},#{registerCapital},#{registerCapitalCurrency},#{regType},#{businessScope},#{address},#{status},#{businessTerm},#{belongOrg},#{registerNo},#{startDate},#{createBy}" +
            ")")
    int insertCustomer(CustomerDto customerDto);

    @Select("select * from ${sheetApiName} where `id`=#{id}")
    Map<String,Object> selectById(@Param("id") Long id,@Param("sheetApiName") String sheetApiName);

    @Insert("insert into bus_customer_taxation (" +
            "`id`) values (#{id})")
    int insertCustomerTaxation(Long id);

    @Insert({
            "<script>",
            "INSERT INTO ${sheetApiName} ",
            "( <foreach collection='list' item='item' index='index' separator=','>",
            "#{item.key}",
            "</foreach> )",
            "VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "<foreach collection='item.values()' item='value' separator=','>",
            "#{value}",
            "</foreach>",
            "</foreach>",
            "</script>"
    })
    void insertBatch(@Param("list") List<Map<String,Object>> list,@Param("sheetApiName") String sheetApiName);

    @Select("select ${quoteFieldApiName} from ${quoteSheetApiNam} where ${quoteRuleFieldApiName} = #{fieldValue}")
    Object selectQuoteFieldVale(@Param("quoteSheetApiNam") String quoteSheetApiNam,@Param("quoteFieldApiName") String quoteFieldApiName,
                              @Param("quoteRuleFieldApiName") String quoteRuleFieldApiName,@Param("fieldValue") Object fieldValue);

    @Update("update ${updateSheetApiName} set ${updateFieldApiName} = #{updateValue} where ${quoteRuleFieldApiName} = #{quoteRuleFieldValue}")
    void updateQuoteField(@Param("updateFieldApiName") String updateFieldApiName,@Param("updateSheetApiName") String updateSheetApiName,
                          @Param("updateValue") Object updateValue,@Param("quoteRuleFieldApiName") String quoteRuleFieldApiName,@Param("quoteRuleFieldValue") Object quoteRuleFieldValue);
    // name, sys_base_entity, 腾讯科技, credit_no, 91430702MA7AGP2X75
    // update sys_base_entity set name = 腾讯科技 where credit_no = 91430702MA7AGP2X75

    @Select("select * from ${quoteSheetApiNam} where ${quoteFieldApiName} = #{fieldValue}")
    Map<String, Object> selectByQuote(@Param("quoteSheetApiNam") String quoteSheetApiNam,@Param("quoteFieldApiName") String quoteFieldApiName,@Param("fieldValue") Object fieldValue);
    // sys_base_entity, name, 腾讯科技有限公司
    // select * from sys_base_entity where name = 腾讯科技有限公司
}
