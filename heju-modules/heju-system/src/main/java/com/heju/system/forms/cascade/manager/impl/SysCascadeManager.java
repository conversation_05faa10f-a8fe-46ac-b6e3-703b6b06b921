package com.heju.system.forms.cascade.manager.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.merge.SysCascadeRelationMerge;
import com.heju.system.forms.cascade.domain.model.SysCascadeConverter;
import com.heju.system.forms.cascade.domain.po.CascadeTree;
import com.heju.system.forms.cascade.domain.po.SysCascadePo;
import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.cascade.mapper.SysCascadeMapper;
import com.heju.system.forms.cascade.mapper.merge.SysCascadeRelationMapper;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 级联管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysCascadeManager extends BaseManagerImpl<SysCascadeQuery, SysCascadeDto, SysCascadePo, SysCascadeMapper, SysCascadeConverter> implements ISysCascadeManager {

    @Resource
    private SysCascadeRelationMapper relationMapper;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    @Override
    public void insertCascadeRelation(List<CascadeTree> cascadeTreeList, Long cascadeId) {
        List<SysCascadeRelationMerge> cascadeRelationMerges=new ArrayList<>();
        buildData(cascadeTreeList, cascadeRelationMerges, cascadeId, 1);
        relationMapper.insertBatch(cascadeRelationMerges);
    }

    @Override
    public void deleteCascadeRelation(Long cascadeId) {
        LambdaQueryWrapper<SysCascadeRelationMerge> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCascadeRelationMerge::getCascadeId,cascadeId);
        relationMapper.delete(wrapper);
    }

    @Override
    public  List<Tree<String>> selectCascadeRelation(Long cascadeId) {
        LambdaQueryWrapper<SysCascadeRelationMerge> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCascadeRelationMerge::getCascadeId,cascadeId);
        List<SysCascadeRelationMerge> cascadeRelationMerges = relationMapper.selectList(wrapper);
        List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(new LambdaQueryWrapper<>());
        Map<Long, String> collect = optionValueList.stream().collect(
                Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        List<TreeNode<String>> nodeList = new ArrayList<>();
        for (SysCascadeRelationMerge cascadeRelationMerge : cascadeRelationMerges) {
            Long mainOptionValueId = cascadeRelationMerge.getMainOptionValueId();
            Long cascadeOptionValueId = cascadeRelationMerge.getCascadeOptionValueId();
            if(cascadeRelationMerge.getLevel()==1){
                nodeList.add(new TreeNode<>(String.valueOf(mainOptionValueId), "0", collect.get(mainOptionValueId), 1));
            }
            nodeList.add(new TreeNode<>(String.valueOf(cascadeOptionValueId) ,String.valueOf(mainOptionValueId), collect.get(cascadeOptionValueId), 1));
        }
        return TreeUtil.build(nodeList, "0");
    }

    public void buildData(List<CascadeTree> cascadeTreeList,List<SysCascadeRelationMerge> cascadeRelationMerges,Long cascadeId,int level){
        for (CascadeTree sysCascadeTree : cascadeTreeList) {
            List<CascadeTree> children = sysCascadeTree.getChildren();
            if(children!=null&&!children.isEmpty()){
                long lId = Long.parseLong(sysCascadeTree.getId());
                int reLevel=level+1;
                for (CascadeTree child : children) {
                    SysCascadeRelationMerge cascadeRelationMerge=new SysCascadeRelationMerge();
                    cascadeRelationMerge.setCascadeId(cascadeId);
                    cascadeRelationMerge.setMainOptionValueId(lId);
                    cascadeRelationMerge.setLevel(level);
                    cascadeRelationMerge.setCascadeOptionValueId(Long.parseLong(child.getId()));
                    cascadeRelationMerges.add(cascadeRelationMerge);
                    buildData(children,cascadeRelationMerges,cascadeId,reLevel);
                }
            }
        }
    }


}