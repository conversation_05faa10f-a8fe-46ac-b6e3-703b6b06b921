package com.heju.system.entity.service.impl;

import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.query.SysEntitySaicEmployeeQuery;
import com.heju.system.entity.service.ISysEntitySaicEmployeeService;
import com.heju.system.entity.manager.ISysEntitySaicEmployeeManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体工商董事会成员管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntitySaicEmployeeServiceImpl extends BaseServiceImpl<SysEntitySaicEmployeeQuery, SysEntitySaicEmployeeDto, ISysEntitySaicEmployeeManager> implements ISysEntitySaicEmployeeService {

    /**
     * 查询实体工商董事会成员对象列表 | 数据权限
     *
     * @param entitySaicEmployee 实体工商董事会成员对象
     * @return 实体工商董事会成员对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntitySaicEmployeeMapper"})
    public List<SysEntitySaicEmployeeDto> selectListScope(SysEntitySaicEmployeeQuery entitySaicEmployee) {
        return baseManager.selectList(entitySaicEmployee);
    }

}