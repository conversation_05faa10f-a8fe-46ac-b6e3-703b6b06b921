package com.heju.system.entity.controller;

import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.poi.ExcelUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.redis.service.RedisService;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.system.entity.domain.po.SysEntity;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityQuery;
import com.heju.system.entity.service.ISysEntityService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;
import java.util.UUID;

/**
 * 实体信息管理管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/entity")
public class SysEntityController {

    @Resource
    private ISysEntityService entityService;

    @Resource
    private RedisService redisService;

    /**
     * 查询实体信息管理列表
     */
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_ENTITY_LIST)
    public AjaxResult list(SysEntityQuery entityQuery) {
        return entityService.list(entityQuery);
    }

    /**
     * 查询实体信息管理详细
     */
    @GetMapping(value = "/getInfo")
    @RequiresPermissions(Auth.SYS_ENTITY_SINGLE)
    public AjaxResult getInfo(SysEntityQuery entityQuery) {
        return entityService.getInfo(entityQuery);
    }

    /**
     * 实体信息管理新增
     */
    @PostMapping
    @Log(title = "实体信息管理管理", businessType = BusinessType.INSERT)
    @RequiresPermissions(Auth.SYS_ENTITY_ADD)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysEntityQuery entityQuery) throws ParseException {
        return entityService.add(entityQuery);
    }

    /**
     * 实体信息管理修改
     */
    @PutMapping
    @Log(title = "实体信息管理管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions(Auth.SYS_ENTITY__EDIT)
    public AjaxResult edit(@RequestBody String entityJson) {
        return entityService.edit(entityJson);
    }
//
//    /**
//     * 实体信息管理修改状态
//     */
//    @PutMapping("/status")
//    @RequiresPermissions(value = {Auth.SYS_ENTITY_FIELD_EDIT, Auth.SYS_ENTITY_FIELD_ES}, logical = Logical.OR)
//    @Log(title = "实体信息管理管理", businessType = BusinessType.UPDATE_STATUS)
//    public AjaxResult editStatus(@RequestBody SysEntityFieldDto entityField) {
//        return super.editStatus(entityField);
//    }
//

    /**
     * 实体信息管理批量删除
     */
    @DeleteMapping("/batch/{idList}")
    @Log(title = "实体信息管理管理", businessType = BusinessType.DELETE)
    @RequiresPermissions(Auth.SYS_ENTITY_ES)
    public AjaxResult remove(@PathVariable List<Long> idList) {
        return entityService.batchRemove(idList);
    }
//

    /**
     * 获取实体信息管理选择框列表
     */
    @GetMapping("/option")
    public AjaxResult option() {
        return entityService.option();
    }

    /**
     * 导入excel
     *
     * @param file 文件流
     * @return 成功失败
     */
    @PostMapping("/importData")
    @ResponseBody
    @Log(title = "实体信息管理管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Auth.SYS_ENTITY_IMPORT)
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<SysEntity> util = new ExcelUtil<>(SysEntity.class);
        List<SysEntity> userList = util.importExcel(file.getInputStream());
        String UUID = usingUUID();
        redisService.setCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), UUID, NumberUtil.Zero);
        entityService.importData(userList, UUID);
        return AjaxResult.success(UUID);
    }

    /**
     * @param redisUUID 导入excel 结果标识
     * @return 成功/失败
     */
    @GetMapping("/getImportResult/{redisUUID}")
    @ResponseBody
    public AjaxResult getImportResult(@PathVariable String redisUUID) {
        Object cacheMapValue = redisService.getCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), redisUUID);
        if (!cacheMapValue.toString().equals("0")) {
            redisService.deleteCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), redisUUID);
        }
        return AjaxResult.success(cacheMapValue.toString());
    }

    /**
     * 下载模板
     *
     * @param response http请求response
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    @Log(title = "实体信息管理管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions(Auth.SYS_ENTITY_TEMPLATE)
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysEntity> util = new ExcelUtil<>(SysEntity.class);
        util.importTemplateExcel(response, "实体信息数据");
    }

    public static String usingUUID() {
        UUID randomUUID = UUID.randomUUID();
        return randomUUID.toString().replaceAll("-", "");
    }

    /**
     * 获取实体下拉框列表
     */
    @GetMapping("/drop")
    public SysEntityPo[] drop() {
        return entityService.drop();
    }

}
