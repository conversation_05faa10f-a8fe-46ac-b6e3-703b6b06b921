package com.heju.system.company.mapper;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.company.domain.query.SysCompanyQuery;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 子公司管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysCompanyMapper extends BaseMapper<SysCompanyQuery, SysCompanyDto, SysCompanyPo> {

    @Select("select * from sys_company where tenant_id")
    SysCompanyDto[] selectCompany(String tenantId);

    @Select("select * from sys_company")
    List<SysCompanyDto> findAllCompanyIdAndName();
}