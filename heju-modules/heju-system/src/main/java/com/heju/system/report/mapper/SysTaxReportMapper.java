package com.heju.system.report.mapper;

import com.heju.system.report.domain.query.SysReportManagementQuery;
import com.heju.system.report.domain.query.SysTaxReportQuery;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.po.SysTaxReportPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 税务申报管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysTaxReportMapper extends BaseMapper<SysTaxReportQuery, SysTaxReportDto, SysTaxReportPo> {
    @Select("select * from sys_tax_report where del_flag = '0'" +
            "and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)")
    List<SysTaxReportDto> findAll(SysReportManagementQuery query);
}