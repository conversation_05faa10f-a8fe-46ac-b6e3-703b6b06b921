package com.heju.system.phoneinfo.domain.model;
import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.po.SysTelephoneCodePo;
import com.heju.system.phoneinfo.domain.query.SysTelephoneCodeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 转发短信 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysTelephoneCodeConverter extends BaseConverter<SysTelephoneCodeQuery, SysTelephoneCodeDto, SysTelephoneCodePo> {
}
