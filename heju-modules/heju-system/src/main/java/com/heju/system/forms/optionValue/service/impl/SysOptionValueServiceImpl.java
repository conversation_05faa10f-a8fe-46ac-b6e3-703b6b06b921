package com.heju.system.forms.optionValue.service.impl;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.query.SysOptionValueQuery;
import com.heju.system.forms.optionValue.service.ISysOptionValueService;
import com.heju.system.forms.optionValue.manager.ISysOptionValueManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 选项值管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysOptionValueServiceImpl extends BaseServiceImpl<SysOptionValueQuery, SysOptionValueDto, ISysOptionValueManager> implements ISysOptionValueService {

    /**
     * 查询选项值对象列表 | 数据权限
     *
     * @param optionValue 选项值对象
     * @return 选项值对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysOptionValueMapper"})
    public List<SysOptionValueDto> selectListScope(SysOptionValueQuery optionValue) {
        return baseManager.selectList(optionValue);
    }

    @Override
    public boolean checkConfigCodeUnique(Long Id, String name) {
        return ObjectUtil.isNotNull(baseManager.checkConfigCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, name));
    }

    @Override
    public List<SysOptionValueDto> getValueListByOptionId(SysOptionValueQuery optionValue) {
        List<SysOptionValueDto> sysOptionValueDtos = baseManager.selectList(optionValue);
        return sysOptionValueDtos;
    }
}
