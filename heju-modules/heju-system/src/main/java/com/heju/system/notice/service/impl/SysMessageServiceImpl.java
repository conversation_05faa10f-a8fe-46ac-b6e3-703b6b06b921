package com.heju.system.notice.service.impl;

import com.heju.common.core.constant.basic.MessageConstants;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.domain.query.SysMessageQuery;
import com.heju.system.notice.mapper.SysMessageMapper;
import com.heju.system.notice.service.ISysMessageEntityChangeService;
import com.heju.system.notice.service.ISysMessageService;
import com.heju.system.notice.manager.ISysMessageManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.organize.service.ISysUserService;
import com.heju.system.utils.MessageUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息通知管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysMessageServiceImpl extends BaseServiceImpl<SysMessageQuery, SysMessageDto, ISysMessageManager> implements ISysMessageService {

    @Resource
    private ISysUserService userService;

    @Resource
    private ISysMessageEntityChangeService messageEntityChangeService;

    @Resource
    private SysMessageMapper messageMapper;

    /**
     * 查询消息通知对象列表 | 数据权限
     *
     * @param message 消息通知对象
     * @return 消息通知对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysMessageMapper"})
    public List<SysMessageDto> selectListScope(SysMessageQuery message) {
        Long userId = SecurityContextHolder.getUserId();
        String receiveUserName = "";
        if (userId != null){
            message.setReceiveUserId(userId);
            SysUserDto sysUserDto = userService.selectById(userId);
            receiveUserName = sysUserDto.getNickName();
            if (sysUserDto.getReception().equals(MessageConstants.Reception.NO.getCode())){
                return new ArrayList<>();
            }
        }
        List<SysMessageDto> sysMessageDtos = baseManager.selectList(message);
        for (SysMessageDto sysMessageDto : sysMessageDtos) {
            sysMessageDto.setSendUserName(userService.selectById(sysMessageDto.getReceiveUserId()).getNickName());
            sysMessageDto.setReceiveUserName(receiveUserName);
        }
        return sysMessageDtos;
    }

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysMessageDto selectById(Serializable id) {
        SysMessageDto dto = baseManager.selectById(id);
        if (dto.getStatus().equals(MessageConstants.Status.UNREAD.getCode())){
            dto.setStatus(MessageConstants.Status.READ.getCode());
            baseManager.updateStatus(dto);
        }
        SysMessageEntityChangeDto messageEntityChangeDto = messageEntityChangeService.selectById(dto.getChangeMessageId());
        SysMessageDto messageView = MessageUtil.packView(dto, messageEntityChangeDto);
        return subCorrelates(messageView);
    }

    @Override
    public int getMessageCount() {
        Long userId = SecurityContextHolder.getUserId();
        SysUserDto sysUserDto = userService.selectById(userId);
        if (sysUserDto.getReception().equals(MessageConstants.Reception.NO.getCode())){
            return 0;
        }
        return messageMapper.getMessageCount(userId,MessageConstants.Status.UNREAD.getCode());
    }
}