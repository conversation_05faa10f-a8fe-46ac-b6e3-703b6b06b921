package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体工商股东 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntitySaicPartnerDto extends SysEntitySaicPartnerPo {

    @Serial
    private static final long serialVersionUID = 1L;

}