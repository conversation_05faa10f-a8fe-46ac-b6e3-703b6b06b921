package com.heju.system.notice.manager.impl;

import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.domain.model.SysMessageEntityChangeConverter;
import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import com.heju.system.notice.domain.query.SysMessageEntityChangeQuery;
import com.heju.system.notice.manager.ISysMessageEntityChangeManager;
import com.heju.system.notice.mapper.SysMessageEntityChangeMapper;
import org.springframework.stereotype.Component;

/**
 * 消息实体变更通知管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysMessageEntityChangeManagerImpl extends BaseManagerImpl<SysMessageEntityChangeQuery, SysMessageEntityChangeDto, SysMessageEntityChangePo, SysMessageEntityChangeMapper, SysMessageEntityChangeConverter> implements ISysMessageEntityChangeManager {
}
