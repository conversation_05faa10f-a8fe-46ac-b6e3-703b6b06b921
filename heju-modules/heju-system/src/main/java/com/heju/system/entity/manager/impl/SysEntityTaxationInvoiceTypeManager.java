package com.heju.system.entity.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.po.SysEntityTaxationInvoiceTypePo;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import com.heju.system.entity.domain.query.SysEntityTaxationInvoiceTypeQuery;
import com.heju.system.entity.domain.model.SysEntityTaxationInvoiceTypeConverter;
import com.heju.system.entity.mapper.SysEntityTaxationInvoiceTypeMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.entity.manager.ISysEntityTaxationInvoiceTypeManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体税务票种认定管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysEntityTaxationInvoiceTypeManager extends BaseManagerImpl<SysEntityTaxationInvoiceTypeQuery, SysEntityTaxationInvoiceTypeDto, SysEntityTaxationInvoiceTypePo, SysEntityTaxationInvoiceTypeMapper, SysEntityTaxationInvoiceTypeConverter> implements ISysEntityTaxationInvoiceTypeManager {
    @Override
    public List<SysEntityTaxationInvoiceTypePo> getByEntityId(Long id) {
        LambdaQueryWrapper<SysEntityTaxationInvoiceTypePo> lambdaInvoice = new LambdaQueryWrapper<>();
        lambdaInvoice.eq(SysEntityTaxationInvoiceTypePo::getEntityId,id);
        return baseMapper.selectList(lambdaInvoice);
    }

    @Override
    public int updateByExamine(SysEntityExamineDto entityExamine) {
        UpdateWrapper<SysEntityTaxationInvoiceTypePo> wrapper=new UpdateWrapper<>();
        wrapper.eq("id",entityExamine.getListId()).set(entityExamine.getFieldName(),entityExamine.getAfterText());
        return baseMapper.update(null,wrapper);
    }
}