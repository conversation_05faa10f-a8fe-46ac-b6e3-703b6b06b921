package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import com.heju.system.entity.domain.query.SysEntitySaicPartnerQuery;

import java.util.List;

/**
 * 实体工商股东管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntitySaicPartnerManager extends IBaseManager<SysEntitySaicPartnerQuery, SysEntitySaicPartnerDto> {

    List<SysEntitySaicPartnerPo> getByEntityId(Long id);
}