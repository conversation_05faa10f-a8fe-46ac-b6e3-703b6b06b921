package com.heju.system.declaration.manager.impl;

import com.heju.system.declaration.domain.po.SysBusinessAnnualReportPo;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.query.SysBusinessAnnualReportQuery;
import com.heju.system.declaration.domain.model.SysBusinessAnnualReportConverter;
import com.heju.system.declaration.mapper.SysBusinessAnnualReportMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.declaration.manager.ISysBusinessAnnualReportManager;
import org.springframework.stereotype.Component;

/**
 * 工商年报管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysBusinessAnnualReportManager extends BaseManagerImpl<SysBusinessAnnualReportQuery, SysBusinessAnnualReportDto, SysBusinessAnnualReportPo, SysBusinessAnnualReportMapper, SysBusinessAnnualReportConverter> implements ISysBusinessAnnualReportManager {
}