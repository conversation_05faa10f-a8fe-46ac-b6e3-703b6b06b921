package com.heju.system.company.domain.merge;

import com.baomidou.mybatisplus.annotation.*;
import com.heju.common.core.annotation.Correlation;
import com.heju.common.core.annotation.Correlations;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.web.tenant.base.TBasisEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

import static com.heju.system.api.organize.domain.merge.MergeGroup.Company_SysCompanyThirdMerge_GROUP;
import static com.heju.system.api.organize.domain.merge.MergeGroup.Third_SysCompanyThirdMerge_GROUP;


/**
 * 公司第三方关联
 持久化对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_company_third_merge")
public class SysCompanyThirdMerge extends TBasisEntity {

    @Serial
    private static final long serialVersionUID = 1L;


    @Correlations({
            @Correlation(groupName = Company_SysCompanyThirdMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_MAIN),
            @Correlation(groupName = Third_SysCompanyThirdMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_SLAVE)
    })
    private Long companyId;

    @Correlations({
            @Correlation(groupName = Company_SysCompanyThirdMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_SLAVE),
            @Correlation(groupName = Third_SysCompanyThirdMerge_GROUP, keyType = OperateConstants.SubKeyType.MERGE_MAIN)
    })
    private Long thirdId;

    public SysCompanyThirdMerge(Long companyId, Long thirdId) {
        this.companyId = companyId;
        this.thirdId = thirdId;
    }
}