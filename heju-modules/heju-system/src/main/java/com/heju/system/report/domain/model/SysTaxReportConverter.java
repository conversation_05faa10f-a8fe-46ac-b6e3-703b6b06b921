package com.heju.system.report.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.po.SysTaxReportPo;
import com.heju.system.report.domain.query.SysTaxReportQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 税务申报 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysTaxReportConverter extends BaseConverter<SysTaxReportQuery, SysTaxReportDto, SysTaxReportPo> {
}
