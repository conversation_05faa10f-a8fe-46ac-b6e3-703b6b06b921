package com.heju.system.forms.cascade.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;
import com.heju.system.forms.cascade.service.ISysCascadeService;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.service.ISysFieldService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 级联管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cascade")
public class SysCascadeController extends BaseController<SysCascadeQuery, SysCascadeDto, ISysCascadeService> {

    @Resource
    private ISysFieldService fieldService;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "级联" ;
    }

    /**
     * 查询级联列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_CASCADE_LIST)
    public AjaxResult list(SysCascadeQuery cascade) {
        return super.list(cascade);
    }

    /**
     * 查询级联详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_CASCADE_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 级联新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_CASCADE_ADD)
    @Log(title = "级联管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysCascadeDto cascade) {
        return super.add(cascade);
    }

    /**
     * 级联修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_CASCADE_EDIT)
    @Log(title = "级联管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysCascadeDto cascade) {
        return super.edit(cascade);
    }

    /**
     * 级联修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_CASCADE_EDIT, Auth.SYS_CASCADE_ES}, logical = Logical.OR)
    @Log(title = "级联管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysCascadeDto cascade) {
        return super.editStatus(cascade);
    }

    /**
     * 级联批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_CASCADE_DEL)
    @Log(title = "级联管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取级联选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 查看引用
     */
    @GetMapping("/searchQuote")
    @RequiresPermissions(Auth.SYS_CASCADE_QUOTE)
    public AjaxResult searchQuote(SysCascadeQuery cascade) {
        SysFieldQuery query=new SysFieldQuery();
        query.setCascadeId(cascade.getId());
        return fieldService.searchQuote(query);
    }

}
