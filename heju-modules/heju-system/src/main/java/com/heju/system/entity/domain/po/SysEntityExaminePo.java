package com.heju.system.entity.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 实体信息审核 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_entity_examine", excludeProperty = { NAME , DEL_FLAG})
public class SysEntityExaminePo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 字段中文名 */
    @Excel(name = "字段中文名")
    protected String fieldComment;

    /** 字段编码 */
    @Excel(name = "字段编码")
    protected String fieldName;

    /** 字段所属模块 */
    @Excel(name = "字段所属模块")
    protected String fieldBelong;

    /** 字段原内容 */
    @Excel(name = "字段原内容")
    protected String beforeText;

    /** 字段现内容 */
    @Excel(name = "字段现内容")
    protected String afterText;

    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

    /** 实体名称 */
    @Excel(name = "实体名称")
    protected String entityName;

    /** 所属列表id */
    @Excel(name = "所属列表id")
    protected Long listId;

    /** 审核状态(0-待审核 1-通过 2-驳回) */
    @Excel(name = "审核状态(0-待审核 1-通过 2-驳回)")
    protected Integer examineStatus;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}