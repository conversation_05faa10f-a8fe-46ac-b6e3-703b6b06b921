package com.heju.system.declaration.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 工商年报 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_business_annual_report", excludeProperty = { NAME })
public class SysBusinessAnnualReportPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体id */
    @Excel(name = "实体id")
    protected Long entityId;

    /** 报表编码 */
    @Excel(name = "报表编码")
    protected String code;

    /** 工商年报类型(0工商年报) */
    @Excel(name = "工商年报类型(0工商年报)")
    protected String reporttypeType;

    /** 年份 */
    @Excel(name = "年份")
    protected String year;

    /** 报表地址 */
    @Excel(name = "报表地址")
    protected String reportAddress;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

    /** 申报状态(0已申报 1申报失败 2审批通过 3审批驳回) */
    @Excel(name = "申报状态(0已申报 1申报失败 2审批通过 3审批驳回)")
    protected String declareStatus;

    /** 无法申报原因 */
    @Excel(name = "无法申报原因")
    protected String declareRemark;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    protected String reason;

}