package com.heju.system.entity.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.entity.domain.dto.CompanyAbnormalInfoListDto;
import com.heju.system.entity.domain.dto.CompanyAbnormalInformationDto;
import com.heju.system.entity.domain.dto.CompanyBaseInfoApiDto;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.CompanyAbnormalInformationPo;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityExceptionInfoQuery;
import com.heju.system.entity.manager.ISysEntityExceptionInfoManager;
import com.heju.system.entity.mapper.SysEntityExceptionInfoBMapper;
import com.heju.system.entity.mapper.SysEntityExceptionInfoHistoryRecordsMapper;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.entity.service.ISysEntityExceptionInfoService;
import com.heju.system.utils.CompanyBaseInfoUtil;
import com.heju.system.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 企业经营异常信息管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityExceptionInfoServiceImpl extends BaseServiceImpl<SysEntityExceptionInfoQuery, SysEntityExceptionInfoDto, ISysEntityExceptionInfoManager> implements ISysEntityExceptionInfoService {

    @Resource
    private SysEntityExceptionInfoBMapper sysEntityExceptionInfoBMapper;

    @Resource
    private SysEntityExceptionInfoHistoryRecordsMapper sysEntityExceptionInfoHistoryRecordsMapper;

    @Resource
    private SysEntityMapper sysEntityMapper;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    /**
     * 查询企业经营异常信息对象列表 | 数据权限
     *
     * @param entityExceptionInfo 企业经营异常信息对象
     * @return 企业经营异常信息对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntityExceptionInfoMapper"})
    public List<SysEntityExceptionInfoDto> selectListScope(SysEntityExceptionInfoQuery entityExceptionInfo) {
        return baseManager.selectList(entityExceptionInfo);
    }


    @Override
    @DSTransactional
    public void entityAbnormalInfo(SysEntityPo sysEntityPo) throws ParseException {
        String companyName1 = sysEntityPo.getName();
        CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(companyName1);
        String companyName = companyBaseInfoApiDto.getName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (companyName != null) {
            CompanyAbnormalInfoListDto companyAbnormalInfo = CompanyBaseInfoUtil.getCompanyAbnormalInfo(companyName);
            if (companyAbnormalInfo.getTotal() == 0) {
                // 如果为0，则没有异常信息
                SysEntityExceptionInfoPo sysEntityExceptionInfoPo = new SysEntityExceptionInfoPo();
                sysEntityExceptionInfoPo.setBusinessStatus(0);
                sysEntityExceptionInfoPo.setEntityId(sysEntityPo.getId());
                sysEntityExceptionInfoBMapper.insert(sysEntityExceptionInfoPo);
            } else {
                List<CompanyAbnormalInformationDto> list = companyAbnormalInfo.getList();

                List<CompanyAbnormalInformationPo> companyAbnormalInformationPoList = new ArrayList<>();
                for (CompanyAbnormalInformationDto companyAbnormalInformationDto : list) {
                    CompanyAbnormalInformationPo po = new CompanyAbnormalInformationPo();
                    Date oDate = null;
                    Date iDate = null;
                    if (companyAbnormalInformationDto.getODate() != null && companyAbnormalInformationDto.getODate()!="") {
                        oDate = sdf.parse(companyAbnormalInformationDto.getODate());
                    }
                    if (companyAbnormalInformationDto.getIDate() != null && companyAbnormalInformationDto.getIDate() !="") {
                        iDate = sdf.parse(companyAbnormalInformationDto.getIDate());
                    }
                    BeanUtils.copyProperties(companyAbnormalInformationDto, po);
                    po.setCompanyName(companyName1);
                    po.setODate(oDate);
                    po.setIDate(iDate);

                    companyAbnormalInformationPoList.add(po);
                }
                // 删除旧的记录
                QueryWrapper<CompanyAbnormalInformationPo> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.lambda().eq(CompanyAbnormalInformationPo::getCompanyName, companyName);
                sysEntityExceptionInfoHistoryRecordsMapper.delete(queryWrapper1);
                // 插入新的记录
                sysEntityExceptionInfoHistoryRecordsMapper.insertBatches(companyAbnormalInformationPoList);
                if (!companyAbnormalInformationPoList.isEmpty()) {
                    CompanyAbnormalInformationPo latestAbnormalPo = null;
                    CompanyAbnormalInformationPo latestNormalPo = null;
                    for (CompanyAbnormalInformationPo po : companyAbnormalInformationPoList) {
                        Date currentIDate = po.getIDate();
                        Date currentODate = po.getODate();
                        if (currentIDate != null && (latestAbnormalPo == null || currentIDate.compareTo(latestAbnormalPo.getIDate()) > 0)) {
                            latestAbnormalPo = po;
                        }
                        if (currentODate != null && (latestNormalPo == null || currentODate.compareTo(latestNormalPo.getODate()) > 0)) {
                            latestNormalPo = po;
                        }
                    }
                    if (latestAbnormalPo != null && (latestNormalPo == null || latestAbnormalPo.getIDate() != null && latestAbnormalPo.getIDate().compareTo(latestNormalPo.getODate()) > 0)) {
                        // 有异常信息
                        SysEntityExceptionInfoPo sysEntityExceptionInfoPo = new SysEntityExceptionInfoPo();
                        sysEntityExceptionInfoPo.setIReason(latestAbnormalPo.getIReason());
                        sysEntityExceptionInfoPo.setIDate(sdf.format(latestAbnormalPo.getIDate()));
                        sysEntityExceptionInfoPo.setBusinessStatus(1);
                        sysEntityExceptionInfoPo.setEntityId(sysEntityPo.getId());
                        sysEntityExceptionInfoBMapper.insert(sysEntityExceptionInfoPo);
                    } else if (latestNormalPo != null) {
                        // 无异常信息
                        SysEntityExceptionInfoPo sysEntityExceptionInfoPo = new SysEntityExceptionInfoPo();
                        sysEntityExceptionInfoPo.setBusinessStatus(0);
                        sysEntityExceptionInfoPo.setEntityId(sysEntityPo.getId());
                        sysEntityExceptionInfoBMapper.insert(sysEntityExceptionInfoPo);
                    }
                }
            }

        }
    }

    @Override
    @DSTransactional
    public List<SysEntityExceptionInfoDto> refresh(Long[] ids) throws ParseException {
        List<SysEntityExceptionInfoDto> sysEntityExceptionInfoDtoList = new ArrayList<>();
        QueryWrapper<SysEntityExceptionInfoPo> query = new QueryWrapper<>();
        query.lambda().in(SysEntityExceptionInfoPo::getEntityId, ids);
        List<SysEntityExceptionInfoPo> sysEntityExceptionInfoPos = sysEntityExceptionInfoBMapper.selectList(query);
        if (!sysEntityExceptionInfoPos.isEmpty()) {
            for (SysEntityExceptionInfoPo sysEntityExceptionInfoPo : sysEntityExceptionInfoPos) {
                Long entityId = sysEntityExceptionInfoPo.getEntityId();
                SysEntityPo sysEntityPo = sysEntityMapper.selectById(entityId);
                String companyName = sysEntityPo.getName();
                QueryWrapper<SysEntityExceptionInfoPo> queryWrapper = new QueryWrapper<>();
                CompanyAbnormalInfoListDto companyAbnormalInfo = CompanyBaseInfoUtil.getCompanyAbnormalInfo(companyName);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                if (companyAbnormalInfo.getTotal() == 0) {
                    //如果为0，则没有异常信息
                    sysEntityExceptionInfoPo.setBusinessStatus(0);
                    sysEntityExceptionInfoPo.setEntityId(entityId);
                    sysEntityExceptionInfoPo.setIDate(null);
                    sysEntityExceptionInfoPo.setIReason(null);
                    queryWrapper.lambda().eq(SysEntityExceptionInfoPo::getEntityId, sysEntityExceptionInfoPo.getEntityId());
                    sysEntityExceptionInfoBMapper.update(sysEntityExceptionInfoPo, queryWrapper);
                    SysEntityExceptionInfoDto sysEntityExceptionInfoDto = new SysEntityExceptionInfoDto();
                    BeanUtils.copyProperties(sysEntityExceptionInfoPo, sysEntityExceptionInfoDto);
                    sysEntityExceptionInfoDto.setCompanyName(companyName);
                    sysEntityExceptionInfoDtoList.add(sysEntityExceptionInfoDto);
                } else {
                    List<CompanyAbnormalInformationPo> companyAbnormalInformationPoList = new ArrayList<>();
                    List<CompanyAbnormalInformationDto> list = companyAbnormalInfo.getList();
                    for (CompanyAbnormalInformationDto companyAbnormalInformationDto : list) {
                        CompanyAbnormalInformationPo po = new CompanyAbnormalInformationPo();
                        Date oDate = null;
                        Date iDate = null;
                        if (companyAbnormalInformationDto.getODate() != null&& companyAbnormalInformationDto.getODate()!="") {
                            oDate = sdf.parse(companyAbnormalInformationDto.getODate());
                        }
                        if (companyAbnormalInformationDto.getIDate() != null && companyAbnormalInformationDto.getIDate() !="") {
                            iDate = sdf.parse(companyAbnormalInformationDto.getIDate());
                        }
                        BeanUtils.copyProperties(companyAbnormalInformationDto, po);
                        po.setCompanyName(companyName);
                        po.setODate(oDate);
                        po.setIDate(iDate);
                        companyAbnormalInformationPoList.add(po);
                        QueryWrapper<CompanyAbnormalInformationPo> queryWrapper1 = new QueryWrapper<>();
                        queryWrapper1.lambda().eq(CompanyAbnormalInformationPo::getCompanyName, companyName);
                        sysEntityExceptionInfoHistoryRecordsMapper.delete(queryWrapper1);
                        sysEntityExceptionInfoHistoryRecordsMapper.insertBatches(companyAbnormalInformationPoList);
                    }
                    if (!companyAbnormalInformationPoList.isEmpty()) {
                        CompanyAbnormalInformationPo latestAbnormalPo = null;
                        CompanyAbnormalInformationPo latestNormalPo = null;
                        for (CompanyAbnormalInformationPo po : companyAbnormalInformationPoList) {
                            Date currentIDate = po.getIDate();
                            Date currentODate = po.getODate();
                            if (currentIDate != null && (latestAbnormalPo == null || currentIDate.compareTo(latestAbnormalPo.getIDate()) > 0)) {
                                latestAbnormalPo = po;
                            }
                            if (currentODate != null && (latestNormalPo == null || currentODate.compareTo(latestNormalPo.getODate()) > 0)) {
                                latestNormalPo = po;
                            }
                        }
                        if (latestAbnormalPo != null && (latestNormalPo == null || latestAbnormalPo.getIDate() != null && latestAbnormalPo.getIDate().compareTo(latestNormalPo.getODate()) > 0)) {
                            // 有异常信息
                            sysEntityExceptionInfoPo.setIReason(latestAbnormalPo.getIReason());
                            sysEntityExceptionInfoPo.setIDate(sdf.format(latestAbnormalPo.getIDate()));
                            sysEntityExceptionInfoPo.setBusinessStatus(1);
                            queryWrapper.lambda().eq(SysEntityExceptionInfoPo::getEntityId, sysEntityExceptionInfoPo.getEntityId());
                            sysEntityExceptionInfoBMapper.update(sysEntityExceptionInfoPo, queryWrapper);
                            SysEntityExceptionInfoDto sysEntityExceptionInfoDto = new SysEntityExceptionInfoDto();
                            BeanUtils.copyProperties(sysEntityExceptionInfoPo, sysEntityExceptionInfoDto);
                            sysEntityExceptionInfoDto.setCompanyName(companyName);
                            sysEntityExceptionInfoDtoList.add(sysEntityExceptionInfoDto);
                        } else if (latestNormalPo != null) {
                            // 无异常信息
                            sysEntityExceptionInfoPo.setBusinessStatus(0);
                            sysEntityExceptionInfoPo.setIDate(null);
                            sysEntityExceptionInfoPo.setIReason(null);
                            queryWrapper.lambda().eq(SysEntityExceptionInfoPo::getEntityId, sysEntityExceptionInfoPo.getEntityId());
                            sysEntityExceptionInfoBMapper.update(sysEntityExceptionInfoPo, queryWrapper);
                            SysEntityExceptionInfoDto sysEntityExceptionInfoDto = new SysEntityExceptionInfoDto();
                            BeanUtils.copyProperties(sysEntityExceptionInfoPo, sysEntityExceptionInfoDto);
                            sysEntityExceptionInfoDto.setCompanyName(companyName);
                            sysEntityExceptionInfoDtoList.add(sysEntityExceptionInfoDto);
                        }
                    }
                }
            }
        }
        return sysEntityExceptionInfoDtoList;
    }




    @Override
    public PageResult getHistoryRecord(String companyName, Integer page, Integer pageSize) {
        QueryWrapper<CompanyAbnormalInformationPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CompanyAbnormalInformationPo::getCompanyName, companyName);
        List<CompanyAbnormalInformationPo> companyAbnormalInformationPoList = sysEntityExceptionInfoHistoryRecordsMapper.selectList(queryWrapper);
        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, companyAbnormalInformationPoList.size());
        if (startIndex >= companyAbnormalInformationPoList.size()) {
            return new PageResult();
        }
        List<CompanyAbnormalInformationPo> list = companyAbnormalInformationPoList.subList(startIndex, endIndex);
        PageResult pageResult = new PageResult();
        pageResult.setItems(list);
        pageResult.setTotal(companyAbnormalInformationPoList.size());
        return pageResult;
    }


    @Override
    public PageResult AList(SysEntityExceptionInfoDto sysEntityExceptionInfoDto) {
        List<SysEntityPo> list = sysEntityMapper.select();
        if (list==null||list.size()==0){
            return new PageResult();
        }
        List<Long> entityIdList = list.stream().map(SysEntityPo::getId).collect(Collectors.toList());
        List<SysEntityExceptionInfoDto> sysEntityExceptionInfoDtos = sysEntityExceptionInfoBMapper.selectP(entityIdList, sysEntityExceptionInfoDto.getBusinessStatus());
        List<SysEntityExceptionInfoDto> list1 = new ArrayList<>();
        for (SysEntityExceptionInfoDto entityExceptionInfoDto1 : sysEntityExceptionInfoDtos) {
            for (SysEntityPo sysEntityPo : list) {
                if (sysEntityPo.getId().equals(entityExceptionInfoDto1.getEntityId())) {
                    entityExceptionInfoDto1.setCompanyName(sysEntityPo.getName());
                    list1.add(entityExceptionInfoDto1);
                }
            }
        }
        int startIndex = (sysEntityExceptionInfoDto.getPage() - 1) * sysEntityExceptionInfoDto.getPageSize();
        int endIndex = Math.min(startIndex + sysEntityExceptionInfoDto.getPageSize(), list1.size());
        if (startIndex >= list1.size()) {
            return new PageResult();
        }
        List<SysEntityExceptionInfoDto> sysEntityExceptionInfoDtoList = list1.subList(startIndex, endIndex);
        PageResult pageResult = new PageResult();
        pageResult.setItems(sysEntityExceptionInfoDtoList);
        pageResult.setTotal(sysEntityExceptionInfoDtos.size());
        return pageResult;
    }








//    private String getCompanyName(SysEntityPo sysEntityPo) {
//        CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(sysEntityPo.getName());
//        return companyBaseInfoApiDto.getName();
//    }
//
//    private void processCompanyAbnormalInfo(SysEntityPo sysEntityPo, String companyName)  {
//        CompanyAbnormalInfoListDto companyAbnormalInfo = CompanyBaseInfoUtil.getCompanyAbnormalInfo(companyName);
//        if (companyAbnormalInfo.getTotal() == 0) {
//            handleNoAbnormalInfo(sysEntityPo);
//        } else {
//            handleAbnormalInfo(sysEntityPo, companyName);
//        }
//    }
//
//    private void handleNoAbnormalInfo(SysEntityPo sysEntityPo) {
//        SysEntityExceptionInfoPo sysEntityExceptionInfoPo = new SysEntityExceptionInfoPo();
//        sysEntityExceptionInfoPo.setBusinessStatus(0);
//        sysEntityExceptionInfoPo.setEntityId(sysEntityPo.getId());
//        sysEntityExceptionInfoBMapper.insert(sysEntityExceptionInfoPo);
//    }
//
//    private void handleAbnormalInfo(SysEntityPo sysEntityPo, String companyName)  {
//        List<CompanyAbnormalInformationDto> list = CompanyBaseInfoUtil.getCompanyAbnormalInfo(companyName).getList();
//        List<CompanyAbnormalInformationPo> companyAbnormalInformationPoList = new ArrayList<>();
//        for (CompanyAbnormalInformationDto companyAbnormalInformationDto : list) {
//            CompanyAbnormalInformationPo po = parseDates(companyAbnormalInformationDto);
//            BeanUtils.copyProperties(companyAbnormalInformationDto, po);
//            companyAbnormalInformationPoList.add(po);
//        }
//        deleteAndInsertRecords(companyName, companyAbnormalInformationPoList);
//        updateLatestExceptionInfo(sysEntityPo, companyAbnormalInformationPoList);
//    }
//
//    private CompanyAbnormalInformationPo parseDates(CompanyAbnormalInformationDto dto) {
//        CompanyAbnormalInformationPo po = new CompanyAbnormalInformationPo();
//        po.setODate(parseDate(dto.getODate()));
//        po.setIDate(parseDate(dto.getIDate()));
//        return po;
//    }
//
//    private Date parseDate(String dateString) {
//        if (dateString != null && !dateString.isEmpty()) {
//            try {
//                return sdf.parse(dateString);
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
//        }
//        return null;
//    }
//
//    private void deleteAndInsertRecords(String companyName, List<CompanyAbnormalInformationPo> poList) {
//        QueryWrapper<CompanyAbnormalInformationPo> queryWrapper1 = new QueryWrapper<>();
//        queryWrapper1.lambda().eq(CompanyAbnormalInformationPo::getCompanyName, companyName);
//        sysEntityExceptionInfoHistoryRecordsMapper.delete(queryWrapper1);
//        sysEntityExceptionInfoHistoryRecordsMapper.insertBatches(poList);
//    }
//
//    private void updateLatestExceptionInfo(SysEntityPo sysEntityPo, List<CompanyAbnormalInformationPo> poList) {
//        CompanyAbnormalInformationPo latestAbnormalPo = null;
//        CompanyAbnormalInformationPo latestNormalPo = null;
//        for (CompanyAbnormalInformationPo po : poList) {
//            if (updateLatestPo(latestAbnormalPo, po, true)) {
//                latestAbnormalPo = po;
//            }
//            if (updateLatestPo(latestNormalPo, po, false)) {
//                latestNormalPo = po;
//            }
//        }
//        if (latestAbnormalPo != null && (latestNormalPo == null || latestAbnormalPo.getIDate() != null && latestAbnormalPo.getIDate().compareTo(latestNormalPo.getODate()) > 0)) {
//            updateExceptionInfo(sysEntityPo, latestAbnormalPo, 1);
//        } else if (latestNormalPo != null) {
//            updateExceptionInfo(sysEntityPo, latestNormalPo, 0);
//        }
//    }
//
//    private boolean updateLatestPo(CompanyAbnormalInformationPo latestPo, CompanyAbnormalInformationPo po, boolean isAbnormal) {
//        Date currentDate = latestPo == null ? null : isAbnormal ? latestPo.getIDate() : latestPo.getODate();
//        if (currentDate == null || po.getIDate() != null && po.getIDate().compareTo(currentDate) > 0) {
//            return true;
//        }
//        return false;
//    }
//
//    private void updateExceptionInfo(SysEntityPo sysEntityPo, CompanyAbnormalInformationPo po, int status) {
//        SysEntityExceptionInfoPo sysEntityExceptionInfoPo = new SysEntityExceptionInfoPo();
//        sysEntityExceptionInfoPo.setBusinessStatus(status);
//        sysEntityExceptionInfoPo.setEntityId(sysEntityPo.getId());
//        sysEntityExceptionInfoPo.setIReason(po.getIReason());
//        sysEntityExceptionInfoPo.setIDate(Optional.ofNullable(po.getIDate()).map(this::formatDate).orElse(null));
//        sysEntityExceptionInfoBMapper.insert(sysEntityExceptionInfoPo);
//    }
//
//    private String formatDate(Date date) {
//        return Optional.ofNullable(date).map(d -> sdf.format(d)).orElse(null);
//    }
//
//    @Override
//    @DSTransactional
//    public void entityAbnormalInfo(SysEntityPo sysEntityPo) throws ParseException {
//        String companyName = getCompanyName(sysEntityPo);
//        if (companyName != null) {
//            processCompanyAbnormalInfo(sysEntityPo, companyName);
//        }
//    }
//
//
//    @Override
//    @DSTransactional
//    public List<SysEntityExceptionInfoDto> refresh(Long[] ids) throws ParseException {
//        List<SysEntityExceptionInfoDto> sysEntityExceptionInfoDtoList = new ArrayList<>();
//        QueryWrapper<SysEntityExceptionInfoPo> query = new QueryWrapper<>();
//        query.lambda().in(SysEntityExceptionInfoPo::getEntityId, ids);
//        List<SysEntityExceptionInfoPo> sysEntityExceptionInfoPos = sysEntityExceptionInfoBMapper.selectList(query);
//        if (!sysEntityExceptionInfoPos.isEmpty()) {
//            for (SysEntityExceptionInfoPo sysEntityExceptionInfoPo : sysEntityExceptionInfoPos) {
//                Long entityId = sysEntityExceptionInfoPo.getEntityId();
//                SysEntityPo sysEntityPo = sysEntityMapper.selectById(entityId);
//                String companyName = sysEntityPo.getName();
//                processCompanyAbnormalInfo(sysEntityPo, companyName);
//                SysEntityExceptionInfoDto sysEntityExceptionInfoDto = new SysEntityExceptionInfoDto();
//                BeanUtils.copyProperties(sysEntityExceptionInfoPo, sysEntityExceptionInfoDto);
//                sysEntityExceptionInfoDto.setCompanyName(companyName);
//                sysEntityExceptionInfoDtoList.add(sysEntityExceptionInfoDto);
//            }
//        }
//        return sysEntityExceptionInfoDtoList;
//    }

}