package com.heju.system.forms.cascade.mapper;

import com.heju.system.forms.cascade.domain.query.SysCascadeQuery;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.po.SysCascadePo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;

/**
 * 级联管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysCascadeMapper extends BaseMapper<SysCascadeQuery, SysCascadeDto, SysCascadePo> {
}