package com.heju.system.third.service;

import com.heju.system.third.domain.query.SysThirdQuery;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;

/**
 * 第三方模块管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysThirdService extends IBaseService<SysThirdQuery, SysThirdDto> {

    /**
     * 校验第三方编码是否唯一
     *
     * @param Id   第三方Id
     * @param code 第三方编码
     * @return 结果 | true/false 唯一/不唯一
     */
    boolean checkThirdCodeUnique(Long Id, String code);

}