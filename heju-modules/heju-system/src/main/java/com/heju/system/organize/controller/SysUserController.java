package com.heju.system.organize.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.TenantConstants;
import com.heju.common.core.utils.core.CollUtil;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.security.service.TokenService;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.api.model.DataScope;
import com.heju.system.api.model.LoginUser;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.query.SysDeptQuery;
import com.heju.system.api.organize.domain.query.SysUserQuery;
import com.heju.system.organize.service.ISysDeptService;
import com.heju.system.organize.service.ISysOrganizeService;
import com.heju.system.organize.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * 用户管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController<SysUserQuery, SysUserDto, ISysUserService> {

    @Autowired
    private ISysOrganizeService organizeService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysUserService userService;

    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "用户";
    }

    /**
     * 新增用户 | 内部调用
     */
    @InnerAuth
    @PostMapping("/inner/add")
    public R<SysUserDto> addInner(@RequestBody SysUserDto user) {
        return baseService.addInner(user) > 0 ? R.ok(user) : R.fail();
    }

    /**
     * 新增用户 | 内部调用（不添加岗位信息）
     */
    @InnerAuth
    @PostMapping("/inner/addUser")
    public R<SysUserDto> addInnerUser(@RequestBody SysUserDto user,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName) {
        SysUserDto userDto = userService.innerAddUser(user, sourceName);
        return R.ok(userDto);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser();
        baseService.userDesensitized(loginUser.getUser());
        HashMap<String, Object> map = new HashMap<>();
        map.put("enterprise", loginUser.getEnterprise());
        map.put("user", loginUser.getUser());
        DataScope dataScope = tokenService.getDataScope();
        map.put("roles", dataScope.getRoles());
        map.put("permissions", dataScope.getPermissions());
        map.put("routes", tokenService.getRouteURL());
        return success(map);
    }

    /**
     * 查询用户列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_USER_LIST)
    public AjaxResult list(SysUserQuery user) {
        startPage();
        List<SysUserDto> list = new ArrayList<>();
        if (ObjectUtil.isEmpty(user.getCompanyId())) {
            list = baseService.selectListScope(user);
        } else {
            SysDeptQuery sysDeptQuery = new SysDeptQuery();
            sysDeptQuery.setCompanyId(user.getCompanyId());
            sysDeptQuery.setLevel(NumberUtil.Zero);
            List<SysDeptDto> sysDeptDtoList = sysDeptService.selectList(sysDeptQuery);
            for (SysDeptDto sysDeptDto : sysDeptDtoList) {
                user.setDeptId(sysDeptDto.getId());
                list.addAll(baseService.selectListScope(user));
            }
        }
        list.forEach(item -> baseService.userDesensitized(item));
        return getDataTable(list);
    }

    /**
     * 查询用户详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_USER_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 查询用户详细(内部)
     */
    @InnerAuth
    @GetMapping(value = "/getInfoInner/{id}")
    @RequiresPermissions(Auth.SYS_USER_SINGLE)
    public AjaxResult getInfoInner(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 通过openId查询用户 | 内部调用
     */
    @GetMapping(value = "/inner/getInfo")
    public SysUserDto innerGetInfo(String openId,String sourceName) {
        return userService.selectByOpenId(openId,sourceName);
    }

    /**
     * 查询用户关联的角色Id集
     */
    @GetMapping(value = "/auth/{id}")
    @RequiresPermissions(Auth.SYS_USER_AUTH)
    public AjaxResult getRoleAuth(@PathVariable Long id) {
        return success(organizeService.selectUserRoleMerge(id));
    }

    /**
     * 用户导出
     */
    @Override
    @PostMapping("/export")
    @RequiresPermissions(Auth.SYS_USER_EXPORT)
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    public void export(HttpServletResponse response, SysUserQuery user) {
        super.export(response, user);
    }

    /**
     * 用户新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_USER_ADD)
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysUserDto user) {
        user.setPassword(TenantConstants.DEFAULT_PASSWORD);
        user.setCode(""+ UUID.randomUUID());
        return super.add(user);
    }

    /**
     * 用户修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_USER_EDIT)
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysUserDto user) {
        return super.edit(user);
    }

    /**
     * 修改用户关联的角色Id集
     */
    @PutMapping(value = "/auth")
    @RequiresPermissions(Auth.SYS_USER_AUTH)
    public AjaxResult editRoleAuth(@RequestBody SysUserDto user) {
        organizeService.editUserRoleMerge(user.getId(), user.getRoleIds());
        return success();
    }

    /**
     * 修改用户关联的角色Id集 | 内部调用
     */
    @PutMapping(value = "/inner/auth")
    public AjaxResult innerEditRoleAuth(@RequestBody SysUserDto user) {
        organizeService.editUserRoleMerge(user.getId(), user.getRoleIds());
        return success();
    }

    /**
     * 用户修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_USER_EDIT, Auth.SYS_USER_ES}, logical = Logical.OR)
    @Log(title = "用户管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysUserDto user) {
        return super.editStatus(user);
    }

    /**
     * 用户批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_USER_DEL)
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 重置密码
     */
    @PutMapping("/resetPwd")
    @RequiresPermissions(Auth.SYS_USER_RESET_PASSWORD)
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    public AjaxResult resetPassword(@RequestBody SysUserDto user) {
        adminValidated(user.getId());
        return toAjax(baseService.resetUserPassword(user.getId(), SecurityUtils.encryptPassword(user.getPassword())));
    }

    /**
     * 获取用户选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysUserDto user) {

        if (operate.isEdit())
            adminValidated(user.getId());
        if (baseService.checkUserCodeUnique(user.getId(), user.getCode()))
            warn(StrUtil.format("{}{}{}失败，用户编码已存在", operate.getInfo(), getNodeName(), user.getNickName()));
        else if (baseService.checkUserNameUnique(user.getId(), user.getUserName()))
            warn(StrUtil.format("{}{}{}失败，用户账号已存在", operate.getInfo(), getNodeName(), user.getNickName()));
        else if (StrUtil.isNotEmpty(user.getEmail()) && baseService.checkPhoneUnique(user.getId(), user.getCode()))
            warn(StrUtil.format("{}{}{}失败，手机号码已存在", operate.getInfo(), getNodeName(), user.getNickName()));
        else if (StrUtil.isNotEmpty(user.getEmail()) && baseService.checkEmailUnique(user.getId(), user.getName()))
            warn(StrUtil.format("{}{}{}失败，邮箱账号已存在", operate.getInfo(), getNodeName(), user.getNickName()));
        switch (operate) {
            case ADD:
                // 防止修改操作更替密码
                user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
                break;
            case EDIT_STATUS:
                adminValidated(user.getId());
                break;
        }
    }

    /**
     * 前置校验 （强制）删除
     *
     * @param idList Id集合
     */
    @Override
    protected void RHandle(BaseConstants.Operate operate, List<Long> idList) {
        int size = idList.size();
        Long userId = SecurityUtils.getUserId();
        // remove oneself or admin
        for (int i = idList.size() - 1; i >= 0; i--)
            if (ObjectUtil.equals(idList.get(i), userId) || !baseService.checkUserAllowed(idList.get(i)))
                idList.remove(i);
        if (CollUtil.isEmpty(idList))
            warn("删除失败，不能删除自己或超管用户！");
        else if (idList.size() != size) {
            baseService.deleteByIds(idList);
            warn("成功删除除自己及超管用户外的所有用户！");
        }
    }

    /**
     * 校验归属的岗位是否启用
     */
    private void adminValidated(Long Id) {
        if (!baseService.checkUserAllowed(Id))
            warn("不允许操作超级管理员用户");
    }
}
