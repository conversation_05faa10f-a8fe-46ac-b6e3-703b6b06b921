package com.heju.system.entity.service.impl;

import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.query.SysEntityTaxationTypeQuery;
import com.heju.system.entity.service.ISysEntityTaxationTypeService;
import com.heju.system.entity.manager.ISysEntityTaxationTypeManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体税务税费种认定管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysEntityTaxationTypeServiceImpl extends BaseServiceImpl<SysEntityTaxationTypeQuery, SysEntityTaxationTypeDto, ISysEntityTaxationTypeManager> implements ISysEntityTaxationTypeService {

    /**
     * 查询实体税务税费种认定对象列表 | 数据权限
     *
     * @param entityTaxationType 实体税务税费种认定对象
     * @return 实体税务税费种认定对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysEntityTaxationTypeMapper"})
    public List<SysEntityTaxationTypeDto> selectListScope(SysEntityTaxationTypeQuery entityTaxationType) {
        return baseManager.selectList(entityTaxationType);
    }

}