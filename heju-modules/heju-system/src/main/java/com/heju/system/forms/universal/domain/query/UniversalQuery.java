package com.heju.system.forms.universal.domain.query;

import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class UniversalQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    private String sheetApiName;

    private Long sheetId;

    private Long id;

    private Integer page;

    private Integer pageSize;

    private String orderSort;

    private List<String> sheetApiNameList;

    private Long fieldId;

    private List<Long> idList;

    private List<String> optionFieldList;

    private String universalJson;

    private Integer addType;

    private Long foreignKey;

}
