package com.heju.system.forms.option.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.domain.query.SysOptionQuery;
import com.heju.system.forms.option.service.ISysOptionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 选项管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/option")
public class SysOptionController extends BaseController<SysOptionQuery, SysOptionDto, ISysOptionService> {

    @Resource
    private ISysFieldService fieldService;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "选项" ;
    }

    /**
     * 查询选项列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_OPTION_LIST)
    public AjaxResult list(SysOptionQuery option) {
        return super.list(option);
    }

    /**
     * 查询选项详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_OPTION_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 选项新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_OPTION_ADD)
    @Log(title = "选项管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysOptionDto option) {
        return super.add(option);
    }

    /**
     * 选项修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_OPTION_EDIT)
    @Log(title = "选项管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysOptionDto option) {
        return super.edit(option);
    }

    /**
     * 选项修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_OPTION_EDIT, Auth.SYS_OPTION_ES}, logical = Logical.OR)
    @Log(title = "选项管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysOptionDto option) {
        return super.editStatus(option);
    }

    /**
     * 选项批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_OPTION_DEL)
    @Log(title = "选项管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取选项选择框列表
     */
    @GetMapping("/option")
    public AjaxResult option(SysOptionQuery option) {
        return baseService.option(option);
    }

    /**
     * 查看引用
     */
    @GetMapping("/searchQuote")
    @RequiresPermissions(Auth.SYS_OPTION_QUOTE)
    public AjaxResult searchQuote(SysOptionQuery option) {
        SysFieldQuery query=new SysFieldQuery();
        query.setOptionId(option.getId());
        return fieldService.searchQuote(query);
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysOptionDto option) {
        if (baseService.checkConfigCodeUnique(option.getId(), option.getName()))
            warn(StrUtil.format("{}{}失败,{}名称已存在", operate.getInfo(), getNodeName(), option.getName()));
    }

}
