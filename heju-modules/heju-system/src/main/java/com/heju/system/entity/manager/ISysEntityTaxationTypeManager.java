package com.heju.system.entity.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import com.heju.system.entity.domain.query.SysEntityTaxationTypeQuery;

import java.util.List;

/**
 * 实体税务税费种认定管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysEntityTaxationTypeManager extends IBaseManager<SysEntityTaxationTypeQuery, SysEntityTaxationTypeDto> {

    List<SysEntityTaxationTypePo> getByEntityId(Long id);
    int updateByExamine(SysEntityExamineDto entityExamine);
}