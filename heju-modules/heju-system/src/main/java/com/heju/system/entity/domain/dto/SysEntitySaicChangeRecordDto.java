package com.heju.system.entity.domain.dto;

import com.heju.system.entity.domain.po.SysEntitySaicChangeRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 实体工商变更记录 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntitySaicChangeRecordDto extends SysEntitySaicChangeRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;

}