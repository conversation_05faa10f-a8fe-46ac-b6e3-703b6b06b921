package com.heju.system.entity.domain.dto;


import com.heju.common.core.annotation.Excel;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 企业经营异常信息 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEntityExceptionInfoDto extends SysEntityExceptionInfoPo {

    @Serial
    private static final long serialVersionUID = 1L;
    /** 实体公司名称 */
    @Excel(name = "实体公司名称")
    protected String companyName;

    private Integer page;

    private Integer pageSize;


}