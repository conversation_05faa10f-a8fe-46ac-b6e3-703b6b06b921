package com.heju.system.monitor.service.impl;


import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.log.domain.dto.SysLoginLogDto;
import com.heju.system.api.log.domain.query.SysLoginLogQuery;
import com.heju.system.monitor.manager.ISysLoginLogManager;
import com.heju.system.monitor.service.ISysLoginLogService;
import org.springframework.stereotype.Service;

/**
 * 访问日志管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysLoginLogServiceImpl extends BaseServiceImpl<SysLoginLogQuery, SysLoginLogDto, ISysLoginLogManager> implements ISysLoginLogService {

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLoginLog() {
        baseManager.cleanLoginLog();
    }
}
