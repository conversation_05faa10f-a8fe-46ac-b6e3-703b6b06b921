package com.heju.system.third.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.third.domain.dto.SysThirdAuthDto;
import com.heju.system.third.domain.po.SysThirdAuthPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:15+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysThirdAuthConverterImpl implements SysThirdAuthConverter {

    @Override
    public SysThirdAuthDto mapperDto(SysThirdAuthPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysThirdAuthDto sysThirdAuthDto = new SysThirdAuthDto();

        sysThirdAuthDto.setId( arg0.getId() );
        sysThirdAuthDto.setSourceName( arg0.getSourceName() );
        sysThirdAuthDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysThirdAuthDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysThirdAuthDto.setName( arg0.getName() );
        sysThirdAuthDto.setStatus( arg0.getStatus() );
        sysThirdAuthDto.setSort( arg0.getSort() );
        sysThirdAuthDto.setCreateBy( arg0.getCreateBy() );
        sysThirdAuthDto.setCreateTime( arg0.getCreateTime() );
        sysThirdAuthDto.setUpdateBy( arg0.getUpdateBy() );
        sysThirdAuthDto.setUpdateTime( arg0.getUpdateTime() );
        sysThirdAuthDto.setDelFlag( arg0.getDelFlag() );
        sysThirdAuthDto.setCreateName( arg0.getCreateName() );
        sysThirdAuthDto.setUpdateName( arg0.getUpdateName() );
        sysThirdAuthDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysThirdAuthDto.setCode( arg0.getCode() );
        sysThirdAuthDto.setAuthJson( arg0.getAuthJson() );
        sysThirdAuthDto.setIsAdmin( arg0.getIsAdmin() );
        sysThirdAuthDto.setThirdId( arg0.getThirdId() );
        sysThirdAuthDto.setRemark( arg0.getRemark() );

        return sysThirdAuthDto;
    }

    @Override
    public List<SysThirdAuthDto> mapperDto(Collection<SysThirdAuthPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysThirdAuthDto> list = new ArrayList<SysThirdAuthDto>( arg0.size() );
        for ( SysThirdAuthPo sysThirdAuthPo : arg0 ) {
            list.add( mapperDto( sysThirdAuthPo ) );
        }

        return list;
    }

    @Override
    public Page<SysThirdAuthDto> mapperPageDto(Collection<SysThirdAuthPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysThirdAuthDto> page = new Page<SysThirdAuthDto>();
        for ( SysThirdAuthPo sysThirdAuthPo : arg0 ) {
            page.add( mapperDto( sysThirdAuthPo ) );
        }

        return page;
    }

    @Override
    public SysThirdAuthPo mapperPo(SysThirdAuthDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysThirdAuthPo sysThirdAuthPo = new SysThirdAuthPo();

        sysThirdAuthPo.setId( arg0.getId() );
        sysThirdAuthPo.setSourceName( arg0.getSourceName() );
        sysThirdAuthPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysThirdAuthPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysThirdAuthPo.setName( arg0.getName() );
        sysThirdAuthPo.setStatus( arg0.getStatus() );
        sysThirdAuthPo.setSort( arg0.getSort() );
        sysThirdAuthPo.setCreateBy( arg0.getCreateBy() );
        sysThirdAuthPo.setCreateTime( arg0.getCreateTime() );
        sysThirdAuthPo.setUpdateBy( arg0.getUpdateBy() );
        sysThirdAuthPo.setUpdateTime( arg0.getUpdateTime() );
        sysThirdAuthPo.setDelFlag( arg0.getDelFlag() );
        sysThirdAuthPo.setCreateName( arg0.getCreateName() );
        sysThirdAuthPo.setUpdateName( arg0.getUpdateName() );
        sysThirdAuthPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysThirdAuthPo.setCode( arg0.getCode() );
        sysThirdAuthPo.setAuthJson( arg0.getAuthJson() );
        sysThirdAuthPo.setIsAdmin( arg0.getIsAdmin() );
        sysThirdAuthPo.setThirdId( arg0.getThirdId() );
        sysThirdAuthPo.setRemark( arg0.getRemark() );

        return sysThirdAuthPo;
    }

    @Override
    public List<SysThirdAuthPo> mapperPo(Collection<SysThirdAuthDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysThirdAuthPo> list = new ArrayList<SysThirdAuthPo>( arg0.size() );
        for ( SysThirdAuthDto sysThirdAuthDto : arg0 ) {
            list.add( mapperPo( sysThirdAuthDto ) );
        }

        return list;
    }

    @Override
    public Page<SysThirdAuthPo> mapperPagePo(Collection<SysThirdAuthDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysThirdAuthPo> page = new Page<SysThirdAuthPo>();
        for ( SysThirdAuthDto sysThirdAuthDto : arg0 ) {
            page.add( mapperPo( sysThirdAuthDto ) );
        }

        return page;
    }
}
