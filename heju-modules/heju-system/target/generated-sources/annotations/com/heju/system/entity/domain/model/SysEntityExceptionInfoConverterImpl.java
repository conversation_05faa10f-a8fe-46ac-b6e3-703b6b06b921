package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntityExceptionInfoDto;
import com.heju.system.entity.domain.po.SysEntityExceptionInfoPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:15+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntityExceptionInfoConverterImpl implements SysEntityExceptionInfoConverter {

    @Override
    public SysEntityExceptionInfoDto mapperDto(SysEntityExceptionInfoPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityExceptionInfoDto sysEntityExceptionInfoDto = new SysEntityExceptionInfoDto();

        sysEntityExceptionInfoDto.setId( arg0.getId() );
        sysEntityExceptionInfoDto.setSourceName( arg0.getSourceName() );
        sysEntityExceptionInfoDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityExceptionInfoDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityExceptionInfoDto.setName( arg0.getName() );
        sysEntityExceptionInfoDto.setStatus( arg0.getStatus() );
        sysEntityExceptionInfoDto.setSort( arg0.getSort() );
        sysEntityExceptionInfoDto.setRemark( arg0.getRemark() );
        sysEntityExceptionInfoDto.setCreateBy( arg0.getCreateBy() );
        sysEntityExceptionInfoDto.setCreateTime( arg0.getCreateTime() );
        sysEntityExceptionInfoDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntityExceptionInfoDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntityExceptionInfoDto.setDelFlag( arg0.getDelFlag() );
        sysEntityExceptionInfoDto.setCreateName( arg0.getCreateName() );
        sysEntityExceptionInfoDto.setUpdateName( arg0.getUpdateName() );
        sysEntityExceptionInfoDto.setEntityId( arg0.getEntityId() );
        sysEntityExceptionInfoDto.setBusinessStatus( arg0.getBusinessStatus() );
        sysEntityExceptionInfoDto.setIReason( arg0.getIReason() );
        sysEntityExceptionInfoDto.setIDate( arg0.getIDate() );

        return sysEntityExceptionInfoDto;
    }

    @Override
    public List<SysEntityExceptionInfoDto> mapperDto(Collection<SysEntityExceptionInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityExceptionInfoDto> list = new ArrayList<SysEntityExceptionInfoDto>( arg0.size() );
        for ( SysEntityExceptionInfoPo sysEntityExceptionInfoPo : arg0 ) {
            list.add( mapperDto( sysEntityExceptionInfoPo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityExceptionInfoDto> mapperPageDto(Collection<SysEntityExceptionInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityExceptionInfoDto> page = new Page<SysEntityExceptionInfoDto>();
        for ( SysEntityExceptionInfoPo sysEntityExceptionInfoPo : arg0 ) {
            page.add( mapperDto( sysEntityExceptionInfoPo ) );
        }

        return page;
    }

    @Override
    public SysEntityExceptionInfoPo mapperPo(SysEntityExceptionInfoDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityExceptionInfoPo sysEntityExceptionInfoPo = new SysEntityExceptionInfoPo();

        sysEntityExceptionInfoPo.setId( arg0.getId() );
        sysEntityExceptionInfoPo.setSourceName( arg0.getSourceName() );
        sysEntityExceptionInfoPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityExceptionInfoPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityExceptionInfoPo.setName( arg0.getName() );
        sysEntityExceptionInfoPo.setStatus( arg0.getStatus() );
        sysEntityExceptionInfoPo.setSort( arg0.getSort() );
        sysEntityExceptionInfoPo.setRemark( arg0.getRemark() );
        sysEntityExceptionInfoPo.setCreateBy( arg0.getCreateBy() );
        sysEntityExceptionInfoPo.setCreateTime( arg0.getCreateTime() );
        sysEntityExceptionInfoPo.setUpdateBy( arg0.getUpdateBy() );
        sysEntityExceptionInfoPo.setUpdateTime( arg0.getUpdateTime() );
        sysEntityExceptionInfoPo.setDelFlag( arg0.getDelFlag() );
        sysEntityExceptionInfoPo.setCreateName( arg0.getCreateName() );
        sysEntityExceptionInfoPo.setUpdateName( arg0.getUpdateName() );
        sysEntityExceptionInfoPo.setEntityId( arg0.getEntityId() );
        sysEntityExceptionInfoPo.setBusinessStatus( arg0.getBusinessStatus() );
        sysEntityExceptionInfoPo.setIReason( arg0.getIReason() );
        sysEntityExceptionInfoPo.setIDate( arg0.getIDate() );

        return sysEntityExceptionInfoPo;
    }

    @Override
    public List<SysEntityExceptionInfoPo> mapperPo(Collection<SysEntityExceptionInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityExceptionInfoPo> list = new ArrayList<SysEntityExceptionInfoPo>( arg0.size() );
        for ( SysEntityExceptionInfoDto sysEntityExceptionInfoDto : arg0 ) {
            list.add( mapperPo( sysEntityExceptionInfoDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityExceptionInfoPo> mapperPagePo(Collection<SysEntityExceptionInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityExceptionInfoPo> page = new Page<SysEntityExceptionInfoPo>();
        for ( SysEntityExceptionInfoDto sysEntityExceptionInfoDto : arg0 ) {
            page.add( mapperPo( sysEntityExceptionInfoDto ) );
        }

        return page;
    }
}
