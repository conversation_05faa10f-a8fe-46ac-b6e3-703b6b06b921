package com.heju.system.forms.option.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.domain.po.SysOptionPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:15+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysOptionConverterImpl implements SysOptionConverter {

    @Override
    public SysOptionDto mapperDto(SysOptionPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysOptionDto sysOptionDto = new SysOptionDto();

        sysOptionDto.setId( arg0.getId() );
        sysOptionDto.setSourceName( arg0.getSourceName() );
        sysOptionDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysOptionDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysOptionDto.setName( arg0.getName() );
        sysOptionDto.setStatus( arg0.getStatus() );
        sysOptionDto.setSort( arg0.getSort() );
        sysOptionDto.setCreateBy( arg0.getCreateBy() );
        sysOptionDto.setCreateTime( arg0.getCreateTime() );
        sysOptionDto.setUpdateBy( arg0.getUpdateBy() );
        sysOptionDto.setUpdateTime( arg0.getUpdateTime() );
        sysOptionDto.setDelFlag( arg0.getDelFlag() );
        sysOptionDto.setCreateName( arg0.getCreateName() );
        sysOptionDto.setUpdateName( arg0.getUpdateName() );
        sysOptionDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysOptionDto.setOptionType( arg0.getOptionType() );
        sysOptionDto.setApiName( arg0.getApiName() );
        sysOptionDto.setRemark( arg0.getRemark() );

        return sysOptionDto;
    }

    @Override
    public List<SysOptionDto> mapperDto(Collection<SysOptionPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysOptionDto> list = new ArrayList<SysOptionDto>( arg0.size() );
        for ( SysOptionPo sysOptionPo : arg0 ) {
            list.add( mapperDto( sysOptionPo ) );
        }

        return list;
    }

    @Override
    public Page<SysOptionDto> mapperPageDto(Collection<SysOptionPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysOptionDto> page = new Page<SysOptionDto>();
        for ( SysOptionPo sysOptionPo : arg0 ) {
            page.add( mapperDto( sysOptionPo ) );
        }

        return page;
    }

    @Override
    public SysOptionPo mapperPo(SysOptionDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysOptionPo sysOptionPo = new SysOptionPo();

        sysOptionPo.setId( arg0.getId() );
        sysOptionPo.setSourceName( arg0.getSourceName() );
        sysOptionPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysOptionPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysOptionPo.setName( arg0.getName() );
        sysOptionPo.setStatus( arg0.getStatus() );
        sysOptionPo.setSort( arg0.getSort() );
        sysOptionPo.setCreateBy( arg0.getCreateBy() );
        sysOptionPo.setCreateTime( arg0.getCreateTime() );
        sysOptionPo.setUpdateBy( arg0.getUpdateBy() );
        sysOptionPo.setUpdateTime( arg0.getUpdateTime() );
        sysOptionPo.setDelFlag( arg0.getDelFlag() );
        sysOptionPo.setCreateName( arg0.getCreateName() );
        sysOptionPo.setUpdateName( arg0.getUpdateName() );
        sysOptionPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysOptionPo.setOptionType( arg0.getOptionType() );
        sysOptionPo.setApiName( arg0.getApiName() );
        sysOptionPo.setRemark( arg0.getRemark() );

        return sysOptionPo;
    }

    @Override
    public List<SysOptionPo> mapperPo(Collection<SysOptionDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysOptionPo> list = new ArrayList<SysOptionPo>( arg0.size() );
        for ( SysOptionDto sysOptionDto : arg0 ) {
            list.add( mapperPo( sysOptionDto ) );
        }

        return list;
    }

    @Override
    public Page<SysOptionPo> mapperPagePo(Collection<SysOptionDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysOptionPo> page = new Page<SysOptionPo>();
        for ( SysOptionDto sysOptionDto : arg0 ) {
            page.add( mapperPo( sysOptionDto ) );
        }

        return page;
    }
}
