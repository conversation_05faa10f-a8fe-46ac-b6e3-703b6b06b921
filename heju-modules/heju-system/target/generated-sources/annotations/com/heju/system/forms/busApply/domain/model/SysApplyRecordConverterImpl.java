package com.heju.system.forms.busApply.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-03T11:55:27+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysApplyRecordConverterImpl implements SysApplyRecordConverter {

    @Override
    public SysApplyRecordDto mapperDto(SysApplyRecordPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysApplyRecordDto sysApplyRecordDto = new SysApplyRecordDto();

        sysApplyRecordDto.setId( arg0.getId() );
        sysApplyRecordDto.setSourceName( arg0.getSourceName() );
        sysApplyRecordDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysApplyRecordDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysApplyRecordDto.setName( arg0.getName() );
        sysApplyRecordDto.setStatus( arg0.getStatus() );
        sysApplyRecordDto.setSort( arg0.getSort() );
        sysApplyRecordDto.setCreateBy( arg0.getCreateBy() );
        sysApplyRecordDto.setCreateTime( arg0.getCreateTime() );
        sysApplyRecordDto.setUpdateBy( arg0.getUpdateBy() );
        sysApplyRecordDto.setUpdateTime( arg0.getUpdateTime() );
        sysApplyRecordDto.setDelFlag( arg0.getDelFlag() );
        sysApplyRecordDto.setCreateName( arg0.getCreateName() );
        sysApplyRecordDto.setUpdateName( arg0.getUpdateName() );
        sysApplyRecordDto.setBusinessId( arg0.getBusinessId() );
        sysApplyRecordDto.setApiName( arg0.getApiName() );
        sysApplyRecordDto.setApplyTime( arg0.getApplyTime() );
        sysApplyRecordDto.setApplyReason( arg0.getApplyReason() );
        sysApplyRecordDto.setRejectReason( arg0.getRejectReason() );
        sysApplyRecordDto.setApplyStatus( arg0.getApplyStatus() );
        sysApplyRecordDto.setRemark( arg0.getRemark() );

        return sysApplyRecordDto;
    }

    @Override
    public List<SysApplyRecordDto> mapperDto(Collection<SysApplyRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysApplyRecordDto> list = new ArrayList<SysApplyRecordDto>( arg0.size() );
        for ( SysApplyRecordPo sysApplyRecordPo : arg0 ) {
            list.add( mapperDto( sysApplyRecordPo ) );
        }

        return list;
    }

    @Override
    public Page<SysApplyRecordDto> mapperPageDto(Collection<SysApplyRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysApplyRecordDto> page = new Page<SysApplyRecordDto>();
        for ( SysApplyRecordPo sysApplyRecordPo : arg0 ) {
            page.add( mapperDto( sysApplyRecordPo ) );
        }

        return page;
    }

    @Override
    public SysApplyRecordPo mapperPo(SysApplyRecordDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysApplyRecordPo sysApplyRecordPo = new SysApplyRecordPo();

        sysApplyRecordPo.setId( arg0.getId() );
        sysApplyRecordPo.setSourceName( arg0.getSourceName() );
        sysApplyRecordPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysApplyRecordPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysApplyRecordPo.setName( arg0.getName() );
        sysApplyRecordPo.setStatus( arg0.getStatus() );
        sysApplyRecordPo.setSort( arg0.getSort() );
        sysApplyRecordPo.setCreateBy( arg0.getCreateBy() );
        sysApplyRecordPo.setCreateTime( arg0.getCreateTime() );
        sysApplyRecordPo.setUpdateBy( arg0.getUpdateBy() );
        sysApplyRecordPo.setUpdateTime( arg0.getUpdateTime() );
        sysApplyRecordPo.setDelFlag( arg0.getDelFlag() );
        sysApplyRecordPo.setCreateName( arg0.getCreateName() );
        sysApplyRecordPo.setUpdateName( arg0.getUpdateName() );
        sysApplyRecordPo.setBusinessId( arg0.getBusinessId() );
        sysApplyRecordPo.setApiName( arg0.getApiName() );
        sysApplyRecordPo.setApplyTime( arg0.getApplyTime() );
        sysApplyRecordPo.setApplyReason( arg0.getApplyReason() );
        sysApplyRecordPo.setRejectReason( arg0.getRejectReason() );
        sysApplyRecordPo.setApplyStatus( arg0.getApplyStatus() );
        sysApplyRecordPo.setRemark( arg0.getRemark() );

        return sysApplyRecordPo;
    }

    @Override
    public List<SysApplyRecordPo> mapperPo(Collection<SysApplyRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysApplyRecordPo> list = new ArrayList<SysApplyRecordPo>( arg0.size() );
        for ( SysApplyRecordDto sysApplyRecordDto : arg0 ) {
            list.add( mapperPo( sysApplyRecordDto ) );
        }

        return list;
    }

    @Override
    public Page<SysApplyRecordPo> mapperPagePo(Collection<SysApplyRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysApplyRecordPo> page = new Page<SysApplyRecordPo>();
        for ( SysApplyRecordDto sysApplyRecordDto : arg0 ) {
            page.add( mapperPo( sysApplyRecordDto ) );
        }

        return page;
    }
}
