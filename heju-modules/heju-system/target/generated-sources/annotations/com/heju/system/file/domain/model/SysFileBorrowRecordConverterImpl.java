package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFileBorrowRecordDto;
import com.heju.system.file.domain.po.SysFileBorrowRecordPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:14+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFileBorrowRecordConverterImpl implements SysFileBorrowRecordConverter {

    @Override
    public SysFileBorrowRecordDto mapperDto(SysFileBorrowRecordPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileBorrowRecordDto sysFileBorrowRecordDto = new SysFileBorrowRecordDto();

        sysFileBorrowRecordDto.setId( arg0.getId() );
        sysFileBorrowRecordDto.setSourceName( arg0.getSourceName() );
        sysFileBorrowRecordDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileBorrowRecordDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileBorrowRecordDto.setName( arg0.getName() );
        sysFileBorrowRecordDto.setStatus( arg0.getStatus() );
        sysFileBorrowRecordDto.setSort( arg0.getSort() );
        sysFileBorrowRecordDto.setRemark( arg0.getRemark() );
        sysFileBorrowRecordDto.setCreateBy( arg0.getCreateBy() );
        sysFileBorrowRecordDto.setCreateTime( arg0.getCreateTime() );
        sysFileBorrowRecordDto.setUpdateBy( arg0.getUpdateBy() );
        sysFileBorrowRecordDto.setUpdateTime( arg0.getUpdateTime() );
        sysFileBorrowRecordDto.setDelFlag( arg0.getDelFlag() );
        sysFileBorrowRecordDto.setCreateName( arg0.getCreateName() );
        sysFileBorrowRecordDto.setUpdateName( arg0.getUpdateName() );
        sysFileBorrowRecordDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileBorrowRecordDto.setFileId( arg0.getFileId() );
        sysFileBorrowRecordDto.setBorrowUserId( arg0.getBorrowUserId() );
        sysFileBorrowRecordDto.setStartTime( arg0.getStartTime() );
        sysFileBorrowRecordDto.setEndTime( arg0.getEndTime() );
        sysFileBorrowRecordDto.setIsView( arg0.getIsView() );
        sysFileBorrowRecordDto.setIsDownload( arg0.getIsDownload() );

        return sysFileBorrowRecordDto;
    }

    @Override
    public List<SysFileBorrowRecordDto> mapperDto(Collection<SysFileBorrowRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileBorrowRecordDto> list = new ArrayList<SysFileBorrowRecordDto>( arg0.size() );
        for ( SysFileBorrowRecordPo sysFileBorrowRecordPo : arg0 ) {
            list.add( mapperDto( sysFileBorrowRecordPo ) );
        }

        return list;
    }

    @Override
    public Page<SysFileBorrowRecordDto> mapperPageDto(Collection<SysFileBorrowRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileBorrowRecordDto> page = new Page<SysFileBorrowRecordDto>();
        for ( SysFileBorrowRecordPo sysFileBorrowRecordPo : arg0 ) {
            page.add( mapperDto( sysFileBorrowRecordPo ) );
        }

        return page;
    }

    @Override
    public SysFileBorrowRecordPo mapperPo(SysFileBorrowRecordDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileBorrowRecordPo sysFileBorrowRecordPo = new SysFileBorrowRecordPo();

        sysFileBorrowRecordPo.setId( arg0.getId() );
        sysFileBorrowRecordPo.setSourceName( arg0.getSourceName() );
        sysFileBorrowRecordPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileBorrowRecordPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileBorrowRecordPo.setName( arg0.getName() );
        sysFileBorrowRecordPo.setStatus( arg0.getStatus() );
        sysFileBorrowRecordPo.setSort( arg0.getSort() );
        sysFileBorrowRecordPo.setRemark( arg0.getRemark() );
        sysFileBorrowRecordPo.setCreateBy( arg0.getCreateBy() );
        sysFileBorrowRecordPo.setCreateTime( arg0.getCreateTime() );
        sysFileBorrowRecordPo.setUpdateBy( arg0.getUpdateBy() );
        sysFileBorrowRecordPo.setUpdateTime( arg0.getUpdateTime() );
        sysFileBorrowRecordPo.setDelFlag( arg0.getDelFlag() );
        sysFileBorrowRecordPo.setCreateName( arg0.getCreateName() );
        sysFileBorrowRecordPo.setUpdateName( arg0.getUpdateName() );
        sysFileBorrowRecordPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileBorrowRecordPo.setFileId( arg0.getFileId() );
        sysFileBorrowRecordPo.setBorrowUserId( arg0.getBorrowUserId() );
        sysFileBorrowRecordPo.setStartTime( arg0.getStartTime() );
        sysFileBorrowRecordPo.setEndTime( arg0.getEndTime() );
        sysFileBorrowRecordPo.setIsView( arg0.getIsView() );
        sysFileBorrowRecordPo.setIsDownload( arg0.getIsDownload() );

        return sysFileBorrowRecordPo;
    }

    @Override
    public List<SysFileBorrowRecordPo> mapperPo(Collection<SysFileBorrowRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileBorrowRecordPo> list = new ArrayList<SysFileBorrowRecordPo>( arg0.size() );
        for ( SysFileBorrowRecordDto sysFileBorrowRecordDto : arg0 ) {
            list.add( mapperPo( sysFileBorrowRecordDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFileBorrowRecordPo> mapperPagePo(Collection<SysFileBorrowRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileBorrowRecordPo> page = new Page<SysFileBorrowRecordPo>();
        for ( SysFileBorrowRecordDto sysFileBorrowRecordDto : arg0 ) {
            page.add( mapperPo( sysFileBorrowRecordDto ) );
        }

        return page;
    }
}
