package com.heju.system.report.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.report.domain.dto.SysTaxReportDto;
import com.heju.system.report.domain.po.SysTaxReportPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:14+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysTaxReportConverterImpl implements SysTaxReportConverter {

    @Override
    public SysTaxReportDto mapperDto(SysTaxReportPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysTaxReportDto sysTaxReportDto = new SysTaxReportDto();

        sysTaxReportDto.setId( arg0.getId() );
        sysTaxReportDto.setSourceName( arg0.getSourceName() );
        sysTaxReportDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysTaxReportDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysTaxReportDto.setName( arg0.getName() );
        sysTaxReportDto.setStatus( arg0.getStatus() );
        sysTaxReportDto.setSort( arg0.getSort() );
        sysTaxReportDto.setCreateBy( arg0.getCreateBy() );
        sysTaxReportDto.setCreateTime( arg0.getCreateTime() );
        sysTaxReportDto.setUpdateBy( arg0.getUpdateBy() );
        sysTaxReportDto.setUpdateTime( arg0.getUpdateTime() );
        sysTaxReportDto.setDelFlag( arg0.getDelFlag() );
        sysTaxReportDto.setCreateName( arg0.getCreateName() );
        sysTaxReportDto.setUpdateName( arg0.getUpdateName() );
        sysTaxReportDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysTaxReportDto.setEntityId( arg0.getEntityId() );
        sysTaxReportDto.setEntityName( arg0.getEntityName() );
        sysTaxReportDto.setCode( arg0.getCode() );
        sysTaxReportDto.setTaxType( arg0.getTaxType() );
        sysTaxReportDto.setReporttimeType( arg0.getReporttimeType() );
        sysTaxReportDto.setYear( arg0.getYear() );
        sysTaxReportDto.setMonth( arg0.getMonth() );
        sysTaxReportDto.setSeason( arg0.getSeason() );
        sysTaxReportDto.setReportAddress( arg0.getReportAddress() );
        sysTaxReportDto.setRemark( arg0.getRemark() );

        return sysTaxReportDto;
    }

    @Override
    public List<SysTaxReportDto> mapperDto(Collection<SysTaxReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysTaxReportDto> list = new ArrayList<SysTaxReportDto>( arg0.size() );
        for ( SysTaxReportPo sysTaxReportPo : arg0 ) {
            list.add( mapperDto( sysTaxReportPo ) );
        }

        return list;
    }

    @Override
    public Page<SysTaxReportDto> mapperPageDto(Collection<SysTaxReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysTaxReportDto> page = new Page<SysTaxReportDto>();
        for ( SysTaxReportPo sysTaxReportPo : arg0 ) {
            page.add( mapperDto( sysTaxReportPo ) );
        }

        return page;
    }

    @Override
    public SysTaxReportPo mapperPo(SysTaxReportDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysTaxReportPo sysTaxReportPo = new SysTaxReportPo();

        sysTaxReportPo.setId( arg0.getId() );
        sysTaxReportPo.setSourceName( arg0.getSourceName() );
        sysTaxReportPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysTaxReportPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysTaxReportPo.setName( arg0.getName() );
        sysTaxReportPo.setStatus( arg0.getStatus() );
        sysTaxReportPo.setSort( arg0.getSort() );
        sysTaxReportPo.setCreateBy( arg0.getCreateBy() );
        sysTaxReportPo.setCreateTime( arg0.getCreateTime() );
        sysTaxReportPo.setUpdateBy( arg0.getUpdateBy() );
        sysTaxReportPo.setUpdateTime( arg0.getUpdateTime() );
        sysTaxReportPo.setDelFlag( arg0.getDelFlag() );
        sysTaxReportPo.setCreateName( arg0.getCreateName() );
        sysTaxReportPo.setUpdateName( arg0.getUpdateName() );
        sysTaxReportPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysTaxReportPo.setEntityId( arg0.getEntityId() );
        sysTaxReportPo.setEntityName( arg0.getEntityName() );
        sysTaxReportPo.setCode( arg0.getCode() );
        sysTaxReportPo.setTaxType( arg0.getTaxType() );
        sysTaxReportPo.setReporttimeType( arg0.getReporttimeType() );
        sysTaxReportPo.setYear( arg0.getYear() );
        sysTaxReportPo.setMonth( arg0.getMonth() );
        sysTaxReportPo.setSeason( arg0.getSeason() );
        sysTaxReportPo.setReportAddress( arg0.getReportAddress() );
        sysTaxReportPo.setRemark( arg0.getRemark() );

        return sysTaxReportPo;
    }

    @Override
    public List<SysTaxReportPo> mapperPo(Collection<SysTaxReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysTaxReportPo> list = new ArrayList<SysTaxReportPo>( arg0.size() );
        for ( SysTaxReportDto sysTaxReportDto : arg0 ) {
            list.add( mapperPo( sysTaxReportDto ) );
        }

        return list;
    }

    @Override
    public Page<SysTaxReportPo> mapperPagePo(Collection<SysTaxReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysTaxReportPo> page = new Page<SysTaxReportPo>();
        for ( SysTaxReportDto sysTaxReportDto : arg0 ) {
            page.add( mapperPo( sysTaxReportDto ) );
        }

        return page;
    }
}
