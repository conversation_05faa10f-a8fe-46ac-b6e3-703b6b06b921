package com.heju.system.report.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.report.domain.dto.SysBankReportDto;
import com.heju.system.report.domain.po.SysBankReportPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:15+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysBankReportConverterImpl implements SysBankReportConverter {

    @Override
    public SysBankReportDto mapperDto(SysBankReportPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysBankReportDto sysBankReportDto = new SysBankReportDto();

        sysBankReportDto.setId( arg0.getId() );
        sysBankReportDto.setSourceName( arg0.getSourceName() );
        sysBankReportDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysBankReportDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysBankReportDto.setName( arg0.getName() );
        sysBankReportDto.setStatus( arg0.getStatus() );
        sysBankReportDto.setSort( arg0.getSort() );
        sysBankReportDto.setCreateBy( arg0.getCreateBy() );
        sysBankReportDto.setCreateTime( arg0.getCreateTime() );
        sysBankReportDto.setUpdateBy( arg0.getUpdateBy() );
        sysBankReportDto.setUpdateTime( arg0.getUpdateTime() );
        sysBankReportDto.setDelFlag( arg0.getDelFlag() );
        sysBankReportDto.setCreateName( arg0.getCreateName() );
        sysBankReportDto.setUpdateName( arg0.getUpdateName() );
        sysBankReportDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysBankReportDto.setEntityId( arg0.getEntityId() );
        sysBankReportDto.setEntityName( arg0.getEntityName() );
        sysBankReportDto.setCode( arg0.getCode() );
        sysBankReportDto.setReporttypeType( arg0.getReporttypeType() );
        sysBankReportDto.setReporttimeType( arg0.getReporttimeType() );
        sysBankReportDto.setYear( arg0.getYear() );
        sysBankReportDto.setMonth( arg0.getMonth() );
        sysBankReportDto.setSeason( arg0.getSeason() );
        sysBankReportDto.setReportAddress( arg0.getReportAddress() );
        sysBankReportDto.setRemark( arg0.getRemark() );

        return sysBankReportDto;
    }

    @Override
    public List<SysBankReportDto> mapperDto(Collection<SysBankReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysBankReportDto> list = new ArrayList<SysBankReportDto>( arg0.size() );
        for ( SysBankReportPo sysBankReportPo : arg0 ) {
            list.add( mapperDto( sysBankReportPo ) );
        }

        return list;
    }

    @Override
    public Page<SysBankReportDto> mapperPageDto(Collection<SysBankReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysBankReportDto> page = new Page<SysBankReportDto>();
        for ( SysBankReportPo sysBankReportPo : arg0 ) {
            page.add( mapperDto( sysBankReportPo ) );
        }

        return page;
    }

    @Override
    public SysBankReportPo mapperPo(SysBankReportDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysBankReportPo sysBankReportPo = new SysBankReportPo();

        sysBankReportPo.setId( arg0.getId() );
        sysBankReportPo.setSourceName( arg0.getSourceName() );
        sysBankReportPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysBankReportPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysBankReportPo.setName( arg0.getName() );
        sysBankReportPo.setStatus( arg0.getStatus() );
        sysBankReportPo.setSort( arg0.getSort() );
        sysBankReportPo.setCreateBy( arg0.getCreateBy() );
        sysBankReportPo.setCreateTime( arg0.getCreateTime() );
        sysBankReportPo.setUpdateBy( arg0.getUpdateBy() );
        sysBankReportPo.setUpdateTime( arg0.getUpdateTime() );
        sysBankReportPo.setDelFlag( arg0.getDelFlag() );
        sysBankReportPo.setCreateName( arg0.getCreateName() );
        sysBankReportPo.setUpdateName( arg0.getUpdateName() );
        sysBankReportPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysBankReportPo.setEntityId( arg0.getEntityId() );
        sysBankReportPo.setEntityName( arg0.getEntityName() );
        sysBankReportPo.setCode( arg0.getCode() );
        sysBankReportPo.setReporttypeType( arg0.getReporttypeType() );
        sysBankReportPo.setReporttimeType( arg0.getReporttimeType() );
        sysBankReportPo.setYear( arg0.getYear() );
        sysBankReportPo.setMonth( arg0.getMonth() );
        sysBankReportPo.setSeason( arg0.getSeason() );
        sysBankReportPo.setReportAddress( arg0.getReportAddress() );
        sysBankReportPo.setRemark( arg0.getRemark() );

        return sysBankReportPo;
    }

    @Override
    public List<SysBankReportPo> mapperPo(Collection<SysBankReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysBankReportPo> list = new ArrayList<SysBankReportPo>( arg0.size() );
        for ( SysBankReportDto sysBankReportDto : arg0 ) {
            list.add( mapperPo( sysBankReportDto ) );
        }

        return list;
    }

    @Override
    public Page<SysBankReportPo> mapperPagePo(Collection<SysBankReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysBankReportPo> page = new Page<SysBankReportPo>();
        for ( SysBankReportDto sysBankReportDto : arg0 ) {
            page.add( mapperPo( sysBankReportDto ) );
        }

        return page;
    }
}
