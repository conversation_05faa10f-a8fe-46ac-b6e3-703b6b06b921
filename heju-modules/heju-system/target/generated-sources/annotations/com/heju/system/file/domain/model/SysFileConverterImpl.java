package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFileDto;
import com.heju.system.file.domain.po.SysFilePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:14+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFileConverterImpl implements SysFileConverter {

    @Override
    public SysFileDto mapperDto(SysFilePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileDto sysFileDto = new SysFileDto();

        sysFileDto.setId( arg0.getId() );
        sysFileDto.setSourceName( arg0.getSourceName() );
        sysFileDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileDto.setName( arg0.getName() );
        sysFileDto.setStatus( arg0.getStatus() );
        sysFileDto.setSort( arg0.getSort() );
        sysFileDto.setRemark( arg0.getRemark() );
        sysFileDto.setCreateBy( arg0.getCreateBy() );
        sysFileDto.setCreateTime( arg0.getCreateTime() );
        sysFileDto.setUpdateBy( arg0.getUpdateBy() );
        sysFileDto.setUpdateTime( arg0.getUpdateTime() );
        sysFileDto.setDelFlag( arg0.getDelFlag() );
        sysFileDto.setCreateName( arg0.getCreateName() );
        sysFileDto.setUpdateName( arg0.getUpdateName() );
        sysFileDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileDto.setFolderId( arg0.getFolderId() );
        sysFileDto.setNick( arg0.getNick() );
        sysFileDto.setUrl( arg0.getUrl() );
        sysFileDto.setSize( arg0.getSize() );
        sysFileDto.setType( arg0.getType() );

        return sysFileDto;
    }

    @Override
    public List<SysFileDto> mapperDto(Collection<SysFilePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileDto> list = new ArrayList<SysFileDto>( arg0.size() );
        for ( SysFilePo sysFilePo : arg0 ) {
            list.add( mapperDto( sysFilePo ) );
        }

        return list;
    }

    @Override
    public Page<SysFileDto> mapperPageDto(Collection<SysFilePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileDto> page = new Page<SysFileDto>();
        for ( SysFilePo sysFilePo : arg0 ) {
            page.add( mapperDto( sysFilePo ) );
        }

        return page;
    }

    @Override
    public SysFilePo mapperPo(SysFileDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFilePo sysFilePo = new SysFilePo();

        sysFilePo.setId( arg0.getId() );
        sysFilePo.setSourceName( arg0.getSourceName() );
        sysFilePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFilePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFilePo.setName( arg0.getName() );
        sysFilePo.setStatus( arg0.getStatus() );
        sysFilePo.setSort( arg0.getSort() );
        sysFilePo.setRemark( arg0.getRemark() );
        sysFilePo.setCreateBy( arg0.getCreateBy() );
        sysFilePo.setCreateTime( arg0.getCreateTime() );
        sysFilePo.setUpdateBy( arg0.getUpdateBy() );
        sysFilePo.setUpdateTime( arg0.getUpdateTime() );
        sysFilePo.setDelFlag( arg0.getDelFlag() );
        sysFilePo.setCreateName( arg0.getCreateName() );
        sysFilePo.setUpdateName( arg0.getUpdateName() );
        sysFilePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFilePo.setFolderId( arg0.getFolderId() );
        sysFilePo.setNick( arg0.getNick() );
        sysFilePo.setUrl( arg0.getUrl() );
        sysFilePo.setSize( arg0.getSize() );
        sysFilePo.setType( arg0.getType() );

        return sysFilePo;
    }

    @Override
    public List<SysFilePo> mapperPo(Collection<SysFileDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFilePo> list = new ArrayList<SysFilePo>( arg0.size() );
        for ( SysFileDto sysFileDto : arg0 ) {
            list.add( mapperPo( sysFileDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFilePo> mapperPagePo(Collection<SysFileDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFilePo> page = new Page<SysFilePo>();
        for ( SysFileDto sysFileDto : arg0 ) {
            page.add( mapperPo( sysFileDto ) );
        }

        return page;
    }
}
