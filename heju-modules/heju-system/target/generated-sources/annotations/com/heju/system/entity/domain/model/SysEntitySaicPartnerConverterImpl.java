package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntitySaicPartnerDto;
import com.heju.system.entity.domain.po.SysEntitySaicPartnerPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:15+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntitySaicPartnerConverterImpl implements SysEntitySaicPartnerConverter {

    @Override
    public SysEntitySaicPartnerDto mapperDto(SysEntitySaicPartnerPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicPartnerDto sysEntitySaicPartnerDto = new SysEntitySaicPartnerDto();

        sysEntitySaicPartnerDto.setId( arg0.getId() );
        sysEntitySaicPartnerDto.setSourceName( arg0.getSourceName() );
        sysEntitySaicPartnerDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicPartnerDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicPartnerDto.setName( arg0.getName() );
        sysEntitySaicPartnerDto.setStatus( arg0.getStatus() );
        sysEntitySaicPartnerDto.setSort( arg0.getSort() );
        sysEntitySaicPartnerDto.setRemark( arg0.getRemark() );
        sysEntitySaicPartnerDto.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicPartnerDto.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicPartnerDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicPartnerDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicPartnerDto.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicPartnerDto.setCreateName( arg0.getCreateName() );
        sysEntitySaicPartnerDto.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicPartnerDto.setTotalRealCapital( arg0.getTotalRealCapital() );
        sysEntitySaicPartnerDto.setPartnerType( arg0.getPartnerType() );
        sysEntitySaicPartnerDto.setTotalShouldCapital( arg0.getTotalShouldCapital() );
        sysEntitySaicPartnerDto.setPercent( arg0.getPercent() );
        sysEntitySaicPartnerDto.setPartnerName( arg0.getPartnerName() );
        sysEntitySaicPartnerDto.setEntityId( arg0.getEntityId() );

        return sysEntitySaicPartnerDto;
    }

    @Override
    public List<SysEntitySaicPartnerDto> mapperDto(Collection<SysEntitySaicPartnerPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicPartnerDto> list = new ArrayList<SysEntitySaicPartnerDto>( arg0.size() );
        for ( SysEntitySaicPartnerPo sysEntitySaicPartnerPo : arg0 ) {
            list.add( mapperDto( sysEntitySaicPartnerPo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicPartnerDto> mapperPageDto(Collection<SysEntitySaicPartnerPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicPartnerDto> page = new Page<SysEntitySaicPartnerDto>();
        for ( SysEntitySaicPartnerPo sysEntitySaicPartnerPo : arg0 ) {
            page.add( mapperDto( sysEntitySaicPartnerPo ) );
        }

        return page;
    }

    @Override
    public SysEntitySaicPartnerPo mapperPo(SysEntitySaicPartnerDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicPartnerPo sysEntitySaicPartnerPo = new SysEntitySaicPartnerPo();

        sysEntitySaicPartnerPo.setId( arg0.getId() );
        sysEntitySaicPartnerPo.setSourceName( arg0.getSourceName() );
        sysEntitySaicPartnerPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicPartnerPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicPartnerPo.setName( arg0.getName() );
        sysEntitySaicPartnerPo.setStatus( arg0.getStatus() );
        sysEntitySaicPartnerPo.setSort( arg0.getSort() );
        sysEntitySaicPartnerPo.setRemark( arg0.getRemark() );
        sysEntitySaicPartnerPo.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicPartnerPo.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicPartnerPo.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicPartnerPo.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicPartnerPo.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicPartnerPo.setCreateName( arg0.getCreateName() );
        sysEntitySaicPartnerPo.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicPartnerPo.setTotalRealCapital( arg0.getTotalRealCapital() );
        sysEntitySaicPartnerPo.setPartnerType( arg0.getPartnerType() );
        sysEntitySaicPartnerPo.setTotalShouldCapital( arg0.getTotalShouldCapital() );
        sysEntitySaicPartnerPo.setPercent( arg0.getPercent() );
        sysEntitySaicPartnerPo.setPartnerName( arg0.getPartnerName() );
        sysEntitySaicPartnerPo.setEntityId( arg0.getEntityId() );

        return sysEntitySaicPartnerPo;
    }

    @Override
    public List<SysEntitySaicPartnerPo> mapperPo(Collection<SysEntitySaicPartnerDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicPartnerPo> list = new ArrayList<SysEntitySaicPartnerPo>( arg0.size() );
        for ( SysEntitySaicPartnerDto sysEntitySaicPartnerDto : arg0 ) {
            list.add( mapperPo( sysEntitySaicPartnerDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicPartnerPo> mapperPagePo(Collection<SysEntitySaicPartnerDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicPartnerPo> page = new Page<SysEntitySaicPartnerPo>();
        for ( SysEntitySaicPartnerDto sysEntitySaicPartnerDto : arg0 ) {
            page.add( mapperPo( sysEntitySaicPartnerDto ) );
        }

        return page;
    }
}
