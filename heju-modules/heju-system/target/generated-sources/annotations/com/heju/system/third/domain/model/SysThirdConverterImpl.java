package com.heju.system.third.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.third.domain.dto.SysThirdDto;
import com.heju.system.third.domain.po.SysThirdPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:06:14+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysThirdConverterImpl implements SysThirdConverter {

    @Override
    public SysThirdDto mapperDto(SysThirdPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysThirdDto sysThirdDto = new SysThirdDto();

        sysThirdDto.setId( arg0.getId() );
        sysThirdDto.setSourceName( arg0.getSourceName() );
        sysThirdDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysThirdDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysThirdDto.setName( arg0.getName() );
        sysThirdDto.setStatus( arg0.getStatus() );
        sysThirdDto.setSort( arg0.getSort() );
        sysThirdDto.setCreateBy( arg0.getCreateBy() );
        sysThirdDto.setCreateTime( arg0.getCreateTime() );
        sysThirdDto.setUpdateBy( arg0.getUpdateBy() );
        sysThirdDto.setUpdateTime( arg0.getUpdateTime() );
        sysThirdDto.setDelFlag( arg0.getDelFlag() );
        sysThirdDto.setCreateName( arg0.getCreateName() );
        sysThirdDto.setUpdateName( arg0.getUpdateName() );
        sysThirdDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysThirdDto.setCode( arg0.getCode() );
        sysThirdDto.setLogo( arg0.getLogo() );
        sysThirdDto.setAuthStr( arg0.getAuthStr() );
        sysThirdDto.setRemark( arg0.getRemark() );

        return sysThirdDto;
    }

    @Override
    public List<SysThirdDto> mapperDto(Collection<SysThirdPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysThirdDto> list = new ArrayList<SysThirdDto>( arg0.size() );
        for ( SysThirdPo sysThirdPo : arg0 ) {
            list.add( mapperDto( sysThirdPo ) );
        }

        return list;
    }

    @Override
    public Page<SysThirdDto> mapperPageDto(Collection<SysThirdPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysThirdDto> page = new Page<SysThirdDto>();
        for ( SysThirdPo sysThirdPo : arg0 ) {
            page.add( mapperDto( sysThirdPo ) );
        }

        return page;
    }

    @Override
    public SysThirdPo mapperPo(SysThirdDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysThirdPo sysThirdPo = new SysThirdPo();

        sysThirdPo.setId( arg0.getId() );
        sysThirdPo.setSourceName( arg0.getSourceName() );
        sysThirdPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysThirdPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysThirdPo.setName( arg0.getName() );
        sysThirdPo.setStatus( arg0.getStatus() );
        sysThirdPo.setSort( arg0.getSort() );
        sysThirdPo.setCreateBy( arg0.getCreateBy() );
        sysThirdPo.setCreateTime( arg0.getCreateTime() );
        sysThirdPo.setUpdateBy( arg0.getUpdateBy() );
        sysThirdPo.setUpdateTime( arg0.getUpdateTime() );
        sysThirdPo.setDelFlag( arg0.getDelFlag() );
        sysThirdPo.setCreateName( arg0.getCreateName() );
        sysThirdPo.setUpdateName( arg0.getUpdateName() );
        sysThirdPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysThirdPo.setCode( arg0.getCode() );
        sysThirdPo.setLogo( arg0.getLogo() );
        sysThirdPo.setAuthStr( arg0.getAuthStr() );
        sysThirdPo.setRemark( arg0.getRemark() );

        return sysThirdPo;
    }

    @Override
    public List<SysThirdPo> mapperPo(Collection<SysThirdDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysThirdPo> list = new ArrayList<SysThirdPo>( arg0.size() );
        for ( SysThirdDto sysThirdDto : arg0 ) {
            list.add( mapperPo( sysThirdDto ) );
        }

        return list;
    }

    @Override
    public Page<SysThirdPo> mapperPagePo(Collection<SysThirdDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysThirdPo> page = new Page<SysThirdPo>();
        for ( SysThirdDto sysThirdDto : arg0 ) {
            page.add( mapperPo( sysThirdDto ) );
        }

        return page;
    }
}
