package com.heju.flowable;

import com.heju.common.security.annotation.EnableCustomConfig;
import com.heju.common.security.annotation.EnableRyFeignClients;
import com.heju.common.swagger.annotation.EnableCustomSwagger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;

import javax.annotation.PostConstruct;

/**
 * 流程引擎模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger
@EnableRyFeignClients
@SpringBootApplication(excludeName = {
        "org.flowable.spring.boot.process.ProcessEngineAutoConfiguration",
        "org.flowable.spring.boot.ProcessEngineServicesAutoConfiguration"
})
public class HeJuFlowableApplication {
    public static void main(String[] args) {
        SpringApplication.run(HeJuFlowableApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  流程模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " ('-. .-.          \n" +
                "( OO )  /          \n" +
                ",--. ,--.     ,--. \n" +
                "|  | |  | .-')| ,| \n" +
                "|   .|  |( OO |(_| \n" +
                "|       || `-'|  | \n" +
                "|  .-.  |,--. |  | \n" +
                "|  | |  ||  '-'  / \n" +
                "`--' `--' `-----'  \n");
    }

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void checkBean() {
        boolean exists = applicationContext.containsBean("multiInstanceHandler");
        System.out.println("multiInstanceHandler bean exists: " + exists);
    }
}
