package com.heju.flowable.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SimpleQuery;
import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.flowable.common.constant.ProcessConstants;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.authority.domain.po.SysOrganizeRoleMerge;
import com.heju.system.api.authority.domain.po.SysUserPostMerge;
import com.heju.system.api.organize.domain.po.SysPostPo;
import com.heju.system.api.organize.domain.po.SysUserPo;
import lombok.AllArgsConstructor;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.boot.actuate.endpoint.SecurityContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 多实例处理类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component("multiInstanceHandler")
public class MultiInstanceHandler {


    public Set<String> getUserIds(DelegateExecution execution) {
        Set<String> candidateUserIds = new LinkedHashSet<>();
        FlowElement flowElement = execution.getCurrentFlowElement();
        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            String dataType = userTask.getAttributeValue(ProcessConstants.NAMASPASE, ProcessConstants.PROCESS_CUSTOM_DATA_TYPE);
            if ("USERS".equals(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
                // 添加候选用户id
                candidateUserIds.addAll(userTask.getCandidateUsers());
            } else if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
                // 获取组的ID，角色ID集合或部门ID集合
                List<Long> groups = userTask.getCandidateGroups().stream()
                    .map(item -> Long.parseLong(item.substring(4)))
                    .collect(Collectors.toList());
                List<Long> userIds = new ArrayList<>();
                SecurityContextHolder.setSourceName(SecurityUtils.getSourceName());
                if ("ROLES".equals(dataType)) {
                    // 通过角色id，获取所有用户id集合
                    LambdaQueryWrapper<SysOrganizeRoleMerge> lqw = Wrappers.lambdaQuery(SysOrganizeRoleMerge.class).select(SysOrganizeRoleMerge::getUserId).in(SysOrganizeRoleMerge::getRoleId, groups);
                    userIds = SimpleQuery.list(lqw, SysOrganizeRoleMerge::getUserId);
                } else if ("DEPTS".equals(dataType)) {
                    // 通过部门id，获取所有用户id集合
                    LambdaQueryWrapper<SysPostPo> lqw=Wrappers.lambdaQuery(SysPostPo.class).select(SysPostPo::getId).in(SysPostPo::getDeptId,groups);
                    List<Long> postIds = SimpleQuery.list(lqw, SysPostPo::getId);
                    LambdaQueryWrapper<SysUserPostMerge> lqwPost=Wrappers.lambdaQuery(SysUserPostMerge.class).select(SysUserPostMerge::getUserId).in(SysUserPostMerge::getPostId,postIds);
                    userIds = SimpleQuery.list(lqwPost, SysUserPostMerge::getUserId);
                }
                // 添加候选用户id
                userIds.forEach(id -> candidateUserIds.add(String.valueOf(id)));
            }
        }
        return candidateUserIds;
    }
}
