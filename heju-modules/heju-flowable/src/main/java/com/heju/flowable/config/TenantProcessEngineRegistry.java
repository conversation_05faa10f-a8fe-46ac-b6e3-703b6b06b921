package com.heju.flowable.config;

import com.heju.flowable.handler.MultiInstanceHandler;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class TenantProcessEngineRegistry {
    private static final Map<String, ProcessEngine> ENGINE_MAP = new ConcurrentHashMap<>();

    private static ApplicationContext staticApplicationContext;
    private static MultiInstanceHandler staticMultiInstanceHandler;

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        TenantProcessEngineRegistry.staticApplicationContext = applicationContext;
    }

    @Autowired
    public void setMultiInstanceHandler(MultiInstanceHandler multiInstanceHandler) {
        TenantProcessEngineRegistry.staticMultiInstanceHandler = multiInstanceHandler;
    }

    public static ProcessEngine getEngine(String slave) {

        return ENGINE_MAP.get(slave);
    }
    public void registerTenant(String slave, DataSource dataSource) {
        if (ENGINE_MAP.containsKey(slave)) {
            return; // 已注册，跳过
        }
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(dataSource);
        config.setTransactionManager(new DataSourceTransactionManager(dataSource));
        config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE);
        // 支持 Spring Bean 注入表达式如 ${multiInstanceHandler.getUserIds(execution)}
        config.setApplicationContext(staticApplicationContext);
        config.setAsyncExecutorActivate(false);
        Map<Object, Object> beans = new HashMap<>();
        beans.put("multiInstanceHandler", staticMultiInstanceHandler);
//        Map<String, Object> beansOfType = applicationContext.getBeansOfType(Object.class);
//        for (Map.Entry<String, Object> mapEntry : beansOfType.entrySet()) {
//            beans.put(mapEntry.getKey(),mapEntry.getValue());
//        }
        config.setBeans(beans);
//        config.setExpressionManager(new SpringExpressionManager())
        // 防止中文乱码
        config.setActivityFontName("宋体");
        config.setLabelFontName("宋体");
        config.setAnnotationFontName("宋体");
        System.out.println("----------------"+config.getBeans());
        ProcessEngine engine = config.buildProcessEngine();
        ENGINE_MAP.put(slave, engine);
    }
}
