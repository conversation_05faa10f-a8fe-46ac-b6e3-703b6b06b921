package com.heju.gen.service.impl;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.job.ScheduleConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.gen.domain.dto.GenTableColumnDto;
import com.heju.gen.domain.query.GenTableColumnQuery;
import com.heju.gen.manager.impl.GenTableColumnManagerImpl;
import com.heju.gen.service.IGenTableColumnService;
import com.heju.tenant.api.tenant.feign.RemoteTenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务字段管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class GenTableColumnServiceImpl extends BaseServiceImpl<GenTableColumnQuery, GenTableColumnDto, GenTableColumnManagerImpl> implements IGenTableColumnService {

    @Autowired
    private RemoteTenantService remoteTenantService;

    /**
     * 根据表名称查询数据库表列信息
     *
     * @param tableName 表名称
     * @return 数据库表列信息
     */
    @Override
    public List<GenTableColumnDto> selectDbTableColumnsByName(String tableName) {
        return baseManager.selectDbTableColumnsByName(tableName);
    }

    @Override
    public AjaxResult updateDatabase(String sql) {
        System.out.println(sql);
        return remoteTenantService.updateTheDatabase(sql, SecurityConstants.INNER);
    }
}