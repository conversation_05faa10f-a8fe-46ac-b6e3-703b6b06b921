package com.heju.gen.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.gen.domain.dto.GenTableColumnDto;
import com.heju.gen.domain.query.GenTableColumnQuery;
import com.heju.tenant.api.tenant.feign.RemoteTenantService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 业务字段管理 服务层
 *
 * <AUTHOR>
 */
public interface IGenTableColumnService extends IBaseService<GenTableColumnQuery, GenTableColumnDto> {

    /**
     * 根据表名称查询数据库表列信息
     *
     * @param tableName 表名称
     * @return 数据库表列信息
     */
    List<GenTableColumnDto> selectDbTableColumnsByName(String tableName);

    AjaxResult updateDatabase(String sql);
}