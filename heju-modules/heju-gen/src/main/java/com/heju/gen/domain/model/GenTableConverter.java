package com.heju.gen.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.gen.domain.dto.GenTableDto;
import com.heju.gen.domain.po.GenTablePo;
import com.heju.gen.domain.query.GenTableQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 业务 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface GenTableConverter extends BaseConverter<GenTableQuery, GenTableDto, GenTablePo> {
}