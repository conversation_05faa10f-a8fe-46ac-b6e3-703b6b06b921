package com.heju.gen.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.gen.domain.dto.GenTableColumnDto;
import com.heju.gen.domain.po.GenTableColumnPo;
import com.heju.gen.domain.query.GenTableColumnQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 业务字段 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface GenTableColumnConverter extends BaseConverter<GenTableColumnQuery, GenTableColumnDto, GenTableColumnPo> {
}