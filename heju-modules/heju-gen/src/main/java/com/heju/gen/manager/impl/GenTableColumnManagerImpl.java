package com.heju.gen.manager.impl;

import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.gen.domain.dto.GenTableColumnDto;
import com.heju.gen.domain.model.GenTableColumnConverter;
import com.heju.gen.domain.po.GenTableColumnPo;
import com.heju.gen.domain.query.GenTableColumnQuery;
import com.heju.gen.manager.IGenTableColumnManager;
import com.heju.gen.mapper.GenTableColumnMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 业务字段管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class GenTableColumnManagerImpl extends BaseManagerImpl<GenTableColumnQuery, GenTableColumnDto, GenTableColumnPo, GenTableColumnMapper, GenTableColumnConverter> implements IGenTableColumnManager {

    /**
     * 根据表名称查询数据库表列信息
     *
     * @param tableName 表名称
     * @return 数据库表列信息
     */
    @Override
    public List<GenTableColumnDto> selectDbTableColumnsByName(String tableName) {
        return baseMapper.selectDbTableColumnsByName(tableName);
    }
}
