package com.heju.file.controller;

import cn.hutool.core.io.resource.InputStreamResource;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.TenantConstants;
import com.heju.common.core.utils.file.FileUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.file.api.domain.SysFile;
import com.heju.file.api.feign.RemoteFileManageService;
import com.heju.file.service.ISysFileService;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.feign.RemoteTenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SysFileController {

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private RemoteFileManageService remoteFileManageService;

    @Autowired
    private RemoteTenantService remoteTenantService;

    /**
     * 文件上传 | 内部调用
     */
    @PostMapping("/inner/upload")
    public R<SysFile> uploadInner(MultipartFile file) {
        try {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setUrl(url);
            sysFile.setSize(file.getSize());
            sysFile.setName(FileUtil.getName(url));
            sysFile.setNick(sysFile.getName());
            remoteFileManageService.saveFileLog(sysFile, SecurityConstants.INNER);
            return R.ok(sysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }


    /**
     * 删除文件 | 内部调用
     */
    @DeleteMapping(value = "/inner/delete/{url}")
    public R<Boolean> deleteInner(@PathVariable String url) {
        try {
            Boolean result = sysFileService.deleteFile(url);
            return R.ok(result);
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 文件上传请求
     */
    @PostMapping("/upload")
    public AjaxResult upload(MultipartFile file) {
        R<SysFile> R = uploadInner(file);
        return R.isOk()
                ? AjaxResult.success("上传成功！", R.getData().getUrl())
                : AjaxResult.error("上传失败！");
    }

    /**
     * 文件上传/更新建表sql | 内部调用
     */
    @InnerAuth
    @PostMapping("/inner/uploadSQLFile")
    public R<SysFile> uploadSQLFile(MultipartFile file) {
        //SysFile tempFile = sysFileService.selectByFileName(fileName);
        try {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setUrl(url);
            sysFile.setSize(file.getSize());
            sysFile.setName(FileUtil.getName(url));
            if (file.getName().contains("version_updates")) {
                R<Integer> databaseVersion = remoteTenantService.getDatabaseVersion(SecurityConstants.INNER);
                Integer data = databaseVersion.getData();
                if (data == null) {
                    data = 0;
                }
                sysFile.setNick("version_updates_1." + data + 1);
            }
            remoteFileManageService.saveFileLog(sysFile, SecurityConstants.INNER);
            return R.ok(sysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }
}