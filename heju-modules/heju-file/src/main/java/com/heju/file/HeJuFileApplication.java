package com.heju.file;

import com.heju.common.security.annotation.EnableRyFeignClients;
import com.heju.common.security.config.ApplicationConfig;
import com.heju.common.security.config.JacksonConfig;
import com.heju.common.security.feign.FeignAutoConfiguration;
import com.heju.common.swagger.annotation.EnableCustomSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
@EnableCustomSwagger
@EnableRyFeignClients
@Import({ApplicationConfig.class, FeignAutoConfiguration.class, JacksonConfig.class})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class HeJuFileApplication {
    public static void main(String[] args) {
        SpringApplication.run(HeJuFileApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  文件服务模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " ('-. .-.          \n" +
                "( OO )  /          \n" +
                ",--. ,--.     ,--. \n" +
                "|  | |  | .-')| ,| \n" +
                "|   .|  |( OO |(_| \n" +
                "|       || `-'|  | \n" +
                "|  .-.  |,--. |  | \n" +
                "|  | |  ||  '-'  / \n" +
                "`--' `--' `-----'  \n");
    }
}
