version : '3.8'
services:
#  heju-nacos:
#    container_name: heju-nacos
#    image: nacos/nacos-server
#    build:
#      context: docker/nacos
#    environment:
#      - MODE=standalone
#    volumes:
#      - ./docker/nacos/logs/:/home/<USER>/logs
#      - ./docker/nacos/conf/application.properties:/home/<USER>/conf/application.properties
#    ports:
#      - "8848:8848"
#      - "9848:9848"
#      - "9849:9849"
#    depends_on:
#      - heju-mysql
#    links:
#      - heju-mysql
#  heju-mysql:
#    container_name: heju-mysql
#    image: mysql:8.0.26
#    build:
#      context: sql
#      dockerfile: Dockerfile
#    ports:
#      - "3306:3306"
#    volumes:
#      - ./docker/mysql/conf:/etc/mysql/conf.d
#      - ./docker/mysql/logs:/logs
#      - ./docker/mysql/data:/var/lib/mysql
#    command: [
#          'mysqld',
#          '--innodb-buffer-pool-size=80M',
#          '--character-set-server=utf8mb4',
#          '--collation-server=utf8mb4_unicode_ci',
#          '--default-time-zone=+8:00',
#          '--lower-case-table-names=1'
#        ]
#    environment:
#      MYSQL_DATABASE: 'xy-cloud'
#      MYSQL_ROOT_PASSWORD: password
#  heju-redis:
#    container_name: heju-redis
#    image: redis
#    build:
#      context: docker/redis
#    ports:
#      - "6379:6379"
#    volumes:
#      - ./docker/redis/conf/redis.conf:/home/<USER>/redis/redis.conf
#      - ./docker/redis/data:/data
#    command: redis-server /home/<USER>/redis/redis.conf
  heju-gateway:
    container_name: heju-gateway
    build:
      context: heju-gateway
      dockerfile: ./Dockerfile
    ports:
      - "8080:8080"
#    depends_on:
#      - heju-nacos
#      - heju-redis
#    links:
#      - heju-nacos
#      - heju-redis
  heju-auth:
    container_name: heju-auth
    build:
      context: heju-auth
      dockerfile: ./Dockerfile
    ports:
      - "9200:9200"
#    depends_on:
#      - heju-nacos
#      - heju-redis
#    links:
#      - heju-nacos
#      - heju-redis
  heju-modules-tenant:
    container_name: heju-modules-tenant
    build:
      context: heju-modules/heju-tenant
      dockerfile: ./Dockerfile
    ports:
      - "9700:9700"
#    depends_on:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
#    links:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
  heju-modules-system:
    container_name: heju-modules-system
    build:
      context: heju-modules/heju-system
      dockerfile: ./Dockerfile
    ports:
      - "9600:9600"
#    depends_on:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
#    links:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
  heju-modules-gen:
    container_name: heju-modules-gen
    build:
      context: heju-modules/heju-gen
      dockerfile: ./Dockerfile
    ports:
      - "9400:9400"
#    depends_on:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
#    links:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
  heju-modules-job:
    container_name: heju-modules-job
    build:
      context: heju-modules/heju-job
      dockerfile: ./Dockerfile
    ports:
      - "9500:9500"
#    depends_on:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
#    links:
#      - heju-nacos
#      - heju-redis
#      - heju-mysql
  heju-modules-file:
    container_name: heju-modules-file
    build:
      context: heju-modules/heju-file
      dockerfile: ./Dockerfile
    ports:
      - "9300:9300"
    volumes:
    - ./heju/uploadPath:/home/<USER>/uploadPath
  heju-visual-monitor:
    container_name: heju-visual-monitor
    build:
      context: heju-visual/heju-monitor
      dockerfile: ./Dockerfile
    ports:
      - "9100:9100"