<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.heju</groupId>
    <artifactId>heju</artifactId>
    <version>2.5.0</version>

    <name>heju</name>
    <url>https://hejutt.cn</url>
    <description>和聚管理系统</description>

    <properties>
        <heju.version>2.5.0</heju.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <java.version>17</java.version>
        <spring-boot.version>2.7.7</spring-boot.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.4.0</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <mybatis-plus.version>3.5.3</mybatis-plus.version>
        <pagehelper.version>5.3.2</pagehelper.version>
        <netty.version>4.1.67.Final</netty.version>
        <spring-doc.version>1.6.13</spring-doc.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <druid.version>1.2.16</druid.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson2.version>2.0.25</fastjson2.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <hutool.version>5.8.11</hutool.version>
        <jjwt.version>0.9.1</jjwt.version>
        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- Mybatis-Plus 依赖配置 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- pageHelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- mapstruct 转换器 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-core</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 通信模块 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-web</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-swagger</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-security</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-datascope</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-datasource</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-log</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 缓存管理 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-cache</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-redis</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-seata</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-api-system</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 租管接口 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-api-tenant</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 文件管理接口 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-api-file</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- 定时任务接口 -->
            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-api-job</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <dependency>
                <groupId>com.heju</groupId>
                <artifactId>heju-common-flowable</artifactId>
                <version>${heju.version}</version>
            </dependency>

            <!-- huTool 工具类库 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>heju-auth</module>
        <module>heju-gateway</module>
        <module>heju-visual</module>
        <module>heju-modules</module>
        <module>heju-api</module>
        <module>heju-common</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>

        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
